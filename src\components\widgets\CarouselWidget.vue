<script setup lang="ts">
import { computed } from 'vue'

// 定义组件属性
interface Props {
  images?: string
  autoplay?: boolean
  interval?: number
  showIndicators?: boolean
  showArrows?: boolean
  width?: string
  height?: string
  borderRadius?: string
  border?: string
  padding?: string
  margin?: string
  backgroundColor?: string
  display?: string
  position?: string
  top?: string
  left?: string
  right?: string
  bottom?: string
  zIndex?: string
  opacity?: string
  transform?: string
  transition?: string
  cursor?: string
  userSelect?: string
  overflow?: string
  boxShadow?: string
  [key: string]: any
}

const props = withDefaults(defineProps<Props>(), {
  images: '',
  autoplay: true,
  interval: 3,
  showIndicators: true,
  showArrows: true,
  width: '100%',
  height: '400px',
  borderRadius: '8px',
  border: 'none',
  padding: '0',
  margin: '0',
  backgroundColor: 'transparent',
  display: 'block',
  position: 'static',
  top: 'auto',
  left: 'auto',
  right: 'auto',
  bottom: 'auto',
  zIndex: 'auto',
  opacity: '1',
  transform: 'none',
  transition: 'none',
  cursor: 'default',
  userSelect: 'auto',
  overflow: 'hidden',
  boxShadow: 'none',
})

// 解析图片列表
const imageList = computed(() => {
  return props.images.split('\n').filter((img) => img.trim())
})

// 计算样式
const widgetStyle = computed(() => {
  const style: Record<string, string> = {
    width: props.width,
    height: props.height,
    borderRadius: props.borderRadius,
    border: props.border,
    padding: props.padding,
    margin: props.margin,
    backgroundColor: props.backgroundColor,
    display: props.display,
    position: props.position,
    top: props.top,
    left: props.left,
    right: props.right,
    bottom: props.bottom,
    zIndex: props.zIndex,
    opacity: props.opacity,
    transform: props.transform,
    transition: props.transition,
    cursor: props.cursor,
    userSelect: props.userSelect,
    overflow: props.overflow,
    boxShadow: props.boxShadow,
  }

  return Object.fromEntries(
    Object.entries(style).filter(
      ([_, value]) =>
        value !== 'auto' && value !== 'normal' && value !== 'none' && value !== 'transparent',
    ),
  )
})
</script>

<template>
  <div class="carousel-widget" :style="widgetStyle">
    <div class="carousel-container">
      <div class="carousel-track">
        <div v-for="(image, index) in imageList" :key="index" class="carousel-item">
          <img :src="image" :alt="`轮播图${index + 1}`" />
        </div>
      </div>

      <!-- 指示器 -->
      <div v-if="showIndicators && imageList.length > 1" class="carousel-indicators">
        <span
          v-for="(_, index) in imageList"
          :key="index"
          class="indicator"
          :class="{ active: index === 0 }"
        ></span>
      </div>

      <!-- 箭头 -->
      <div v-if="showArrows && imageList.length > 1" class="carousel-arrows">
        <button class="arrow prev">‹</button>
        <button class="arrow next">›</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.carousel-widget {
  box-sizing: border-box;
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.carousel-track {
  display: flex;
  width: 100%;
  height: 100%;
}

.carousel-item {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
}

.carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.indicator.active {
  background-color: #ffffff;
}

.carousel-arrows {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.arrow {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.arrow:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>
