<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import type { CanvasComponent } from '../../types/editor'

// 定义组件属性
interface Props {
    canvasComponents: CanvasComponent[]
    selectedComponent: CanvasComponent | null
    previewMode: boolean
}

// 定义组件事件
interface Emits {
    (e: 'componentSelect', component: CanvasComponent): void
    (e: 'componentDelete', component: CanvasComponent): void
    (e: 'componentMove', component: CanvasComponent, direction: 'up' | 'down'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 处理组件选择
const handleComponentSelect = (component: CanvasComponent) => {
    emit('componentSelect', component)
}

// 处理组件删除
const handleComponentDelete = (component: CanvasComponent) => {
    emit('componentDelete', component)
}

// 处理组件移动
const handleComponentMove = (component: CanvasComponent, direction: 'up' | 'down') => {
    emit('componentMove', component, direction)
}

// 获取组件图标
const getComponentIcon = (type: string) => {
    const iconMap: Record<string, string> = {
        text: 'material-symbols:text-fields',
        title: 'material-symbols:title',
        image: 'material-symbols:image',
        button: 'material-symbols:smart-button',
        container: 'material-symbols:view-module',
        row: 'material-symbols:view-stream',
        column: 'material-symbols:view-column',
        grid: 'material-symbols:grid-view',
        carousel: 'material-symbols:view-carousel',
        video: 'material-symbols:video-library',
        card: 'material-symbols:credit-card',
        banner: 'material-symbols:view-module',
        imageText: 'material-symbols:article',
        infoGrid: 'material-symbols:grid-view',
        fileList: 'material-symbols:folder'
    }
    return iconMap[type] || 'material-symbols:widgets'
}
</script>

<template>
    <div class="outline-panel">
        <div class="panel-header">
            <h4>
                <Icon icon="material-symbols:account-tree" style="width: 18px; height: 18px;" />
                页面大纲
            </h4>
        </div>

        <div class="panel-content">
            <div v-if="canvasComponents.length === 0" class="no-components">
                <Icon icon="material-symbols:add-circle" style="width: 48px; height: 48px; color: #c0c4cc;" />
                <p>暂无组件，请从左侧拖拽组件到画布</p>
            </div>

            <div v-else class="outline-tree">
                <div v-for="(component, index) in canvasComponents" :key="component.id" 
                     class="outline-item"
                     :class="{ active: selectedComponent?.id === component.id }"
                     @click="handleComponentSelect(component)">
                    
                    <div class="outline-item-content">
                        <div class="outline-item-icon">
                            <Icon :icon="getComponentIcon(component.type)" style="width: 16px; height: 16px;" />
                        </div>
                        <div class="outline-item-info">
                            <div class="outline-item-name">{{ component.name }}</div>
                            <div class="outline-item-type">{{ component.type }}</div>
                        </div>
                        <div class="outline-item-index">{{ index + 1 }}</div>
                    </div>

                    <div v-if="!previewMode" class="outline-item-actions">
                        <button v-if="index > 0" 
                                class="action-btn move-up" 
                                @click.stop="handleComponentMove(component, 'up')"
                                title="上移">
                            <Icon icon="material-symbols:keyboard-arrow-up" style="width: 14px; height: 14px;" />
                        </button>
                        <button v-if="index < canvasComponents.length - 1" 
                                class="action-btn move-down" 
                                @click.stop="handleComponentMove(component, 'down')"
                                title="下移">
                            <Icon icon="material-symbols:keyboard-arrow-down" style="width: 14px; height: 14px;" />
                        </button>
                        <button class="action-btn delete" 
                                @click.stop="handleComponentDelete(component)"
                                title="删除">
                            <Icon icon="material-symbols:delete" style="width: 14px; height: 14px;" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.outline-panel {
    width: 280px;
    background: white;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f2f5;
    background: #fafbfc;
}

.panel-header h4 {
    margin: 0;
    font-size: 14px;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.no-components {
    text-align: center;
    color: #909399;
    padding: 40px 20px;
}

.no-components p {
    margin: 16px 0 0 0;
    font-size: 14px;
}

.outline-tree {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.outline-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.outline-item:hover {
    background: #f5f7fa;
    border-color: #e4e7ed;
}

.outline-item.active {
    background: #ecf5ff;
    border-color: #409eff;
}

.outline-item-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
}

.outline-item-icon {
    color: #409eff;
    flex-shrink: 0;
}

.outline-item-info {
    flex: 1;
    min-width: 0;
}

.outline-item-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.outline-item-type {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
}

.outline-item-index {
    font-size: 12px;
    color: #c0c4cc;
    background: #f5f7fa;
    padding: 2px 6px;
    border-radius: 10px;
    flex-shrink: 0;
}

.outline-item-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.outline-item:hover .outline-item-actions {
    opacity: 1;
}

.action-btn {
    padding: 4px;
    background: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    color: #909399;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: #f5f7fa;
    color: #409eff;
}

.action-btn.delete:hover {
    background: #fef0f0;
    color: #f56c6c;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
    width: 6px;
}

.panel-content::-webkit-scrollbar-track {
    background: #f1f3f4;
}

.panel-content::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
    background: #909399;
}
</style> 