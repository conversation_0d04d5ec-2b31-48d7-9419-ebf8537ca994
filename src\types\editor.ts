// 画布组件类型定义
export interface CanvasComponent {
  id: string
  name: string
  icon: string
  type: string
  x: number
  y: number
  width?: number
  height?: number
  properties?: Record<string, any>
  layoutMode?: 'absolute' | 'flow' // 布局模式：绝对定位或流式布局
}

// 组件库项目类型
export interface ComponentItem {
  id: string
  name: string
  icon: string
  description: string
}

// 组件分类类型
export interface ComponentCategory {
  category: string
  components: ComponentItem[]
}

// 组件属性类型
export interface ComponentProperties {
  style: {
    width: string
    height: string
    margin: string
    padding: string
    backgroundColor: string
    borderRadius: string
    border: string
  }
  content: {
    text: string
    placeholder: string
    src: string
    alt: string
  }
  layout: {
    display: string
    flexDirection: string
    justifyContent: string
    alignItems: string
    gap: string
  }
}

// 编辑器状态类型
export interface EditorState {
  selectedComponent: CanvasComponent | null
  previewMode: boolean
  deviceType: 'desktop' | 'tablet' | 'mobile'
  showGrid: boolean
  zoom: number
}

// 页面配置类型
export interface PageConfig {
  id: string
  name: string
  components: CanvasComponent[]
  settings: {
    width: string
    height: string
    backgroundColor: string
  }
}
