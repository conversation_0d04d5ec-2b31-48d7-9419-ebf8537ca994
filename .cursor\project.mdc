---
description: "前端开发总览与核心规则 (Vue 3, Element Plus, Baidu Map)"
globs:
  # 这个规则将应用于所有前端相关的文件
  - '../src/**/*.{ts,js,vue}'
  - '../package.json'
  - '../vite.config.ts'
  - '../index.html'
alwaysApply: true
---

# 前端开发总览与核心规则

## 1. 项目目标与技术栈

### 项目目标
构建一个高质量、稳定且现代化的Web应用，名为“江西省学校“大思政课”实践教学基地数字地图”。

### 前端技术栈
- **核心框架**: Vue 3 (严格使用组合式API, `<script setup>`)
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **HTTP请求**: Axios
- **地图服务**: 国家地理信息天地图 JavaScript API 4.0

## 2. 架构与编码规范

### 状态管理 (Pinia)
- 为不同功能创建独立的store，例如 `useBaseStore` 用于管理基地数据和筛选条件，`useUserStore` 用于管理用户认证信息。

### API交互 (Axios)
- 所有API调用必须集中在 `../src/api/` 目录下的服务模块中。
- API的基础URL (`/api`) 应在一个集中的Axios实例中配置。

### 国家地理信息天地图集成
- **加载**: 国家地理信息天地图API脚本**必须**通过一个返回Promise的工具函数进行异步加载。
- **初始化**: 地图初始化 (`new T.Map(...)`) **必须**在 `onMounted` 生命周期钩子内部，且在API脚本成功加载后执行。
- **AK管理**: 国家地理信息天地图API密钥 (AK) **必须**存储为环境变量 (`VITE_BAIDU_MAP_AK`) 并通过 `import.meta.env.VITE_BAIDU_MAP_AK` 访问。**禁止**在组件中硬编码AK。

### Vue组件开发规范
- **结构**: 总是遵循 `<template>`, `<script setup>`, `<style scoped>` 的顺序。
- **样式**: 总是使用 `<style scoped>` 以防止组件间的CSS冲突。
- **路由**: 所有页面级组件**必须**进行懒加载，以提升首屏加载性能。例如: `component: () => import('../views/HomeView.vue')`。

## 3. 通用编码风格
- **语言**: 所有面向用户的UI文本、代码注释应使用**简体中文**。所有代码、变量名、函数名**必须**使用**英文**。
- **异步代码**: 优先使用 `async/await`，而不是 `.then()` 链。