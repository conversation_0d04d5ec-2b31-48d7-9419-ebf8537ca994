# ImageUploader 组件使用指南

## 概述

`ImageUploader` 是一个高度可配置的图片上传组件，支持多种使用场景、尺寸和布局样式。它封装了真实的图片上传接口，提供了统一的用户体验。

## 基本用法

```vue
<template>
  <ImageUploader v-model="imageUrl" />
</template>

<script setup>
import { ref } from 'vue'
import ImageUploader from '@/components/ImageUploader.vue'

const imageUrl = ref('')
</script>
```

## 配置选项

### 基础配置

| 属性         | 类型               | 默认值    | 说明                 |
| ------------ | ------------------ | --------- | -------------------- |
| `modelValue` | `string`           | `''`      | 图片URL，支持v-model |
| `disabled`   | `boolean`          | `false`   | 是否禁用上传         |
| `width`      | `string \| number` | `'200px'` | 组件宽度             |
| `height`     | `string \| number` | `'200px'` | 组件高度             |

### 布局配置

| 属性           | 类型                                  | 默认值     | 说明     |
| -------------- | ------------------------------------- | ---------- | -------- |
| `layout`       | `'square' \| 'rectangle' \| 'custom'` | `'square'` | 布局类型 |
| `borderStyle`  | `'dashed' \| 'solid' \| 'none'`       | `'dashed'` | 边框样式 |
| `borderRadius` | `string`                              | `'6px'`    | 圆角大小 |

### 显示配置

| 属性          | 类型      | 默认值 | 说明             |
| ------------- | --------- | ------ | ---------------- |
| `showPreview` | `boolean` | `true` | 是否显示预览功能 |
| `showDelete`  | `boolean` | `true` | 是否显示删除按钮 |
| `showView`    | `boolean` | `true` | 是否显示查看按钮 |

### 上传配置

| 属性      | 类型     | 默认值      | 说明             |
| --------- | -------- | ----------- | ---------------- |
| `maxSize` | `number` | `5`         | 最大文件大小(MB) |
| `accept`  | `string` | `'image/*'` | 接受的文件类型   |

### 文本配置

| 属性            | 类型     | 默认值           | 说明       |
| --------------- | -------- | ---------------- | ---------- |
| `placeholder`   | `string` | `'点击上传图片'` | 占位符文本 |
| `uploadingText` | `string` | `'上传中...'`    | 上传中文本 |

### 预览配置

| 属性            | 类型     | 默认值    | 说明           |
| --------------- | -------- | --------- | -------------- |
| `previewWidth`  | `string` | `'600px'` | 预览对话框宽度 |
| `previewHeight` | `string` | `'400px'` | 预览对话框高度 |

## 事件

| 事件名              | 参数              | 说明              |
| ------------------- | ----------------- | ----------------- |
| `update:modelValue` | `(value: string)` | 图片URL更新时触发 |
| `upload-success`    | `(url: string)`   | 上传成功时触发    |
| `upload-error`      | `(error: Error)`  | 上传失败时触发    |
| `remove`            | `()`              | 删除图片时触发    |

## 使用场景示例

### 1. 正方形头像上传

```vue
<template>
  <ImageUploader
    v-model="avatarUrl"
    :width="120"
    :height="120"
    layout="square"
    :max-size="2"
    placeholder="上传头像"
    accept="image/jpeg,image/png"
  />
</template>
```

### 2. 矩形横幅图片

```vue
<template>
  <ImageUploader
    v-model="bannerUrl"
    :width="400"
    :height="200"
    layout="rectangle"
    :max-size="5"
    placeholder="上传横幅图片"
    border-style="solid"
  />
</template>
```

### 3. 自定义尺寸

```vue
<template>
  <ImageUploader
    v-model="customImageUrl"
    width="100%"
    height="300px"
    layout="custom"
    :border-radius="'12px'"
    :show-view="false"
    placeholder="自定义尺寸上传"
  />
</template>
```

### 4. 只读模式

```vue
<template>
  <ImageUploader
    v-model="readonlyImageUrl"
    :disabled="true"
    :show-delete="false"
    :show-view="true"
  />
</template>
```

### 5. 带事件处理

```vue
<template>
  <ImageUploader
    v-model="imageUrl"
    @upload-success="handleUploadSuccess"
    @upload-error="handleUploadError"
    @remove="handleRemove"
  />
</template>

<script setup>
const handleUploadSuccess = (url: string) => {
  console.log('上传成功:', url)
}

const handleUploadError = (error: Error) => {
  console.error('上传失败:', error.message)
}

const handleRemove = () => {
  console.log('图片已删除')
}
</script>
```

## 在表单中使用

### BaseFormDialog 中的使用

```vue
<template>
  <el-form-item label="基地图片">
    <ImageUploader
      v-model="baseForm.imageUrl"
      :width="300"
      :height="200"
      layout="rectangle"
      :max-size="2"
      placeholder="拖拽图片到此处或点击上传"
      accept="image/jpeg,image/png"
      @upload-success="handleImageUploadSuccess"
      @upload-error="handleImageUploadError"
    />
    <div class="upload-tip">支持 jpg/png 格式，文件大小不超过 2MB</div>
  </el-form-item>
</template>
```

## 样式定制

组件支持通过CSS变量进行样式定制：

```css
.image-uploader {
  --upload-border-color: #409eff;
  --upload-hover-border-color: #66b1ff;
  --upload-bg-color: #fafafa;
  --upload-text-color: #8c939d;
}
```

## 响应式设计

组件内置响应式支持，在小屏幕设备上会自动调整：

- 图标大小从28px调整为24px
- 文字大小从14px调整为12px
- 按钮间距从8px调整为6px

## 最佳实践

1. **选择合适的尺寸**：根据使用场景选择合适的宽高比
2. **设置合理的文件大小限制**：避免上传过大的文件
3. **提供清晰的提示文本**：帮助用户了解上传要求
4. **处理上传事件**：为用户提供及时反馈
5. **考虑禁用状态**：在表单提交等场景下禁用上传

## 注意事项

1. 组件依赖 `@/api/user` 中的 `uploadImage` 接口
2. 上传的文件会自动进行类型和大小验证
3. 支持拖拽上传和点击上传两种方式
4. 预览功能使用Element Plus的Dialog组件
5. 删除操作会清空modelValue并触发remove事件
