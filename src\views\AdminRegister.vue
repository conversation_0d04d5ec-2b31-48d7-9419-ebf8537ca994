<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { User, Lock, Message } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { register } from '@/api/user'

const router = useRouter()
const registerForm = ref({
    phone: '',
    password: '',
    confirmPassword: '',
    code: ''
})
const loading = ref(false)

const handleRegister = async () => {
    if (!registerForm.value.phone || !registerForm.value.password || !registerForm.value.code) {
        ElMessage.error('请填写完整信息')
        return
    }

    if (registerForm.value.password !== registerForm.value.confirmPassword) {
        ElMessage.error('两次输入的密码不一致')
        return
    }

    loading.value = true

    try {
        const response = await register({
            phone: registerForm.value.phone,
            password: registerForm.value.password,
            code: registerForm.value.code
        })

        if (response.code === '200') {
            ElMessage.success('注册成功，请登录')
            router.push('/login')
        } else {
            ElMessage.error(response.msg || '注册失败')
        }
    } catch (error) {
        console.error('注册错误:', error)
        ElMessage.error('注册失败，请检查网络连接')
    } finally {
        loading.value = false
    }
}

const goToLogin = () => {
    router.push('/login')
}
</script>

<template>
    <div class="register-container">
        <!-- 背景装饰 -->
        <div class="background-decoration">
            <div class="red-star star-1"></div>
            <div class="red-star star-2"></div>
            <div class="red-star star-3"></div>
            <div class="red-star star-4"></div>
            <div class="red-star star-5"></div>
            <div class="floating-element element-1"></div>
            <div class="floating-element element-2"></div>
            <div class="floating-element element-3"></div>
        </div>

        <!-- 主背景 -->
        <div class="register-background">
            <div class="background-overlay"></div>
            <div class="background-pattern"></div>
        </div>

        <div class="register-content">
            <!-- 系统Logo和标题 -->
            <div class="system-header">
                <div class="logo-container">
                </div>
                <h1 class="system-title">江西红色教育实践基地</h1>
                <!-- <p class="system-subtitle">管理系统</p> -->
            </div>

            <div class="register-card">
                <div class="register-header">
                    <div class="header-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                                fill="#E74C3C" />
                        </svg>
                    </div>
                    <h2>管理员注册</h2>
                </div>

                <el-form :model="registerForm" class="register-form" @submit.prevent="handleRegister">
                    <el-form-item>
                        <el-input v-model="registerForm.phone" placeholder="请输入手机号" size="large" :prefix-icon="User"
                            class="custom-input" />
                    </el-form-item>

                    <el-form-item>
                        <el-input v-model="registerForm.password" type="password" placeholder="请输入密码" size="large"
                            :prefix-icon="Lock" show-password class="custom-input" />
                    </el-form-item>

                    <el-form-item>
                        <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请确认密码"
                            size="large" :prefix-icon="Lock" show-password class="custom-input" />
                    </el-form-item>

                    <el-form-item>
                        <el-input v-model="registerForm.code" placeholder="请输入验证码" size="large" :prefix-icon="Message"
                            class="custom-input" />
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" size="large" :loading="loading" @click="handleRegister"
                            class="register-button">
                            <span v-if="!loading">注册账号</span>
                            <span v-else>注册中...</span>
                        </el-button>
                    </el-form-item>
                </el-form>

                <div class="register-footer">
                    <div class="footer-divider">
                        <span class="divider-text">管理系统</span>
                    </div>
                    <div class="footer-links">
                        <p>已有账号？ <el-button type="text" @click="goToLogin" class="link-button">立即登录</el-button></p>
                    </div>
                </div>
            </div>

            <!-- 底部装饰 -->
            <div class="bottom-decoration">
                <div class="red-line"></div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.register-container {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.red-star {
    position: absolute;
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #E74C3C, #C0392B);
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    animation: float 6s ease-in-out infinite;
}

.star-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.star-2 {
    top: 20%;
    right: 15%;
    animation-delay: 1s;
}

.star-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 2s;
}

.star-4 {
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

.star-5 {
    top: 50%;
    left: 5%;
    animation-delay: 4s;
}

.floating-element {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgba(231, 76, 60, 0.3);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
}

.element-1 {
    top: 15%;
    right: 25%;
    animation-delay: 0.5s;
}

.element-2 {
    bottom: 25%;
    left: 30%;
    animation-delay: 1.5s;
}

.element-3 {
    top: 70%;
    right: 20%;
    animation-delay: 2.5s;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* 主背景 */
.register-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    z-index: 0;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(192, 57, 43, 0.1) 100%);
}

.background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(231, 76, 60, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(192, 57, 43, 0.03) 0%, transparent 50%);
}

.register-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 450px;
    padding: 20px;
}

/* 系统标题 */
.system-header {
    text-align: center;
    margin-bottom: 40px;
}

.logo-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #E74C3C, #C0392B);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(231, 76, 60, 0.3);
}

.logo-icon svg {
    width: 32px;
    height: 32px;
}

.system-title {
    font-size: 28px;
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.system-subtitle {
    font-size: 18px;
    color: #E74C3C;
    font-weight: 600;
    margin: 0;
}

/* 注册卡片 */
.register-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.1);
    position: relative;
    overflow: hidden;
}

.register-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #E74C3C, #C0392B);
}

.register-header {
    text-align: center;
    margin-bottom: 32px;
}

.header-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
}

.header-icon svg {
    width: 32px;
    height: 32px;
}

.register-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #2C3E50;
    margin-bottom: 8px;
}

.register-header p {
    color: #E74C3C;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
}

/* 表单样式 */
.register-form {
    margin-bottom: 24px;
}

.register-form .el-form-item {
    margin-bottom: 20px;
}

.custom-input :deep(.el-input__wrapper) {
    border-radius: 12px;
    border: 2px solid #E8E8E8;
    transition: all 0.3s ease;
    background: #FAFAFA;
}

.custom-input :deep(.el-input__wrapper:hover) {
    border-color: #E74C3C;
    background: white;
}

.custom-input :deep(.el-input__wrapper.is-focus) {
    border-color: #E74C3C;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1);
    background: white;
}

.custom-input :deep(.el-input__inner) {
    height: 48px;
    font-size: 16px;
}

.custom-input :deep(.el-input__prefix) {
    color: #E74C3C;
}

.register-button {
    width: 100%;
    height: 52px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    background: linear-gradient(135deg, #E74C3C, #C0392B);
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(231, 76, 60, 0.3);
}

.register-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(231, 76, 60, 0.4);
}

.register-button:active {
    transform: translateY(0);
}

/* 底部样式 */
.register-footer {
    text-align: center;
    padding-top: 24px;
}

.footer-divider {
    position: relative;
    margin-bottom: 20px;
}

.footer-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #E74C3C, transparent);
}

.divider-text {
    background: white;
    padding: 0 16px;
    color: #E74C3C;
    font-size: 12px;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.footer-links p {
    color: #7F8C8D;
    font-size: 14px;
    margin: 8px 0;
}

.link-button {
    color: #E74C3C !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.link-button:hover {
    color: #C0392B !important;
    text-decoration: underline;
}

/* 底部装饰 */
.bottom-decoration {
    text-align: center;
    margin-top: 30px;
}

.red-line {
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #E74C3C, #C0392B);
    margin: 0 auto 16px;
    border-radius: 2px;
}

.decoration-text {
    color: #95A5A6;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 1px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .register-content {
        padding: 12px;
    }

    .register-card {
        padding: 32px 24px;
    }

    .system-title {
        font-size: 24px;
    }

    .system-subtitle {
        font-size: 16px;
    }

    .red-star {
        width: 16px;
        height: 16px;
    }

    .floating-element {
        width: 6px;
        height: 6px;
    }
}

@media (max-width: 480px) {
    .system-title {
        font-size: 20px;
    }

    .register-card {
        padding: 24px 20px;
    }

    .custom-input :deep(.el-input__inner) {
        height: 44px;
        font-size: 14px;
    }

    .register-button {
        height: 48px;
        font-size: 14px;
    }
}
</style>