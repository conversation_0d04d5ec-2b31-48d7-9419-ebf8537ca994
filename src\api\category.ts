import api from './config'

// 分类信息接口类型定义
export interface Category {
  id?: number
  title: string
  color: string
}

// 新增分类
export interface AddCategoryParams extends Category {}

export interface AddCategoryResponse {
  code: string
  msg: string
}

export const addCategory = (params: AddCategoryParams): Promise<AddCategoryResponse> => {
  return api.post('/category/addCategory', params)
}

// 删除分类
export interface DeleteCategoryParams {
  id: number
}

export interface DeleteCategoryResponse {
  code: string
  msg: string
}

export const deleteCategory = (params: DeleteCategoryParams): Promise<DeleteCategoryResponse> => {
  return api.post('/category/deleteCategory', params)
}

// 获取所有分类
export interface ListAllCategoryResponse {
  code: string
  data: Category[]
  msg: string
}

export const listAllCategory = (): Promise<ListAllCategoryResponse> => {
  return api.get('/category/listAll')
}

// 更新分类
export interface UpdateCategoryParams extends Category {
  id: number // 更新时必须提供id
}

export interface UpdateCategoryResponse {
  code: string
  msg: string
}

export const updateCategory = (params: UpdateCategoryParams): Promise<UpdateCategoryResponse> => {
  return api.post('/category/updateCategory', params)
}
