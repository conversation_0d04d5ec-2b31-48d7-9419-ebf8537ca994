<script setup lang="ts">
import { computed, ref } from 'vue'
import { Icon } from '@iconify/vue'
import { ElMessage } from 'element-plus'
import { uploadImage } from '@/api/user'

// 定义组件属性
interface Props {
  src?: string
  imageStyle?: 'default' | 'rounded' | 'circle' | 'shadow'
  imageSize?: 'small' | 'medium' | 'large' | 'full'
  objectFit?: 'cover' | 'contain' | 'fill'
  imageAlign?: 'left' | 'center' | 'right'
  width?: number
  height?: number
  backgroundColor?: string
}

// 定义组件事件
interface Emits {
  (e: 'update:src', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  src: '',
  imageStyle: 'default',
  imageSize: 'medium',
  objectFit: 'cover',
  imageAlign: 'center',
  width: undefined, // 全宽
  height: undefined, // 高度自适应
  backgroundColor: 'white',
})

const emit = defineEmits<Emits>()

// 状态管理
const imageLoaded = ref(false)
const imageError = ref(false)
const isUploading = ref(false)

// 尺寸配置（保留用于属性面板选择）
// const sizeConfig = {
//   small: { width: '200px', height: '150px' },
//   medium: { width: '400px', height: '300px' },
//   large: { width: '600px', height: '450px' },
//   full: { width: '100%', height: 'auto' },
// }

// 样式配置
const styleConfig = {
  default: {
    borderRadius: '0px',
    boxShadow: 'none',
    border: 'none',
  },
  rounded: {
    borderRadius: '12px',
    boxShadow: 'none',
    border: 'none',
  },
  circle: {
    borderRadius: '50%',
    boxShadow: 'none',
    border: 'none',
  },
  shadow: {
    borderRadius: '12px',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
    border: 'none',
  },
}

// 计算样式
const widgetStyle = computed(() => {
  const style = styleConfig[props.imageStyle] || styleConfig.default

  return {
    // 完全填充外层容器，不使用内部尺寸配置
    width: props.width ? `${props.width}px` : '100%',
    height: props.height ? `${props.height}px` : '100%',
    ...style,
    boxSizing: 'border-box' as const,
    position: 'relative' as const,
    overflow: 'hidden' as const,
    backgroundColor: props.backgroundColor || '#f8f9fa',
    // 根据对齐方式设置margin
    marginLeft: props.imageAlign === 'left' ? '0' : props.imageAlign === 'right' ? 'auto' : 'auto',
    marginRight: props.imageAlign === 'right' ? '0' : props.imageAlign === 'left' ? 'auto' : 'auto',
  } as const
})

// 图片样式
const imageStyle = computed(() => ({
  width: '100%',
  height: '100%',
  objectFit: props.objectFit as 'cover' | 'contain' | 'fill',
  borderRadius: 'inherit',
  display: 'block',
  opacity: imageLoaded.value ? 1 : 0,
  transition: 'opacity 0.3s ease, transform 0.3s ease',
}))

// 图片加载成功处理
const handleImageLoad = () => {
  imageLoaded.value = true
  imageError.value = false
}

// 图片加载错误处理
const handleImageError = () => {
  imageError.value = true
  imageLoaded.value = false
}

// 文件上传处理
const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement
  const file = input.files?.[0]

  if (file) {
    isUploading.value = true

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      ElMessage.error('请选择图片文件')
      isUploading.value = false
      return
    }

    // 检查文件大小 (5MB限制)
    if (file.size > 5 * 1024 * 1024) {
      ElMessage.error('图片文件大小不能超过5MB')
      isUploading.value = false
      return
    }

    try {
      // 调用上传接口
      const response = await uploadImage({ file })

      if (response.code === '200') {
        const imageUrl = response.data
        emit('update:src', imageUrl)
        ElMessage.success('图片上传成功')
      } else {
        ElMessage.error(response.msg || '上传失败')
      }
    } catch (error) {
      console.error('图片上传错误:', error)
      ElMessage.error('上传失败，请检查网络连接')
    } finally {
      isUploading.value = false
    }
  }
}

// 触发文件选择
const triggerFileSelect = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = handleFileUpload
  input.click()
}
</script>

<template>
  <div class="image-widget" :style="widgetStyle" @dblclick="triggerFileSelect">
    <!-- 有图片时显示图片 -->
    <div v-if="props.src && !imageError" class="image-container">
      <img :src="props.src" alt="图片" :style="imageStyle" @load="handleImageLoad" @error="handleImageError"
        loading="lazy" />
      <div v-if="!imageLoaded" class="image-loading">
        <Icon icon="material-symbols:image" style="width: 32px; height: 32px; color: #c0c4cc" />
        <span>加载中...</span>
      </div>
      <!-- 双击提示遮罩 -->
      <div class="double-click-overlay">
        <div class="double-click-hint">
          <Icon icon="material-symbols:upload" style="width: 20px; height: 20px" />
          <span>双击更换图片</span>
        </div>
      </div>
    </div>

    <!-- 上传区域 -->
    <div v-else class="image-placeholder">
      <div v-if="isUploading" class="upload-loading">
        <Icon icon="material-symbols:upload" style="width: 32px; height: 32px; color: #409eff" />
        <span>上传中...</span>
      </div>
      <div v-else class="placeholder-content">
        <Icon icon="material-symbols:add-photo-alternate" style="width: 48px; height: 48px; color: #c0c4cc" />
        <div class="placeholder-text">双击上传图片</div>
        <div class="placeholder-desc">支持 JPG、PNG、GIF 格式，最大 5MB</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.image-widget {
  line-height: 0;
  transition: all 0.3s ease;
}

.image-widget:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-container:hover .double-click-overlay {
  opacity: 1;
}

.image-widget img {
  max-width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

.image-widget img:hover {
  transform: scale(1.02);
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #c0c4cc;
  font-size: 14px;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(248, 249, 250, 0.5);
  border-radius: inherit;
  transition: all 0.3s ease;
  cursor: pointer;
}

.image-placeholder:hover {
  background: rgba(240, 249, 255, 0.8);
}

.placeholder-content {
  text-align: center;
  color: #909399;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.placeholder-text {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.placeholder-desc {
  font-size: 12px;
  color: #909399;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-size: 14px;
}

/* 双击提示遮罩样式 */
.double-click-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  pointer-events: none;
}

.double-click-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  background: rgba(64, 158, 255, 0.9);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-container {
    min-height: 100px;
  }

  .image-placeholder {
    min-height: 100px;
  }

  .placeholder-text {
    font-size: 13px;
  }

  .placeholder-desc {
    font-size: 11px;
  }
}
</style>
