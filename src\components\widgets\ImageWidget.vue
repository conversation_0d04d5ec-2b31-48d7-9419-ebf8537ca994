<script setup lang="ts">
import { computed, ref } from 'vue'
import { Icon } from '@iconify/vue'

// 定义组件属性
interface Props {
    src?: string
    alt?: string
    imageStyle?: 'default' | 'rounded' | 'circle' | 'shadow' | 'bordered'
    imageSize?: 'small' | 'medium' | 'large' | 'full' | 'auto'
    objectFit?: 'cover' | 'contain' | 'fill'
    showUpload?: boolean
    uploadText?: string
    [key: string]: any
}

const props = withDefaults(defineProps<Props>(), {
    src: '',
    alt: '图片',
    imageStyle: 'default',
    imageSize: 'auto',
    objectFit: 'cover',
    showUpload: true,
    uploadText: '点击上传图片'
})

// 状态管理
const imageLoaded = ref(false)
const imageError = ref(false)
const isUploading = ref(false)

// 尺寸配置
const sizeConfig = {
    small: { width: '200px', height: '150px' },
    medium: { width: '400px', height: '300px' },
    large: { width: '600px', height: '450px' },
    full: { width: '100%', height: 'auto' },
    auto: { width: 'auto', height: 'auto' }
}

// 样式配置
const styleConfig = {
    default: {
        borderRadius: '0px',
        boxShadow: 'none',
        border: 'none'
    },
    rounded: {
        borderRadius: '8px',
        boxShadow: 'none',
        border: 'none'
    },
    circle: {
        borderRadius: '50%',
        boxShadow: 'none',
        border: 'none'
    },
    shadow: {
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        border: 'none'
    },
    bordered: {
        borderRadius: '8px',
        boxShadow: 'none',
        border: '2px solid #e4e7ed'
    }
}

// 计算样式
const widgetStyle = computed(() => {
    const size = sizeConfig[props.imageSize] || sizeConfig.auto
    const style = styleConfig[props.imageStyle] || styleConfig.default

    return {
        ...size,
        ...style,
        boxSizing: 'border-box',
        position: 'relative',
        overflow: 'hidden'
    } as any
})

// 图片样式
const imageStyle = computed(() => ({
    width: '100%',
    height: '100%',
    objectFit: props.objectFit as 'cover' | 'contain' | 'fill',
    borderRadius: 'inherit',
    display: 'block',
    opacity: imageLoaded.value ? 1 : 0,
    transition: 'opacity 0.3s ease, transform 0.3s ease'
}))

// 图片加载成功处理
const handleImageLoad = () => {
    imageLoaded.value = true
    imageError.value = false
}

// 图片加载错误处理
const handleImageError = () => {
    imageError.value = true
    imageLoaded.value = false
}

// 文件上传处理
const handleFileUpload = (event: Event) => {
    const input = event.target as HTMLInputElement
    const file = input.files?.[0]

    if (file) {
        isUploading.value = true

        // 检查文件类型
        if (!file.type.startsWith('image/')) {
            alert('请选择图片文件')
            isUploading.value = false
            return
        }

        // 检查文件大小 (5MB限制)
        if (file.size > 5 * 1024 * 1024) {
            alert('图片文件大小不能超过5MB')
            isUploading.value = false
            return
        }

        // 转换为Base64
        const reader = new FileReader()
        reader.onload = (e) => {
            const result = e.target?.result as string
            // 这里可以触发事件通知父组件更新图片URL
            console.log('图片上传成功:', result)
            isUploading.value = false
        }
        reader.onerror = () => {
            alert('图片上传失败')
            isUploading.value = false
        }
        reader.readAsDataURL(file)
    }
}

// 触发文件选择
const triggerFileSelect = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = handleFileUpload
    input.click()
}
</script>

<template>
    <div class="image-widget" :style="widgetStyle">
        <!-- 有图片时显示图片 -->
        <div v-if="src && !imageError" class="image-container">
            <img :src="src" :alt="alt" :style="imageStyle" @load="handleImageLoad" @error="handleImageError"
                loading="lazy" />
            <div v-if="!imageLoaded" class="image-loading">
                <Icon icon="material-symbols:image" style="width: 32px; height: 32px; color: #c0c4cc;" />
                <span>加载中...</span>
            </div>
        </div>

        <!-- 上传区域 -->
        <div v-else-if="showUpload" class="upload-area" @click="triggerFileSelect">
            <div v-if="isUploading" class="upload-loading">
                <Icon icon="material-symbols:upload" style="width: 32px; height: 32px; color: #409eff;" />
                <span>上传中...</span>
            </div>
            <div v-else class="upload-content">
                <Icon icon="material-symbols:add-photo-alternate" style="width: 48px; height: 48px; color: #c0c4cc;" />
                <span class="upload-text">{{ uploadText }}</span>
                <span class="upload-hint">支持 JPG、PNG、GIF 格式，最大 5MB</span>
            </div>
        </div>

        <!-- 占位符 -->
        <div v-else class="image-placeholder">
            <Icon icon="material-symbols:image" style="width: 48px; height: 48px; color: #c0c4cc;" />
            <span>图片组件</span>
        </div>
    </div>
</template>

<style scoped>
.image-widget {
    line-height: 0;
}

.image-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 100px;
}

.image-widget img {
    max-width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.image-widget img:hover {
    transform: scale(1.02);
}

.image-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #c0c4cc;
    font-size: 14px;
}

.upload-area {
    width: 100%;
    height: 100%;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #e4e7ed;
    border-radius: 8px;
    background: #fafbfc;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #409eff;
    background: #f0f9ff;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
}

.upload-text {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

.upload-hint {
    font-size: 12px;
    color: #909399;
}

.upload-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #409eff;
    font-size: 14px;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    border: 1px dashed #e4e7ed;
    border-radius: 4px;
    color: #909399;
    font-size: 14px;
    gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-area {
        min-height: 100px;
    }

    .upload-text {
        font-size: 13px;
    }

    .upload-hint {
        font-size: 11px;
    }
}
</style>