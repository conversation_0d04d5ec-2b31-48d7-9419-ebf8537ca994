<script setup lang="ts">
// 定义组件属性
interface Props {
  text?: string
  fontSize?: number
  textAlign?: string
  color?: string
  width?: number
  height?: number
  backgroundColor?: string
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  text: '这是一个区块标题',
  fontSize: 32,
  textAlign: 'center',
  color: '#333333',
  width: undefined, // 全宽
  height: undefined, // 高度自适应
  backgroundColor: 'white',
})
</script>

<template>
  <div class="title-widget" :style="{
    width: props.width ? `${props.width}px` : '100%',
    height: props.height ? `${props.height}px` : '100%',
    backgroundColor: props.backgroundColor,
    textAlign: props.textAlign as any,
    fontSize: `${props.fontSize}px`,
    color: props.color,
  }">
    <h2 class="title-text">{{ props.text }}</h2>
  </div>
</template>

<style scoped>
.title-widget {
  /* 适配基础组件容器样式 */
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  background: transparent;
  padding: 12px;
  margin: 0;
}

.title-text {
  /* 标题文本样式 */
  font-weight: bold;
  line-height: 1.2;
  margin: 0;
  padding: 0;
  width: 100%;
  text-align: inherit;
  font-size: inherit;
  color: inherit;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
</style>
