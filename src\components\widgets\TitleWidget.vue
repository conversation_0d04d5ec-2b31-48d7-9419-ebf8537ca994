<script setup lang="ts">
// 定义组件属性
interface Props {
    text?: string
    fontSize?: number
    textAlign?: string
    color?: string
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
    text: '这是一个区块标题',
    fontSize: 32,
    textAlign: 'center',
    color: '#333333',
})
</script>

<template>
    <h2 class="title-widget" :style="{
        textAlign: props.textAlign as any,
        fontSize: `${props.fontSize}px`,
        color: props.color
    }">
        {{ props.text }}
    </h2>
</template>

<style scoped>
.title-widget {
    /* 固化的样式，用户不可配置 */
    font-weight: bold;
    margin: 40px 0;
    /* 固定的垂直边距，确保节奏感 */
    line-height: 1.4;
    padding: 0 15px;
    /* 预留一些水平内边距 */
    box-sizing: border-box;
}
</style>