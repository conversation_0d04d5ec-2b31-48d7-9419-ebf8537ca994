<script setup lang="ts">
import { computed, nextTick, watch, ref, onMounted, onUnmounted } from 'vue'

import { Refresh, Delete } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'

// 导入类型
import type { CanvasComponent, ComponentItem } from '../../types/editor'

// 导入组件映射
import { widgetMap } from '../widgets/index'

// 定义组件属性
interface Props {
  canvasComponents: CanvasComponent[]
  selectedComponent: CanvasComponent | null
  previewMode: boolean
  showGrid: boolean
  deviceType: 'desktop' | 'tablet' | 'mobile'
  zoom: number
  pageSettings?: {
    width: string
    height: string
    backgroundColor: string
    title: string
  }
  pageHeader?: {
    title: string
    backgroundColor: string
    textColor: string
    height: string
    fontSize: string
    fontWeight: string
  }
  pageFooter?: {
    contactName: string
    contactPhone: string
    address: string
    copyrightInfo: string
    licenseInfo: string
    backgroundColor: string
    textColor: string
  }
  isHeaderSelected?: boolean
  isFooterSelected?: boolean
}

// 定义组件事件
interface Emits {
  (e: 'componentDrop', component: ComponentItem, event: DragEvent): void
  (e: 'componentSelect', component: CanvasComponent | null): void
  (e: 'componentDelete', component: CanvasComponent): void
  (e: 'componentReorder', fromIndex: number, toIndex: number): void
  (e: 'componentPropertyUpdate', component: CanvasComponent, key: string, value: unknown): void
  (e: 'componentsUpdate', components: CanvasComponent[]): void
  (e: 'toggleGrid'): void
  (e: 'clearCanvas'): void
  (e: 'headerSelect'): void
  (e: 'footerSelect'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 定义业务组件类型
const businessComponentTypes = ['banner', 'imageText', 'infoGrid', 'fileList', 'card', 'videoList']

// 判断是否为业务组件
const isBusinessComponent = (component: CanvasComponent) => {
  return businessComponentTypes.includes(component.type)
}

// 动态响应式定位系统（与LowCodeEditor完全一致）
const RESPONSIVE_CONFIG = {
  containerPadding: 20, // 与canvas-components的padding一致
  minContainerWidth: 320,
  minContainerHeight: 200,
  devices: {
    desktop: { maxWidth: '100%', containerMaxWidth: null },
    tablet: { maxWidth: '768px', containerMaxWidth: 768 },
    mobile: { maxWidth: '375px', containerMaxWidth: 375 }
  }
}

// 获取设计画布的实际容器尺寸
const getDesignCanvasSize = () => {
  // 尝试获取canvas-components的实际尺寸
  const canvasElement = canvasComponentsRef.value
  if (canvasElement) {
    const rect = canvasElement.getBoundingClientRect()
    return {
      width: Math.max(rect.width - (RESPONSIVE_CONFIG.containerPadding * 2), RESPONSIVE_CONFIG.minContainerWidth),
      height: Math.max(rect.height, RESPONSIVE_CONFIG.minContainerHeight)
    }
  }

  // 回退到基于设备类型的估算
  const currentDevice = props.deviceType
  const deviceConfig = RESPONSIVE_CONFIG.devices[currentDevice]
  const estimatedWidth = deviceConfig.containerMaxWidth || 1200 // 桌面模式默认1200

  return {
    width: Math.max(estimatedWidth - (RESPONSIVE_CONFIG.containerPadding * 2), RESPONSIVE_CONFIG.minContainerWidth),
    height: 600 // 设计画布默认高度
  }
}

// 设计画布专用：动态响应式转换（用于非桌面模式的预览）
const convertToResponsiveUnitsForCanvas = (component: CanvasComponent) => {
  if (props.deviceType === 'desktop') {
    // 桌面模式：直接使用像素值，便于编辑
    return {
      left: component.x || 0,
      top: component.y || 0,
      width: component.width || 200,
      height: component.height || 'auto'
    }
  }

  // 非桌面模式：动态缩放预览
  const designCanvasSize = getDesignCanvasSize()
  const currentDevice = props.deviceType
  const deviceConfig = RESPONSIVE_CONFIG.devices[currentDevice]

  // 计算缩放比例（基于实际画布宽度）
  const targetWidth = deviceConfig.containerMaxWidth || designCanvasSize.width
  const scaleFactor = targetWidth / designCanvasSize.width

  return {
    left: Math.round((component.x || 0) * scaleFactor),
    top: Math.round((component.y || 0) * scaleFactor),
    width: component.width ? Math.round(component.width * scaleFactor) : Math.round(200 * scaleFactor),
    height: component.height ? Math.round(component.height * scaleFactor) : 'auto'
  }
}

// 分离业务组件和基础组件
const businessComponents = computed(() => {
  return props.canvasComponents.filter(component => isBusinessComponent(component))
})

const basicComponents = computed(() => {
  return props.canvasComponents.filter(component => !isBusinessComponent(component))
})

// 分离流式布局组件和绝对定位组件
const flowLayoutComponents = computed(() => {
  return props.canvasComponents.filter(component =>
    isBusinessComponent(component) || (component.layoutMode === 'flow' && component.type !== 'button')
  )
})

const absolutePositionComponents = computed(() => {
  return props.canvasComponents.filter(component =>
    !isBusinessComponent(component) && (component.layoutMode !== 'flow' || component.type === 'button')
  )
})

// 创建可写的流式布局组件列表供 Vue.Draggable 使用
const flowLayoutComponentsList = computed({
  get() {
    return flowLayoutComponents.value
  },
  set(newList: CanvasComponent[]) {
    // 当 Vue.Draggable 更新列表时，重新构建完整的组件列表
    const absoluteComps = absolutePositionComponents.value
    const newFullList = [...absoluteComps, ...newList]

    console.log('🔄 Vue.Draggable 流式布局组件排序')
    console.log('📋 新的流式布局组件顺序:', newList.map(c => `${c.type}(${c.id}) [${c.layoutMode || 'business'}]`))

    // 通过事件更新父组件的完整列表
    emit('componentsUpdate', newFullList)
  }
})

// 保持向后兼容的业务组件列表（目前未使用）
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const businessComponentsList = computed({
  get() {
    return businessComponents.value
  },
  set(newList: CanvasComponent[]) {
    // 兼容性保持
    const basicComps = basicComponents.value
    const newFullList = [...basicComps, ...newList]
    emit('componentsUpdate', newFullList)
  }
})

// 计算绝对定位组件需要的最小画布高度
const calculateRequiredCanvasHeight = () => {
  if (absolutePositionComponents.value.length === 0) {
    return 800 // 默认最小高度
  }

  let maxBottom = 0
  absolutePositionComponents.value.forEach(component => {
    const bottom = (component.y || 0) + (component.height || 100) + 100 // 添加100px缓冲区
    if (bottom > maxBottom) {
      maxBottom = bottom
    }
  })

  // 确保至少有800px的基础高度，并添加额外的200px安全区域
  return Math.max(maxBottom + 200, 800)
}

// 动态画布高度（响应式）
const dynamicCanvasHeight = computed(() => {
  const requiredHeight = calculateRequiredCanvasHeight()
  return `${requiredHeight}px`
})

// 画布容器引用
const canvasComponentsRef = ref<HTMLElement>()

// 调整大小相关状态
const resizing = ref(false)
const resizeData = ref<{
  component: CanvasComponent | null
  direction: string
  startX: number
  startY: number
  startWidth: number
  startHeight: number
  maxWidth?: number
  maxHeight?: number
} | null>(null)

// 基础组件拖拽相关状态
const dragging = ref(false)
const dragData = ref<{
  component: CanvasComponent | null
  startX: number
  startY: number
  startComponentX: number
  startComponentY: number
  offsetX: number
  offsetY: number
} | null>(null)

// 右键菜单状态
const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  componentId: ''
})

// 画布扩展提示状态
const canvasExpansionHint = ref({
  visible: false,
  type: 'bottom' // 'bottom' | 'auto'
})

// 计算画布样式
const canvasStyle = computed(() => {
  const baseStyle = {
    zoom: `${props.zoom}%`,
    maxWidth:
      props.deviceType === 'mobile' ? '375px' : props.deviceType === 'tablet' ? '768px' : '100%',
  }

  // 合并页面设置
  if (props.pageSettings) {
    return {
      ...baseStyle,
      width: props.pageSettings.width,
      height: props.pageSettings.height,
      backgroundColor: props.pageSettings.backgroundColor,
    }
  }

  return baseStyle
})

// 自动滚动到选中组件
const scrollToSelectedComponent = async (component: CanvasComponent) => {
  if (!canvasComponentsRef.value) return

  await nextTick()

  // 查找组件对应的DOM元素
  const componentElement = canvasComponentsRef.value.querySelector(
    `[data-component-id="${component.id}"]`,
  ) as HTMLElement
  if (!componentElement) return

  // 获取容器和元素的位置信息
  const container = canvasComponentsRef.value
  const containerRect = container.getBoundingClientRect()
  const elementRect = componentElement.getBoundingClientRect()

  // 计算元素是否在可视区域内（增加一些容差）
  const tolerance = 50
  const isVisible =
    elementRect.top >= containerRect.top - tolerance &&
    elementRect.bottom <= containerRect.bottom + tolerance

  // 如果不在可视区域内，滚动到元素位置
  if (!isVisible) {
    // 计算目标滚动位置，让组件出现在容器中央
    const elementTop = componentElement.offsetTop
    const containerHeight = container.clientHeight
    const elementHeight = elementRect.height
    const scrollOffset = 100 // 额外的偏移量，确保组件不会太靠近边缘

    const targetScrollTop = elementTop - containerHeight / 2 + elementHeight / 2 - scrollOffset

    container.scrollTo({
      top: Math.max(0, targetScrollTop),
      behavior: 'smooth',
    })
  }
}

// 监听选中组件变化
watch(
  () => props.selectedComponent,
  (newComponent) => {
    if (newComponent) {
      scrollToSelectedComponent(newComponent)
    }
  },
  { immediate: false },
)

// 监听设备类型变化，输出响应式调试信息
watch(
  () => props.deviceType,
  (newDevice, oldDevice) => {
    if (oldDevice && newDevice !== oldDevice) {
      console.log(`📱 设计画布设备切换: ${oldDevice} → ${newDevice}`)
      console.log(`📐 画布宽度变化: ${getDesignCanvasSize().width}px`)

      // 输出基础组件响应式调整信息
      const basicComps = basicComponents.value
      if (basicComps.length > 0) {
        console.log('🔄 基础组件响应式调整预览:')
        basicComps.forEach(component => {
          if (newDevice === 'desktop') {
            console.log(`  📍 ${component.type}(${component.id}): 原始模式 - (${component.x}px, ${component.y}px) ${component.width}×${component.height}px`)
          } else {
            const responsive = convertToResponsiveUnitsForCanvas(component)
            console.log(`  📐 ${component.type}(${component.id}): 响应式模式 - (${responsive.left}px, ${responsive.top}px) ${responsive.width}×${responsive.height}px`)
          }
        })
      }
    }
  },
  { immediate: false }
)

// 事件处理
const handleCanvasComponentDrop = (event: DragEvent) => {
  event.preventDefault()

  // 获取拖拽的组件数据
  const componentData = event.dataTransfer?.getData('application/json')
  if (componentData) {
    try {
      const component = JSON.parse(componentData)

      // 如果是基础组件，检查放置位置是否会导致画布扩展
      if (!businessComponentTypes.includes(component.id)) {
        const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
        const y = event.clientY - rect.top
        const currentHeight = calculateRequiredCanvasHeight()
        const estimatedNewHeight = y + 100 + 200 // 组件高度估算 + 缓冲区

        if (estimatedNewHeight > currentHeight) {
          console.log(`📏 组件放置将触发画布扩展: ${currentHeight}px → ${estimatedNewHeight}px`)
        }
      }

      emit('componentDrop', component, event)
    } catch (error) {
      console.error('解析组件数据失败:', error)
    }
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
}

const handleComponentSelect = (component: CanvasComponent) => {
  // 如果正在拖拽业务组件，忽略选择事件
  if (isDraggingBusiness.value) {
    console.log('🔄 忽略拖拽期间的选择事件')
    return
  }

  emit('componentSelect', component)
  // 隐藏右键菜单
  contextMenu.value.visible = false
}

const handleComponentDelete = (component: CanvasComponent) => {
  emit('componentDelete', component)
}

// 右键菜单相关处理
const handleComponentRightClick = (event: MouseEvent, component: CanvasComponent) => {
  event.preventDefault()
  event.stopPropagation()

  // 获取组件容器的位置
  const componentElement = event.currentTarget as HTMLElement
  const rect = componentElement.getBoundingClientRect()

  // 设置右键菜单位置（相对于组件）
  contextMenu.value = {
    visible: true,
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
    componentId: component.id
  }
}

const handleContextMenuDelete = (component: CanvasComponent) => {
  handleComponentDelete(component)
  contextMenu.value.visible = false
}

const handleResetToAdaptive = (component: CanvasComponent) => {
  // 重置组件为自适应状态
  component.width = undefined
  component.height = undefined

  // 同时清除属性中的宽高
  if (component.properties) {
    delete component.properties.width
    delete component.properties.height
  }

  // 通知父组件属性已更新
  emit('componentPropertyUpdate', component, 'width', undefined)
  emit('componentPropertyUpdate', component, 'height', undefined)

  contextMenu.value.visible = false
}

// 切换布局模式
const handleToggleLayoutMode = (component: CanvasComponent) => {
  const isCurrentlyFlow = component.layoutMode === 'flow'

  if (isCurrentlyFlow) {
    // 从流式布局切换回绝对定位
    component.layoutMode = 'absolute'
    // 设置默认位置（如果没有的话）
    if (component.x === undefined) component.x = 100
    if (component.y === undefined) component.y = 100

    console.log(`🔄 ${component.type}(${component.id}) 切换为绝对定位模式`)
  } else {
    // 从绝对定位切换为流式布局
    component.layoutMode = 'flow'

    // 对于文本组件，清除固定宽度以保持自适应
    if (component.type === 'text') {
      component.width = undefined
      if (component.properties) {
        delete component.properties.width
      }
      // 通知父组件宽度已重置
      emit('componentPropertyUpdate', component, 'width', undefined)
      console.log(`📏 ${component.type}(${component.id}) 切换为流式布局时重置宽度为自适应`)
    }

    console.log(`🔄 ${component.type}(${component.id}) 切换为流式布局模式`)
  }

  // 通知父组件布局模式已更新
  emit('componentPropertyUpdate', component, 'layoutMode', component.layoutMode)

  contextMenu.value.visible = false
}

// 点击其他地方隐藏右键菜单
const hideContextMenu = () => {
  contextMenu.value.visible = false
}

const handleCanvasClick = (event: MouseEvent) => {
  // 隐藏右键菜单
  hideContextMenu()

  // 检查点击的是否为画布区域（不是组件、页眉、页脚）
  const target = event.target as HTMLElement
  const isCanvas =
    target.classList.contains('canvas') || target.classList.contains('canvas-components')
  const isEmptyArea =
    target.classList.contains('canvas-empty') || target.classList.contains('empty-content')

  if (isCanvas || isEmptyArea) {
    // 点击画布空白区域取消选中
    emit('componentSelect', null)
  }
}

const handleToggleGrid = () => {
  emit('toggleGrid')
}

const handleClearCanvas = () => {
  emit('clearCanvas')
}

const handleHeaderSelect = () => {
  emit('headerSelect')
}

const handleFooterSelect = () => {
  emit('footerSelect')
}

// Vue.Draggable 事件处理
const isDraggingBusiness = ref(false) // 标记是否正在拖拽业务组件

const handleDragStart = () => {
  // 拖拽开始
  isDraggingBusiness.value = true
  console.log('🔄 Vue.Draggable 拖拽开始')
}

const handleDragEnd = () => {
  // 拖拽结束 - 短暂延迟避免与 click 事件冲突
  setTimeout(() => {
    isDraggingBusiness.value = false
    console.log('✅ Vue.Draggable 拖拽结束')
  }, 50) // 减少延迟时间，只是为了避免immediate的click事件
}

// Vue.Draggable 会自动处理排序，通过 v-model 的 setter 更新组件列表

// 处理组件属性更新
const handleComponentPropertyUpdate = (component: CanvasComponent, key: string, value: unknown) => {
  emit('componentPropertyUpdate', component, key, value)
}

// 基础组件拖拽开始处理
const handleBasicComponentDragStart = (event: MouseEvent, component: CanvasComponent) => {
  // 非桌面模式下禁用拖拽编辑
  if (props.deviceType !== 'desktop') {
    return
  }

  // 如果点击的是调整大小控制点，不开始拖拽
  const target = event.target as HTMLElement
  if (target.classList.contains('resize-handle')) {
    return
  }

  // 如果正在调整大小，不开始拖拽
  if (resizing.value) {
    return
  }

  event.preventDefault()
  event.stopPropagation()

  // 获取组件当前位置
  const componentElement = event.currentTarget as HTMLElement
  const rect = componentElement.getBoundingClientRect()

  // 获取画布容器位置
  const canvasComponentsElement = componentElement.closest('.canvas-components') as HTMLElement
  if (!canvasComponentsElement) return

  // 计算鼠标在组件内的偏移
  const offsetX = event.clientX - rect.left
  const offsetY = event.clientY - rect.top

  dragging.value = true
  dragData.value = {
    component,
    startX: event.clientX,
    startY: event.clientY,
    startComponentX: component.x || 0,
    startComponentY: component.y || 0,
    offsetX,
    offsetY
  }

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleBasicComponentDragMove)
  document.addEventListener('mouseup', handleBasicComponentDragEnd)

  // 设置拖拽样式
  document.body.style.cursor = 'move'
  document.body.style.userSelect = 'none'
}

// 基础组件拖拽移动处理
const handleBasicComponentDragMove = (event: MouseEvent) => {
  if (!dragging.value || !dragData.value) return

  const { component, startX, startY, startComponentX, startComponentY } = dragData.value

  // 计算鼠标移动距离
  const deltaX = event.clientX - startX
  const deltaY = event.clientY - startY

  // 计算新位置
  let newX = startComponentX + deltaX
  let newY = startComponentY + deltaY

  // 获取画布边界限制 - 使用.canvas而不是.canvas-components
  const canvasComponentsElement = document.querySelector('.canvas-components') as HTMLElement
  const canvasElement = document.querySelector('.canvas') as HTMLElement
  if (canvasElement && canvasComponentsElement && component) {
    // 获取组件宽高 - 使用更稳定的方式
    let componentWidth = component.width || 200
    let componentHeight = component.height || 100

    // 对于自适应组件，统一使用200px作为边界计算的默认宽度
    // 这确保了边界计算的一致性，不会因为组件类型而改变可拖拽范围
    if (!component.width) {
      componentWidth = 160 // 统一使用200px，保持边界计算一致性
    }

    if (!component.height) {
      // 根据组件类型设置合理的默认高度
      switch (component.type) {
        case 'title':
          componentHeight = 80
          break
        case 'text':
          componentHeight = 60
          break
        case 'button':
          componentHeight = 40
          break
        case 'image':
          componentHeight = 150
          break
        case 'video':
          componentHeight = 200
          break
        default:
          componentHeight = 100
          break
      }
    }

    // 获取画布容器的实际可用区域
    // 基础组件是相对于.canvas-components定位的，所以应该使用.canvas-components的宽度
    const canvasComponentsStyle = window.getComputedStyle(canvasComponentsElement)
    const canvasPadding = parseInt(canvasComponentsStyle.paddingLeft) || 20

    // 获取.canvas-components的实际DOM宽度（基础组件的坐标参考系）
    const canvasComponentsRect = canvasComponentsElement.getBoundingClientRect()
    const zoomFactor = props.zoom / 100

    // 实际可用宽度需要考虑缩放因子
    // 当缩放时，画布的逻辑坐标空间会发生变化
    const actualCanvasWidth = canvasComponentsRect.width / zoomFactor
    const availableWidth = actualCanvasWidth - (canvasPadding * 2) // 减去左右padding

    // 计算理论边界
    const leftBoundary = 0
    const rightBoundary = availableWidth - componentWidth

    // 贴靠阈值（像素）- 当组件接近边界时自动贴靠
    // 考虑缩放因子，当缩放较小时增大阈值
    const snapThreshold = Math.max(8, 12 / zoomFactor)

    // 水平边界限制 - 精确计算可用区域
    newX = Math.max(leftBoundary, Math.min(newX, rightBoundary))

    // 智能贴靠功能 - 增强版本
    const distanceToLeft = Math.abs(newX - leftBoundary)
    const distanceToRight = Math.abs(newX - rightBoundary)

    // 优先贴靠最近的边界
    if (distanceToLeft <= snapThreshold && distanceToLeft <= distanceToRight) {
      newX = leftBoundary // 贴靠左边界
      console.log(`🎯 贴靠到左边界 (距离: ${distanceToLeft.toFixed(1)}px)`)
    } else if (distanceToRight <= snapThreshold) {
      newX = rightBoundary // 贴靠右边界
      console.log(`🎯 贴靠到右边界 (距离: ${distanceToRight.toFixed(1)}px)`)
    }

    // 垂直边界限制 - 允许拖拽到底部，但会触发扩展
    newY = Math.max(0, newY)

    // 检查是否需要扩展画布高度
    const currentRequiredHeight = calculateRequiredCanvasHeight()
    const newComponentBottom = newY + componentHeight + 100 // 100px缓冲区

    if (newComponentBottom > currentRequiredHeight - 300) {
      // 如果组件接近当前画布底部，显示扩展提示
      canvasExpansionHint.value = {
        visible: true,
        type: 'bottom'
      }
      console.log(`📏 拖拽触发画布扩展: 组件底部 ${newComponentBottom}px, 当前高度 ${currentRequiredHeight}px`)
    } else {
      // 离开底部区域，隐藏提示
      canvasExpansionHint.value.visible = false
    }
    // 更新组件位置
    component.x = Math.round(newX)
    component.y = Math.round(newY)

    // 通知父组件位置已更新
    emit('componentPropertyUpdate', component, 'x', component.x)
    emit('componentPropertyUpdate', component, 'y', component.y)
  }
}

// 基础组件拖拽结束处理
const handleBasicComponentDragEnd = () => {
  if (!dragging.value) return

  dragging.value = false
  dragData.value = null

  // 隐藏画布扩展提示
  canvasExpansionHint.value.visible = false

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleBasicComponentDragMove)
  document.removeEventListener('mouseup', handleBasicComponentDragEnd)

  // 恢复样式
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

// 调整大小相关函数
const handleResizeStart = (event: MouseEvent, component: CanvasComponent, direction: string) => {
  // 非桌面模式下禁用调整大小
  if (props.deviceType !== 'desktop') {
    return
  }

  event.preventDefault()
  event.stopPropagation()

  // 获取组件当前的DOM元素
  const componentElement = document.querySelector(`[data-component-id="${component.id}"]`) as HTMLElement
  if (!componentElement) return

  // 获取画布容器边界
  const canvasElement = componentElement.closest('.canvas-components') as HTMLElement
  if (!canvasElement) return

  const canvasRect = canvasElement.getBoundingClientRect()
  const componentRect = componentElement.getBoundingClientRect()

  // 获取当前组件的实际尺寸
  // 如果组件有设定的宽高，使用设定值；否则使用合理的初始值
  const getInitialSize = (componentType: string, domWidth: number, domHeight: number) => {
    // 如果组件已有固定尺寸，直接返回
    if (component.width !== undefined && component.height !== undefined) {
      return { width: component.width, height: component.height }
    }

    // 对于自适应组件，设置合理的初始调整尺寸
    switch (componentType) {
      case 'text':
        return { width: Math.max(domWidth, 240), height: Math.max(domHeight, 60) }
      case 'title':
        return { width: Math.max(domWidth, 300), height: Math.max(domHeight, 80) }
      case 'button':
        return { width: Math.max(domWidth, 120), height: Math.max(domHeight, 40) }
      case 'image':
        return { width: Math.max(domWidth, 200), height: Math.max(domHeight, 150) }
      case 'video':
        return { width: Math.max(domWidth, 300), height: Math.max(domHeight, 200) }
      default:
        // 业务组件使用DOM尺寸
        return { width: domWidth, height: domHeight }
    }
  }

  const { width: currentWidth, height: currentHeight } = getInitialSize(
    component.type,
    componentRect.width,
    componentRect.height
  )

  // 计算组件在画布中的位置（用于边界计算）
  const componentLeft = componentRect.left - canvasRect.left
  const componentTop = componentRect.top - canvasRect.top

  // 获取画布的实际可用空间（减去padding）
  const canvasComputedStyle = window.getComputedStyle(canvasElement)
  const canvasPadding = parseInt(canvasComputedStyle.paddingLeft) || 20
  const availableWidth = canvasRect.width - (canvasPadding * 2)
  const availableHeight = canvasRect.height - (canvasPadding * 2)

  // 计算组件可以调整到的最大尺寸（基于当前位置）
  const maxWidthFromPosition = availableWidth - componentLeft + canvasPadding
  const maxHeightFromPosition = availableHeight - componentTop + canvasPadding

  resizing.value = true
  resizeData.value = {
    component,
    direction,
    startX: event.clientX,
    startY: event.clientY,
    startWidth: currentWidth,
    startHeight: currentHeight,
    maxWidth: maxWidthFromPosition,
    maxHeight: maxHeightFromPosition
  }

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)

  // 添加拖拽样式
  document.body.style.cursor = getCursorForDirection(direction)
  document.body.style.userSelect = 'none'
}

const handleResizeMove = (event: MouseEvent) => {
  if (!resizing.value || !resizeData.value || !resizeData.value.component) return

  const {
    component,
    direction,
    startX,
    startY,
    startWidth,
    startHeight,
    maxWidth = 1200,
    maxHeight = 800
  } = resizeData.value

  const deltaX = event.clientX - startX
  const deltaY = event.clientY - startY

  // 设置调整阈值，只有拖动超过10像素才生效
  const threshold = 10
  // 如果移动距离太小，不进行调整
  if (Math.abs(deltaX) < threshold && Math.abs(deltaY) < threshold) {
    return
  }

  let newWidth = startWidth
  let newHeight = startHeight

  // 根据拖拽方向计算新的尺寸
  switch (direction) {
    case 'se': // 右下角
      newWidth = startWidth + deltaX
      newHeight = startHeight + deltaY
      break
    case 'sw': // 左下角
      newWidth = startWidth - deltaX
      newHeight = startHeight + deltaY
      break
    case 'ne': // 右上角
      newWidth = startWidth + deltaX
      newHeight = startHeight - deltaY
      break
    case 'nw': // 左上角
      newWidth = startWidth - deltaX
      newHeight = startHeight - deltaY
      break
    case 'e': // 右边
      newWidth = startWidth + deltaX
      break
    case 'w': // 左边
      newWidth = startWidth - deltaX
      break
    case 's': // 下边
      newHeight = startHeight + deltaY
      break
    case 'n': // 上边
      newHeight = startHeight - deltaY
      break
  }

  // 应用边界限制 - 根据组件类型设置合理的最小尺寸
  const getMinSize = (componentType: string) => {
    switch (componentType) {
      case 'text':
        return { minWidth: 120, minHeight: 40 }
      case 'title':
        return { minWidth: 300, minHeight: 50 }
      case 'button':
        return { minWidth: 80, minHeight: 32 }
      case 'image':
        return { minWidth: 100, minHeight: 80 }
      case 'video':
        return { minWidth: 200, minHeight: 150 }
      default:
        // 业务组件或其他组件的默认最小尺寸
        return { minWidth: 200, minHeight: 100 }
    }
  }

  const { minWidth, minHeight } = getMinSize(component.type)

  // 限制最小尺寸
  newWidth = Math.max(minWidth, newWidth)
  newHeight = Math.max(minHeight, newHeight)

  // 精确的边界限制：基于组件当前位置计算可用空间
  // 使用调整开始时计算的精确边界值
  newWidth = Math.min(newWidth, maxWidth)
  newHeight = Math.min(newHeight, maxHeight)

  // 更新组件尺寸
  updateComponentSize(component, newWidth, newHeight)
}

const handleResizeEnd = () => {
  if (!resizing.value) return

  resizing.value = false
  resizeData.value = null

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)

  // 恢复样式
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

const getCursorForDirection = (direction: string): string => {
  const cursors: Record<string, string> = {
    'n': 'n-resize',
    's': 's-resize',
    'e': 'e-resize',
    'w': 'w-resize',
    'ne': 'ne-resize',
    'nw': 'nw-resize',
    'se': 'se-resize',
    'sw': 'sw-resize'
  }
  return cursors[direction] || 'default'
}

const updateComponentSize = (component: CanvasComponent, width: number, height: number) => {
  if (!component) return

  const newWidth = Math.round(width)
  const newHeight = Math.round(height)

  // 只有当尺寸真正发生变化时才更新
  if (component.width !== newWidth || component.height !== newHeight) {
    // 更新组件的宽高属性
    component.width = newWidth
    component.height = newHeight

    // 更新组件属性中的宽高
    if (!component.properties) {
      component.properties = {}
    }
    component.properties.width = component.width
    component.properties.height = component.height

    // 通知父组件属性已更新
    emit('componentPropertyUpdate', component, 'width', component.width)
    emit('componentPropertyUpdate', component, 'height', component.height)
  }
}

// 全局事件监听
const handleGlobalClick = (event: MouseEvent) => {
  // 如果点击的不是右键菜单，则隐藏菜单
  const target = event.target as HTMLElement
  if (!target.closest('.context-menu')) {
    hideContextMenu()
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  // ESC键隐藏右键菜单
  if (event.key === 'Escape') {
    hideContextMenu()
  }
}

// 组件挂载时添加全局监听
onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
  document.removeEventListener('keydown', handleKeyDown)
})

// 移除按钮拖拽相关代码，按钮组件回归正常流式布局
</script>

<template>
  <div class="canvas-area">
    <div class="canvas-header">
      <div class="canvas-title">
        <el-icon>
          <Refresh />
        </el-icon>
        设计画布
      </div>
      <div class="canvas-actions">
        <!-- 响应式预览提示 -->
        <div v-if="deviceType !== 'desktop'" class="responsive-preview-tip">
          <el-icon style="color: #409eff;">
            <Refresh />
          </el-icon>
          <span>{{ deviceType === 'tablet' ? '平板' : '手机' }}预览模式 - 仅查看效果</span>
        </div>

        <el-button :type="showGrid ? 'primary' : ''" @click="handleToggleGrid" size="small" text>
          <el-icon>
            <Refresh />
          </el-icon>
          网格
        </el-button>
        <el-button @click="handleClearCanvas" :icon="Refresh" size="small" text> 清空 </el-button>
      </div>
    </div>

    <div class="canvas-container">
      <div class="canvas" :class="{
        'show-grid': showGrid,
        'preview-mode': previewMode,
        [`device-${deviceType}`]: true,
      }" :style="canvasStyle" @drop="handleCanvasComponentDrop" @dragover="handleDragOver" @click="handleCanvasClick">
        <!-- 全局固定Header -->
        <div class="global-header" :class="{ selected: isHeaderSelected }" @click.stop="handleHeaderSelect"
          v-if="pageHeader">
          <component :is="widgetMap.defaultHeader" v-bind="pageHeader" :key="`global-header-${pageHeader.title}`" />
        </div>

        <!-- 空状态提示 -->
        <div v-if="canvasComponents.length === 0" class="canvas-empty">
          <div class="empty-content">
            <el-icon style="width: 48px; height: 48px; color: #c0c4cc">
              <Refresh />
            </el-icon>
            <h4>从左侧组件库拖拽组件到此处开始设计</h4>
            <p>支持拖拽、编辑、预览等功能</p>
          </div>
        </div>

        <!-- 画布组件渲染区域 -->
        <div v-else class="canvas-components" ref="canvasComponentsRef" :style="{ minHeight: dynamicCanvasHeight }">
          <!-- 流式布局组件 - 支持垂直流式布局和拖拽排序 -->
          <draggable v-model="flowLayoutComponentsList" :disabled="previewMode" :animation="150" ghost-class="ghost"
            chosen-class="chosen" drag-class="drag" @start="handleDragStart" @end="handleDragEnd" item-key="id"
            class="flow-layout-components-container">
            <template #item="{ element: component }">
              <div class="canvas-component component-handle flow-layout-component" :class="{
                'business-component': isBusinessComponent(component),
                'basic-component-flow': !isBusinessComponent(component),
                selected: selectedComponent?.id === component.id,
                'dragging-business': isDraggingBusiness
              }" :data-component-id="component.id" @click="handleComponentSelect(component)"
                @contextmenu="handleComponentRightClick($event, component)">
                <!-- 动态组件渲染 -->
                <div class="component-wrapper">
                  <component :is="widgetMap[component.type] || 'div'" v-bind="component.properties || {}"
                    :key="`${component.id}_${component.type}`" v-if="widgetMap[component.type]"
                    @update:src="(value: string) => handleComponentPropertyUpdate(component, 'src', value)"
                    @update:videoUrl="(value: string) => handleComponentPropertyUpdate(component, 'videoUrl', value)" />
                  <!-- 组件不存在时的占位符 -->
                  <div v-else class="component-placeholder">
                    <el-icon style="width: 24px; height: 24px; color: #f56c6c">
                      <Delete />
                    </el-icon>
                    <span>组件类型 "{{ component.type }}" 不存在</span>
                  </div>
                </div>

                <!-- 右键菜单 -->
                <div v-if="!previewMode && contextMenu.visible && contextMenu.componentId === component.id"
                  class="context-menu" :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }" @click.stop>
                  <!-- 基础组件显示布局模式切换选项（按钮组件除外） -->
                  <div v-if="!isBusinessComponent(component) && component.type !== 'button'" class="context-menu-item"
                    @click="handleToggleLayoutMode(component)">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    <span>{{ component.layoutMode === 'flow' ? '切换为自由定位' : '切换为流式布局' }}</span>
                  </div>
                  <!-- 基础组件在流式布局模式下不显示重置选项 -->
                  <div
                    v-if="!isBusinessComponent(component) && component.layoutMode !== 'flow' && (component.width || component.height)"
                    class="context-menu-item" @click="handleResetToAdaptive(component)">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    <span>重置</span>
                  </div>
                  <div class="context-menu-item" @click="handleContextMenuDelete(component)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                    <span>删除组件</span>
                  </div>
                </div>
              </div>
            </template>
          </draggable>

          <!-- 绝对定位组件 - 自由布局 -->
          <div class="absolute-position-components-container">
            <div v-for="component in absolutePositionComponents" :key="component.id"
              class="canvas-component component-handle basic-component" :class="{
                selected: selectedComponent?.id === component.id,
                resizing: resizing && resizeData?.component?.id === component.id,
                dragging: dragging && dragData?.component?.id === component.id
              }" :data-component-id="component.id" @click="handleComponentSelect(component)"
              @contextmenu="handleComponentRightClick($event, component)"
              @mousedown="handleBasicComponentDragStart($event, component)" :style="(() => {
                const isDesktop = deviceType === 'desktop'
                if (isDesktop) {
                  // 桌面模式：使用原始像素值，支持拖拽编辑
                  return {
                    position: 'absolute',
                    left: component.x + 'px',
                    top: component.y + 'px',
                    width: component.width ? `${component.width}px` : '200px',
                    height: component.height ? `${component.height}px` : 'auto',
                    zIndex: selectedComponent?.id === component.id ? 10 : (component.type === 'button' ? 5 : 1)
                  }
                } else {
                  // 平板/手机模式：使用响应式缩放
                  const responsive = convertToResponsiveUnitsForCanvas(component)
                  return {
                    position: 'absolute',
                    left: responsive.left + 'px',
                    top: responsive.top + 'px',
                    width: responsive.width + 'px',
                    height: responsive.height === 'auto' ? 'auto' : responsive.height + 'px',
                    zIndex: selectedComponent?.id === component.id ? 10 : (component.type === 'button' ? 5 : 1)
                  }
                }
              })()">
              <!-- 动态组件渲染 -->
              <div class="component-wrapper">
                <component :is="widgetMap[component.type] || 'div'" v-bind="component.properties || {}"
                  :key="`${component.id}_${component.type}`" v-if="widgetMap[component.type]"
                  @update:src="(value: string) => handleComponentPropertyUpdate(component, 'src', value)"
                  @update:videoUrl="(value: string) => handleComponentPropertyUpdate(component, 'videoUrl', value)" />
                <!-- 组件不存在时的占位符 -->
                <div v-else class="component-placeholder">
                  <el-icon style="width: 24px; height: 24px; color: #f56c6c">
                    <Delete />
                  </el-icon>
                  <span>组件类型 "{{ component.type }}" 不存在</span>
                </div>
              </div>

              <!-- 右键菜单 -->
              <div v-if="!previewMode && contextMenu.visible && contextMenu.componentId === component.id"
                class="context-menu" :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }" @click.stop>
                <!-- 基础组件显示布局模式切换选项（按钮组件除外） -->
                <div v-if="component.type !== 'button'" class="context-menu-item"
                  @click="handleToggleLayoutMode(component)">
                  <el-icon>
                    <Refresh />
                  </el-icon>
                  <span>{{ component.layoutMode === 'flow' ? '切换为自由定位' : '切换为流式布局' }}</span>
                </div>
                <!-- 基础组件在绝对定位模式下显示重置选项 -->
                <div v-if="component.layoutMode !== 'flow' && (component.width || component.height)"
                  class="context-menu-item" @click="handleResetToAdaptive(component)">
                  <el-icon>
                    <Refresh />
                  </el-icon>
                  <span>重置</span>
                </div>
                <div class="context-menu-item" @click="handleContextMenuDelete(component)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  <span>删除组件</span>
                </div>
              </div>

              <!-- 选中状态指示器 - 避免与内容重叠 -->
              <div v-if="!previewMode && selectedComponent?.id === component.id" class="selection-indicator">
                <div class="selection-label">{{ component.name || component.type }}</div>
              </div>

              <!-- 调整大小控制点 - 仅对基础组件显示，且仅在桌面模式 -->
              <div v-if="!previewMode && selectedComponent?.id === component.id && deviceType === 'desktop'"
                class="resize-controls">
                <!-- 四个角的控制点 -->
                <div class="resize-handle resize-nw" @mousedown="handleResizeStart($event, component, 'nw')"></div>
                <div class="resize-handle resize-ne" @mousedown="handleResizeStart($event, component, 'ne')"></div>
                <div class="resize-handle resize-sw" @mousedown="handleResizeStart($event, component, 'sw')"></div>
                <div class="resize-handle resize-se" @mousedown="handleResizeStart($event, component, 'se')"></div>
                <!-- 四个边的控制点 -->
                <div class="resize-handle resize-n" @mousedown="handleResizeStart($event, component, 'n')"></div>
                <div class="resize-handle resize-s" @mousedown="handleResizeStart($event, component, 's')"></div>
                <div class="resize-handle resize-w" @mousedown="handleResizeStart($event, component, 'w')"></div>
                <div class="resize-handle resize-e" @mousedown="handleResizeStart($event, component, 'e')"></div>
              </div>
            </div>
          </div>

          <!-- 画布扩展提示 -->
          <div v-if="canvasExpansionHint.visible && !previewMode" class="canvas-expansion-hint">
            <div class="expansion-hint-content">
              <el-icon>
                <Refresh />
              </el-icon>
              <span>画布将自动扩展以容纳组件</span>
            </div>
          </div>
        </div>

        <!-- 固定Footer -->
        <div class="fixed-footer" :class="{ selected: isFooterSelected }" @click.stop="handleFooterSelect">
          <component :is="widgetMap.footer" v-bind="pageFooter" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fafbfc;
  min-height: 0;
}

.canvas-header {
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #f0f2f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.canvas-title {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.canvas-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 响应式预览提示 */
.responsive-preview-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #e1f3ff;
  border: 1px solid #409eff;
  border-radius: 4px;
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
  margin-right: 12px;
}

.canvas-container {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 0;
}

.canvas {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  min-height: calc(100vh - 40px);
  width: 100%;
  max-width: 1200px;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: auto;
}

.global-header {
  position: relative;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.global-header:hover {
  opacity: 0.8;
}

.global-header.selected {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

.global-header :deep(.default-header) {
  pointer-events: none;
  user-select: none;
}

.canvas.show-grid {
  background-image:
    linear-gradient(rgba(64, 158, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(64, 158, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.canvas.device-mobile {
  max-width: 375px;
  min-height: 667px;
  width: 375px;
  /* 移动端画布特殊样式 */
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.canvas.device-tablet {
  max-width: 768px;
  min-height: 1024px;
  width: 768px;
  /* 平板画布特殊样式 */
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* 基础组件容器的响应式调整 */
.canvas.device-mobile .basic-components-container,
.canvas.device-tablet .basic-components-container {
  /* 在非桌面模式下，调整容器以适应缩放内容 */
  overflow: hidden;
}

/* 非桌面模式下禁用组件的悬停效果 */
.canvas.device-mobile .canvas-component.basic-component:hover,
.canvas.device-tablet .canvas-component.basic-component:hover {
  border-color: transparent;
  background: transparent;
  transform: none;
}

/* 非桌面模式下的选中效果简化 */
.canvas.device-mobile .canvas-component.basic-component.selected,
.canvas.device-tablet .canvas-component.basic-component.selected {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  transform: none;
}

.canvas.preview-mode {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.canvas-empty {
  flex: 1;
  min-height: 400px;
  padding-bottom: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

.empty-content {
  text-align: center;
  color: #909399;
}

.empty-content h4 {
  margin: 16px 0 8px 0;
  color: #606266;
  font-weight: 500;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
}

.canvas-components {
  padding: 20px;
  padding-bottom: 200px;
  flex: 1;
  position: relative;
  overflow: visible;
  min-height: 800px;
  /* 确保有足够的空间进行绝对定位 */

  /* 动态高度过渡效果 */
  transition: min-height 0.3s ease;
}

/* 流式布局组件容器 - 垂直流式布局，支持业务组件和基础组件 */
.flow-layout-components-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 40px;
  /* 为绝对定位组件留出空间 */
}

/* 保持向后兼容 */
.business-components-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 40px;
}

/* 绝对定位组件容器 - 自由布局容器 */
.absolute-position-components-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  /* 允许点击穿透到背景 */
}

.absolute-position-components-container>.canvas-component {
  pointer-events: auto;
  /* 恢复组件本身的点击事件 */
}

/* 保持向后兼容 */
.basic-components-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.basic-components-container>.canvas-component {
  pointer-events: auto;
}

/* 组件容器基础样式 */
.canvas-component {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-height: 50px;
  width: 100%;
  user-select: none;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  align-items: stretch;
}

/* 业务组件样式 - 保持原有透明边框 */
.canvas-component.business-component {
  border: 2px dashed transparent;
  border-radius: 6px;
  background: transparent;
  box-shadow: none;
}

/* 基础组件样式 - 简化为虚线框 */
.canvas-component.basic-component {
  border: 2px dashed transparent;
  border-radius: 6px;
  background: transparent;
  box-shadow: none;
}

/* 流式布局中的基础组件样式 */
.canvas-component.basic-component-flow {
  border: 2px dashed transparent;
  border-radius: 6px;
  background: transparent;
  box-shadow: none;
  /* 在流式布局中显示为块级元素 */
  display: block;
  width: 100%;
  position: relative;
}

.canvas-component[draggable='true'] {
  cursor: grab;
}

.canvas-component[draggable='true']:active {
  cursor: grabbing;
}

/* 业务组件悬停和选中状态 - 保持原有样式 */
.canvas-component.business-component:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

/* 拖拽活跃期间不显示悬停效果 */
.canvas-component.business-component.dragging-business:hover {
  border-color: transparent;
  background: transparent;
}

.canvas-component.business-component.selected {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.08);
  box-shadow:
    0 0 0 3px rgba(64, 158, 255, 0.3),
    0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
  position: relative;
  z-index: 5;
}

/* 基础组件悬停和选中状态 - 与业务组件一致 */
.canvas-component.basic-component:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.canvas-component.basic-component.selected {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.08);
  box-shadow:
    0 0 0 3px rgba(64, 158, 255, 0.3),
    0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
  position: relative;
  z-index: 5;
}

/* 流式布局基础组件悬停和选中状态 */
.canvas-component.basic-component-flow:hover {
  border-color: #67c23a;
  background: rgba(103, 194, 58, 0.05);
}

.canvas-component.basic-component-flow.selected {
  border-color: #67c23a;
  background: rgba(103, 194, 58, 0.08);
  box-shadow:
    0 0 0 3px rgba(103, 194, 58, 0.3),
    0 4px 12px rgba(103, 194, 58, 0.15);
  transform: translateY(-2px);
  position: relative;
  z-index: 5;
}

/* 正在调整大小时的特殊状态 - 简化版 */
.canvas-component.basic-component.resizing {
  border-color: #67c23a;
  background: rgba(103, 194, 58, 0.05);
}

.canvas-component.basic-component.resizing .resize-handle {
  background: #67c23a;
}

.canvas-component.basic-component.resizing .selection-label {
  background: #67c23a;
}

.canvas-component.basic-component.resizing .selection-label::after {
  border-top-color: #67c23a;
}

/* 正在拖拽时的特殊状态 */
.canvas-component.basic-component.dragging {
  border-color: #f56c6c;
  background: rgba(245, 108, 108, 0.05);
  box-shadow:
    0 0 0 3px rgba(245, 108, 108, 0.3),
    0 8px 24px rgba(245, 108, 108, 0.2);
  transform: translateY(-4px);
  z-index: 1000 !important;
}

.canvas-component.basic-component.dragging .selection-label {
  background: #f56c6c;
}

.canvas-component.basic-component.dragging .selection-label::after {
  border-top-color: #f56c6c;
}

/* Vue.Draggable 样式 */
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
  border: 2px dashed #409eff;
}

.chosen {
  background: rgba(64, 158, 255, 0.1);
  border-color: #409eff;
}

.drag {
  opacity: 0.8;
  transform: rotate(5deg);
}

.component-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  background: #fafbfc;
  border-radius: 4px;
  border: 1px dashed #e4e7ed;
}

.component-placeholder span {
  margin-top: 8px;
  font-size: 14px;
}

/* 右键菜单样式 */
.context-menu {
  position: absolute;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border: 1px solid #e4e7ed;
  padding: 4px 0;
  min-width: 120px;
  z-index: 1000;
  animation: contextMenuFadeIn 0.15s ease-out;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
}

.context-menu-item:hover {
  background: #f5f7fa;
  color: #f56c6c;
}

.context-menu-item .el-icon {
  font-size: 16px;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-4px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 选中状态指示器 */
.selection-indicator {
  position: absolute;
  top: -32px;
  left: 0;
  z-index: 12;
  pointer-events: none;
}

.selection-label {
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  position: relative;
}

.selection-label::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 8px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #409eff;
}

/* 组件包装器基础样式 */
.component-wrapper {
  /* 允许点击事件传播到父级，以便选中组件 */
  pointer-events: auto;
  position: relative;
}

/* 业务组件包装器 - 保持原有样式 */
.business-component .component-wrapper {
  width: 100%;
  height: auto;
  display: block;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 基础组件包装器 - 新的布局样式 */
.basic-component .component-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: stretch;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 滚动条样式 */
.canvas-container::-webkit-scrollbar {
  width: 8px;
}

.canvas-container::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 4px;
}

.canvas-container::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.canvas-container::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

.canvas-container::-webkit-scrollbar-thumb:active {
  background: #606266;
}

/* Footer样式 */
.fixed-footer {
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 20;
  background: white;
  border-top: 1px solid #e4e7ed;
}

.fixed-footer:hover {
  opacity: 0.8;
}

.fixed-footer.selected {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

/* 移除按钮组件特殊样式，回归正常布局 */

/* 调整大小控制点样式 */
.resize-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.resize-handle {
  position: absolute;
  background: #409eff;
  border: 2px solid white;
  border-radius: 50%;
  pointer-events: auto;
  z-index: 11;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.resize-handle:hover {
  background: #66b1ff;
  transform: scale(1.3);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.5);
}

/* 角落控制点 - 增大点击区域，改善操作体验 */
.resize-nw {
  width: 12px;
  height: 12px;
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.resize-ne {
  width: 12px;
  height: 12px;
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.resize-sw {
  width: 12px;
  height: 12px;
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.resize-se {
  width: 12px;
  height: 12px;
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

/* 边缘控制点 - 增大点击区域 */
.resize-n {
  width: 24px;
  height: 6px;
  top: -3px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
  border-radius: 3px;
}

.resize-s {
  width: 24px;
  height: 6px;
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
  border-radius: 3px;
}

.resize-w {
  width: 6px;
  height: 24px;
  left: -3px;
  top: 50%;
  transform: translateY(-50%);
  cursor: w-resize;
  border-radius: 3px;
}

.resize-e {
  width: 6px;
  height: 24px;
  right: -3px;
  top: 50%;
  transform: translateY(-50%);
  cursor: e-resize;
  border-radius: 3px;
}

/* 拖拽时的选中状态 */
.canvas-component.resizing {
  opacity: 0.8;
}

/* 画布扩展提示 */
.canvas-expansion-hint {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  pointer-events: none;
}

.expansion-hint-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(103, 194, 58, 0.95);
  color: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
  backdrop-filter: blur(10px);
  animation: expansionHintPulse 2s ease-in-out infinite;
}

.expansion-hint-content .el-icon {
  font-size: 18px;
  animation: spin 1s linear infinite;
}

@keyframes expansionHintPulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 0.95;
  }

  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 移除canvas-components滚动条样式，因为滚动现在发生在canvas-container */
</style>
