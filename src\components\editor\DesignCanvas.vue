<script setup lang="ts">
import { computed, nextTick, watch, ref } from 'vue'

import { Refresh, Edit, Delete } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'

// 导入类型
import type { CanvasComponent, ComponentItem } from '../../types/editor'

// 导入组件映射
import { widgetMap } from '../widgets/index'

// 调试：检查组件映射
console.log('Available widgets:', Object.keys(widgetMap))

// 定义组件属性
interface Props {
    canvasComponents: CanvasComponent[]
    selectedComponent: CanvasComponent | null
    previewMode: boolean
    showGrid: boolean
    deviceType: 'desktop' | 'tablet' | 'mobile'
    zoom: number
    pageSettings?: {
        width: string
        height: string
        backgroundColor: string
        title: string
    }
    pageHeader?: {
        title: string
        backgroundColor: string
        textColor: string
        height: string
        fontSize: string
        fontWeight: string
    }
    pageFooter?: {
        contactName: string
        contactPhone: string
        address: string
        copyrightInfo: string
        licenseInfo: string
        backgroundColor: string
        textColor: string
    }
    isHeaderSelected?: boolean
    isFooterSelected?: boolean
}

// 定义组件事件
interface Emits {
    (e: 'componentDrop', component: ComponentItem, event: DragEvent): void
    (e: 'componentSelect', component: CanvasComponent | null): void
    (e: 'componentDelete', component: CanvasComponent): void
    (e: 'componentReorder', fromIndex: number, toIndex: number): void
    (e: 'toggleGrid'): void
    (e: 'clearCanvas'): void
    (e: 'headerSelect'): void
    (e: 'footerSelect'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 画布容器引用
const canvasComponentsRef = ref<HTMLElement>()

// 计算画布样式
const canvasStyle = computed(() => {
    const baseStyle = {
        zoom: `${props.zoom}%`,
        maxWidth: props.deviceType === 'mobile' ? '375px' :
            props.deviceType === 'tablet' ? '768px' : '100%'
    }

    // 合并页面设置
    if (props.pageSettings) {
        return {
            ...baseStyle,
            width: props.pageSettings.width,
            height: props.pageSettings.height,
            backgroundColor: props.pageSettings.backgroundColor
        }
    }

    return baseStyle
})

// 自动滚动到选中组件
const scrollToSelectedComponent = async (component: CanvasComponent) => {
    if (!canvasComponentsRef.value) return

    await nextTick()

    // 查找组件对应的DOM元素
    const componentElement = canvasComponentsRef.value.querySelector(`[data-component-id="${component.id}"]`) as HTMLElement
    if (!componentElement) return

    // 获取容器和元素的位置信息
    const container = canvasComponentsRef.value
    const containerRect = container.getBoundingClientRect()
    const elementRect = componentElement.getBoundingClientRect()

    // 计算元素是否在可视区域内（增加一些容差）
    const tolerance = 50
    const isVisible = elementRect.top >= containerRect.top - tolerance &&
        elementRect.bottom <= containerRect.bottom + tolerance

    // 如果不在可视区域内，滚动到元素位置
    if (!isVisible) {
        // 计算目标滚动位置，让组件出现在容器中央
        const elementTop = componentElement.offsetTop
        const containerHeight = container.clientHeight
        const elementHeight = elementRect.height
        const scrollOffset = 100 // 额外的偏移量，确保组件不会太靠近边缘

        const targetScrollTop = elementTop - (containerHeight / 2) + (elementHeight / 2) - scrollOffset

        container.scrollTo({
            top: Math.max(0, targetScrollTop),
            behavior: 'smooth'
        })
    }
}

// 监听选中组件变化
watch(() => props.selectedComponent, (newComponent) => {
    if (newComponent) {
        scrollToSelectedComponent(newComponent)
    }
}, { immediate: false })

// 事件处理
const handleCanvasComponentDrop = (event: DragEvent) => {
    event.preventDefault()

    // 获取拖拽的组件数据
    const componentData = event.dataTransfer?.getData('application/json')
    if (componentData) {
        try {
            const component = JSON.parse(componentData)
            console.log('组件放置到画布:', component)
            emit('componentDrop', component, event)
        } catch (error) {
            console.error('解析组件数据失败:', error)
        }
    }
}

const handleDragOver = (event: DragEvent) => {
    event.preventDefault()
    event.dataTransfer!.dropEffect = 'copy'
}

const handleComponentSelect = (component: CanvasComponent) => {
    console.log('DesignCanvas handleComponentSelect 被调用:', component)
    emit('componentSelect', component)
}

const handleComponentDelete = (component: CanvasComponent) => {
    emit('componentDelete', component)
}

const handleCanvasClick = (event: MouseEvent) => {
    console.log('handleCanvasClick 被调用, target:', event.target)
    // 检查点击的是否为画布区域（不是组件、页眉、页脚）
    const target = event.target as HTMLElement
    const isCanvas = target.classList.contains('canvas') || target.classList.contains('canvas-components')
    const isEmptyArea = target.classList.contains('canvas-empty') || target.classList.contains('empty-content')

    console.log('点击目标分析:', {
        className: target.className,
        isCanvas,
        isEmptyArea
    })

    if (isCanvas || isEmptyArea) {
        // 点击画布空白区域取消选中
        console.log('点击画布空白区域，取消选中')
        emit('componentSelect', null)
    }
}

const handleToggleGrid = () => {
    emit('toggleGrid')
}

const handleClearCanvas = () => {
    emit('clearCanvas')
}

const handleHeaderSelect = () => {
    emit('headerSelect')
}

const handleFooterSelect = () => {
    emit('footerSelect')
}

// Vue.Draggable 事件处理
const handleDragStart = (evt: { oldIndex: number; newIndex: number }) => {
    console.log('拖拽开始:', evt)
}

const handleDragEnd = (evt: { oldIndex: number; newIndex: number }) => {
    console.log('拖拽结束:', evt)
}

const handleChange = (evt: { moved?: { oldIndex: number; newIndex: number } }) => {
    console.log('拖拽变化:', evt)

    if (evt.moved) {
        const { oldIndex, newIndex } = evt.moved
        console.log('组件移动:', oldIndex, '->', newIndex)
        emit('componentReorder', oldIndex, newIndex)
    }
}


</script>

<template>
    <div class="canvas-area">
        <div class="canvas-header">
            <div class="canvas-title">
                <el-icon>
                    <Refresh />
                </el-icon>
                设计画布
            </div>
            <div class="canvas-actions">
                <el-button :type="showGrid ? 'primary' : ''" @click="handleToggleGrid" size="small" text>
                    <el-icon>
                        <Refresh />
                    </el-icon>
                    网格
                </el-button>
                <el-button @click="handleClearCanvas" :icon="Refresh" size="small" text>
                    清空
                </el-button>
            </div>
        </div>

        <div class="canvas-container">
            <div class="canvas" :class="{
                'show-grid': showGrid,
                'preview-mode': previewMode,
                [`device-${deviceType}`]: true
            }" :style="canvasStyle" @drop="handleCanvasComponentDrop" @dragover="handleDragOver"
                @click="handleCanvasClick">
                <!-- 全局固定Header -->
                <div class="global-header" :class="{ 'selected': isHeaderSelected }" @click.stop="handleHeaderSelect"
                    v-if="pageHeader">
                    <component :is="widgetMap.defaultHeader" v-bind="pageHeader"
                        :key="`global-header-${pageHeader.title}`" />
                </div>

                <!-- 空状态提示 -->
                <div v-if="canvasComponents.length === 0" class="canvas-empty">
                    <div class="empty-content">
                        <el-icon style="width: 48px; height: 48px; color: #c0c4cc;">
                            <Refresh />
                        </el-icon>
                        <h4>从左侧组件库拖拽组件到此处开始设计</h4>
                        <p>支持拖拽、编辑、预览等功能</p>
                    </div>
                </div>

                <!-- 画布组件渲染区域 -->
                <div v-else class="canvas-components" ref="canvasComponentsRef">
                    <draggable :list="canvasComponents" :disabled="previewMode" :animation="150" ghost-class="ghost"
                        chosen-class="chosen" drag-class="drag" @start="handleDragStart" @end="handleDragEnd"
                        @change="handleChange" item-key="id">
                        <template #item="{ element: component }">
                            <div class="canvas-component component-handle"
                                :class="{ 'selected': selectedComponent?.id === component.id }"
                                :data-component-id="component.id" @click="handleComponentSelect(component)">
                                <!-- 动态组件渲染 -->
                                <div class="component-wrapper">
                                    <component :is="widgetMap[component.type] || 'div'"
                                        v-bind="component.properties || {}" :key="`${component.id}_${component.type}`"
                                        v-if="widgetMap[component.type]" />
                                    <!-- 组件不存在时的占位符 -->
                                    <div v-else class="component-placeholder">
                                        <el-icon style="width: 24px; height: 24px; color: #f56c6c;">
                                            <Delete />
                                        </el-icon>
                                        <span>组件类型 "{{ component.type }}" 不存在</span>
                                    </div>
                                </div>

                                <!-- 组件操作按钮 -->
                                <div v-if="!previewMode" class="component-actions">
                                    <el-button size="small" circle>
                                        <el-icon>
                                            <Edit />
                                        </el-icon>
                                    </el-button>
                                    <el-button size="small" circle type="danger"
                                        @click.stop="handleComponentDelete(component)">
                                        <el-icon>
                                            <Delete />
                                        </el-icon>
                                    </el-button>
                                </div>
                            </div>
                        </template>
                    </draggable>
                </div>

                <!-- 固定Footer -->
                <div class="fixed-footer" :class="{ 'selected': isFooterSelected }" @click.stop="handleFooterSelect">
                    <component :is="widgetMap.footer" v-bind="pageFooter" />
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.canvas-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fafbfc;
    min-height: 0;
}

.canvas-header {
    padding: 12px 20px;
    background: white;
    border-bottom: 1px solid #f0f2f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.canvas-title {
    font-size: 14px;
    color: #303133;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.canvas-actions {
    display: flex;
    gap: 8px;
}

.canvas-container {
    flex: 1;
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    min-height: 0;
}

.canvas {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    min-height: calc(100vh - 40px);
    width: 100%;
    max-width: 1200px;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: auto;
}

.global-header {
    position: relative;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.global-header:hover {
    opacity: 0.8;
}

.global-header.selected {
    outline: 2px solid #409eff;
    outline-offset: 2px;
}

.global-header :deep(.default-header) {
    pointer-events: none;
    user-select: none;
}

.canvas.show-grid {
    background-image:
        linear-gradient(rgba(64, 158, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(64, 158, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

.canvas.device-mobile {
    max-width: 375px;
    min-height: 667px;
    width: 375px;
}

.canvas.device-tablet {
    max-width: 768px;
    min-height: 1024px;
    width: 768px;
}

.canvas.preview-mode {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.canvas-empty {
    flex: 1;
    min-height: 400px;
    padding-bottom: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: visible;
}

.empty-content {
    text-align: center;
    color: #909399;
}

.empty-content h4 {
    margin: 16px 0 8px 0;
    color: #606266;
    font-weight: 500;
}

.empty-content p {
    margin: 0;
    font-size: 14px;
}

.canvas-components {
    padding: 20px;
    padding-bottom: 200px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: visible;
    min-height: 0;
}

.canvas-component {
    position: relative;
    border: 2px dashed transparent;
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    min-height: 50px;
    width: 100%;
    user-select: none;
}

.canvas-component[draggable="true"] {
    cursor: grab;
}

.canvas-component[draggable="true"]:active {
    cursor: grabbing;
}

.canvas-component:hover {
    border-color: #409eff;
    background: rgba(64, 158, 255, 0.05);
}

.canvas-component.selected {
    border-color: #409eff;
    background: rgba(64, 158, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.3), 0 4px 12px rgba(64, 158, 255, 0.15);
    transform: translateY(-2px);
}

/* Vue.Draggable 样式 */
.ghost {
    opacity: 0.5;
    background: #c8ebfb;
    border: 2px dashed #409eff;
}

.chosen {
    background: rgba(64, 158, 255, 0.1);
    border-color: #409eff;
}

.drag {
    opacity: 0.8;
    transform: rotate(5deg);
}

.component-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #909399;
    background: #fafbfc;
    border-radius: 4px;
    border: 1px dashed #e4e7ed;
}

.component-placeholder span {
    margin-top: 8px;
    font-size: 14px;
}

.component-actions {
    position: absolute;
    top: -16px;
    right: -16px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.canvas-component:hover .component-actions,
.canvas-component.selected .component-actions {
    opacity: 1;
}

.component-wrapper {
    /* 允许点击事件传播到父级，以便选中组件 */
    pointer-events: auto;
    position: relative;
}

/* 滚动条样式 */
.canvas-container::-webkit-scrollbar {
    width: 8px;
}

.canvas-container::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 4px;
}

.canvas-container::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.canvas-container::-webkit-scrollbar-thumb:hover {
    background: #909399;
}

.canvas-container::-webkit-scrollbar-thumb:active {
    background: #606266;
}

/* Footer样式 */
.fixed-footer {
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 20;
    background: white;
    border-top: 1px solid #e4e7ed;
}

.fixed-footer:hover {
    opacity: 0.8;
}

.fixed-footer.selected {
    outline: 2px solid #409eff;
    outline-offset: 2px;
}

/* 移除canvas-components滚动条样式，因为滚动现在发生在canvas-container */
</style>