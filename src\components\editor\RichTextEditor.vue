<template>
  <div class="rich-text-editor">
    <div class="editor-toolbar">
      <el-button-group>
        <el-button size="small" @click="execCommand('bold')" :type="isActive('bold') ? 'primary' : ''">
          <Icon icon="material-symbols:format-bold" style="width: 14px; height: 14px" />
        </el-button>
        <el-button size="small" @click="execCommand('italic')" :type="isActive('italic') ? 'primary' : ''">
          <Icon icon="material-symbols:format-italic" style="width: 14px; height: 14px" />
        </el-button>
        <el-button size="small" @click="execCommand('underline')" :type="isActive('underline') ? 'primary' : ''">
          <Icon icon="material-symbols:format-underlined" style="width: 14px; height: 14px" />
        </el-button>
      </el-button-group>

      <el-button-group>
        <el-button size="small" @click="execCommand('insertUnorderedList')"
          :type="isActive('insertUnorderedList') ? 'primary' : ''">
          <Icon icon="material-symbols:format-list-bulleted" style="width: 14px; height: 14px" />
        </el-button>
        <el-button size="small" @click="execCommand('insertOrderedList')"
          :type="isActive('insertOrderedList') ? 'primary' : ''">
          <Icon icon="material-symbols:format-list-numbered" style="width: 14px; height: 14px" />
        </el-button>
      </el-button-group>

      <el-button-group>
        <el-button size="small" @click="execCommand('justifyLeft')" :type="isActive('justifyLeft') ? 'primary' : ''">
          <Icon icon="material-symbols:format-align-left" style="width: 14px; height: 14px" />
        </el-button>
        <el-button size="small" @click="execCommand('justifyCenter')"
          :type="isActive('justifyCenter') ? 'primary' : ''">
          <Icon icon="material-symbols:format-align-center" style="width: 14px; height: 14px" />
        </el-button>
        <el-button size="small" @click="execCommand('justifyRight')" :type="isActive('justifyRight') ? 'primary' : ''">
          <Icon icon="material-symbols:format-align-right" style="width: 14px; height: 14px" />
        </el-button>
      </el-button-group>
    </div>

    <div ref="editorRef" class="editor-content" contenteditable="true" :placeholder="placeholder" @input="handleInput"
      @blur="handleBlur" v-html="modelValue"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { Icon } from '@iconify/vue'

interface Props {
  modelValue?: string
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const editorRef = ref<HTMLElement>()

// 执行编辑器命令
const execCommand = (command: string, value?: string) => {
  document.execCommand(command, false, value)
  editorRef.value?.focus()
  handleInput()
}

// 检查命令是否激活
const isActive = (command: string): boolean => {
  return document.queryCommandState(command)
}

// 处理输入
const handleInput = () => {
  if (editorRef.value) {
    emit('update:modelValue', editorRef.value.innerHTML)
  }
}

// 处理失焦
const handleBlur = () => {
  if (editorRef.value) {
    emit('update:modelValue', editorRef.value.innerHTML)
  }
}

// 组件挂载后设置初始内容
onMounted(() => {
  nextTick(() => {
    if (editorRef.value && props.modelValue) {
      editorRef.value.innerHTML = props.modelValue
    }
  })
})
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  padding: 8px 12px;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.editor-content {
  min-height: 120px;
  padding: 12px;
  outline: none;
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
}

.editor-content:empty:before {
  content: attr(placeholder);
  color: #c0c4cc;
  pointer-events: none;
}

.editor-content:focus {
  background: #ffffff;
}

/* 富文本内容样式 */
.editor-content :deep(p) {
  margin: 0 0 8px 0;
}

.editor-content :deep(ul),
.editor-content :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.editor-content :deep(li) {
  margin: 4px 0;
}

.editor-content :deep(strong) {
  font-weight: bold;
}

.editor-content :deep(em) {
  font-style: italic;
}

.editor-content :deep(u) {
  text-decoration: underline;
}
</style>
