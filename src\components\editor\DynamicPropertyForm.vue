<script setup lang="ts">
import { computed, ref } from 'vue'
import { Icon } from '@iconify/vue'
import type { PropertyConfig, PropertyGroup } from '../../types/componentConfigs'
import ListEditor from './ListEditor.vue'
import FileUploader from './FileUploader.vue'
import RichTextEditor from './RichTextEditor.vue'
import SegmentedControl from './SegmentedControl.vue'
import ImageUploader from '../ImageUploader.vue'

// 定义组件属性
interface Props {
    groups: PropertyGroup[]
    modelValue: Record<string, unknown>
}

// 定义组件事件
interface Emits {
    (e: 'update:modelValue', value: Record<string, unknown>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 属性面板折叠状态
const activeCollapse = ref<string[]>([])

// 初始化折叠状态
const initCollapseState = () => {
    const activeGroups: string[] = []
    props.groups.forEach(group => {
        if (!group.collapsible || !group.collapsed) {
            activeGroups.push(group.name)
        }
    })
    activeCollapse.value = activeGroups
}

// 创建本地数据副本
const localValue = computed({
    get: () => props.modelValue,
    set: (value) => {
        emit('update:modelValue', value)
    }
})

// 处理属性值变更
const handlePropertyChange = (key: string, value: unknown) => {
    const newValue = { ...localValue.value, [key]: value }
    emit('update:modelValue', newValue)
}

// 获取属性值
const getPropertyValue = (property: PropertyConfig) => {
    return localValue.value[property.key] ?? property.defaultValue
}

// 获取属性值的类型安全版本
const getPropertyValueAsString = (property: PropertyConfig): string => {
    const value = getPropertyValue(property)
    return typeof value === 'string' ? value : String(value || '')
}

const getPropertyValueAsNumber = (property: PropertyConfig): number => {
    const value = getPropertyValue(property)
    return typeof value === 'number' ? value : Number(value) || 0
}

const getPropertyValueAsBoolean = (property: PropertyConfig): boolean => {
    const value = getPropertyValue(property)
    return typeof value === 'boolean' ? value : Boolean(value)
}

const getPropertyValueAsArray = (property: PropertyConfig): Record<string, unknown>[] => {
    const value = getPropertyValue(property)
    return Array.isArray(value) ? value as Record<string, unknown>[] : []
}

// 处理图片上传 - 使用ImageUploader组件获取图片URL
const handleImageUploadSuccess = (property: PropertyConfig, url: string) => {
    handlePropertyChange(property.key, url)
}

const handleImageRemove = (property: PropertyConfig) => {
    handlePropertyChange(property.key, '')
}

// 初始化折叠状态
initCollapseState()
</script>

<template>
    <div class="dynamic-property-form">
        <el-collapse v-model="activeCollapse" accordion>
            <el-collapse-item v-for="group in groups" :key="group.name" :title="group.title" :name="group.name"
                :class="{ 'advanced-group': group.collapsible }">
                <template #title>
                    <div class="collapse-title">
                        <span>{{ group.title }}</span>
                        <span v-if="group.collapsible" class="advanced-badge">高级</span>
                    </div>
                </template>
                <div class="property-group">
                    <div v-for="property in group.properties" :key="property.key" class="property-item">
                        <label class="property-label">{{ property.label }}</label>

                        <!-- 错误提示：缺少control配置 -->
                        <div v-if="!property.control" class="property-error">
                            <Icon icon="material-symbols:error" style="width: 16px; height: 16px; color: #f56c6c;" />
                            <span>属性 "{{ property.key }}" 缺少控制器配置</span>
                        </div>

                        <!-- 文本输入 -->
                        <el-input v-if="property.control?.type === 'text'"
                            :model-value="getPropertyValueAsString(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" :size="property.control?.size || 'small'"
                            :clearable="property.control?.clearable" />

                        <!-- 多行文本输入 -->
                        <el-input v-else-if="property.control?.type === 'textarea'"
                            :model-value="getPropertyValueAsString(property)"
                            @update:model-value="(val: string | null) => handlePropertyChange(property.key, val || '')"
                            type="textarea" :placeholder="property.control?.placeholder"
                            :size="property.control?.size || 'small'" :rows="Number(property.control?.rows) || 3" />

                        <!-- 数字输入 -->
                        <el-input-number v-else-if="property.control?.type === 'number'"
                            :model-value="getPropertyValueAsNumber(property)"
                            @update:model-value="(val: number | undefined) => handlePropertyChange(property.key, val || 0)"
                            :min="property.control?.min" :max="property.control?.max" :step="property.control?.step"
                            :size="property.control?.size || 'small'" style="width: 100%" />

                        <!-- 颜色选择器 -->
                        <el-color-picker v-else-if="property.control?.type === 'color'"
                            :model-value="getPropertyValueAsString(property)"
                            @update:model-value="(val: string | null) => handlePropertyChange(property.key, val || '')"
                            :size="property.control?.size || 'small'"
                            :show-alpha="Boolean(property.control?.showAlpha)" />

                        <!-- 下拉选择 -->
                        <el-select v-else-if="property.control?.type === 'select'"
                            :model-value="getPropertyValueAsString(property)"
                            @update:model-value="(val: any) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" :size="property.control?.size || 'small'"
                            style="width: 100%" :clearable="property.control?.clearable"
                            :multiple="property.control?.multiple">
                            <el-option v-for="option in property.control?.options || []" :key="option.value"
                                :label="option.label" :value="option.value" />
                        </el-select>

                        <!-- 开关 -->
                        <el-switch v-else-if="property.control?.type === 'switch'"
                            :model-value="getPropertyValueAsBoolean(property)"
                            @update:model-value="(val: boolean) => handlePropertyChange(property.key, val)"
                            :size="property.control?.size || 'small'" :disabled="property.control?.disabled"
                            :loading="property.control?.loading" />

                        <!-- 滑块 -->
                        <el-slider v-else-if="property.control?.type === 'slider'"
                            :model-value="getPropertyValueAsNumber(property)"
                            @update:model-value="(val: number) => handlePropertyChange(property.key, val)"
                            :min="property.control?.min" :max="property.control?.max" :step="property.control?.step"
                            :size="property.control?.size || 'small'" :show-input="property.control?.showInput"
                            :show-stops="property.control?.showStops" :show-tooltip="property.control?.showTooltip"
                            :format-tooltip="property.control?.formatTooltip" />

                        <!-- 单选组 -->
                        <el-radio-group v-else-if="property.control?.type === 'radio-group'"
                            :model-value="getPropertyValueAsString(property)"
                            @update:model-value="(val: any) => handlePropertyChange(property.key, val)"
                            :size="property.control?.size || 'small'">
                            <el-radio v-for="option in property.control?.options || []" :key="option.value"
                                :label="option.value">
                                {{ option.label }}
                            </el-radio>
                        </el-radio-group>

                        <!-- 图标选择器 -->
                        <div v-else-if="property.control?.type === 'radio-group-icon'" class="radio-group-icon">
                            <el-tooltip v-for="option in property.control?.options || []" :key="option.value"
                                :content="option.label || option.value" placement="top">
                                <div class="icon-option"
                                    :class="{ active: getPropertyValueAsString(property) === option.value }"
                                    @click="handlePropertyChange(property.key, option.value)">
                                    <Icon :icon="option.icon || 'material-symbols:widgets'"
                                        style="width: 16px; height: 16px;" />
                                </div>
                            </el-tooltip>
                        </div>

                        <!-- 按钮组 -->
                        <el-button-group v-else-if="property.control?.type === 'button-group'">
                            <el-button v-for="option in property.control?.options || []" :key="option.value"
                                :type="getPropertyValueAsString(property) === option.value ? 'primary' : 'default'"
                                :size="property.control?.size || 'small'"
                                @click="handlePropertyChange(property.key, option.value)">
                                <Icon v-if="option.icon" :icon="option.icon" style="width: 14px; height: 14px;" />
                                {{ option.label }}
                            </el-button>
                        </el-button-group>

                        <!-- 图片上传 -->
                        <div v-else-if="property.control?.type === 'image-upload'" class="image-upload-control">
                            <ImageUploader :model-value="getPropertyValueAsString(property)"
                                @update:model-value="(url: string) => handleImageUploadSuccess(property, url)"
                                @remove="() => handleImageRemove(property)"
                                :accept="property.control?.accept || 'image/*'"
                                :placeholder="property.control?.placeholder || '点击上传图片'" :uploading-text="'上传中...'"
                                :max-size="property.control?.maxSize || 5" width="120px" height="80px"
                                layout="rectangle" border-radius="8px" />
                            <div v-if="property.control?.tip" class="upload-tip">{{ property.control.tip }}</div>
                        </div>

                        <!-- 范围滑块 -->
                        <el-slider v-else-if="property.control?.type === 'range'"
                            :model-value="getPropertyValueAsArray(property)"
                            @update:model-value="(val: [number, number]) => handlePropertyChange(property.key, val)"
                            range :min="property.control?.min" :max="property.control?.max"
                            :step="property.control?.step" :size="property.control?.size || 'small'"
                            :show-input="property.control?.showInput" :show-stops="property.control?.showStops"
                            :show-tooltip="property.control?.showTooltip" />

                        <!-- 带文字的开关 -->
                        <el-switch v-else-if="property.control?.type === 'toggle'"
                            :model-value="getPropertyValueAsBoolean(property)"
                            @update:model-value="(val: boolean) => handlePropertyChange(property.key, val)"
                            :size="property.control?.size || 'small'" :active-text="property.control?.activeText"
                            :inactive-text="property.control?.inactiveText" />

                        <!-- 列表编辑器 -->
                        <ListEditor v-else-if="property.control?.type === 'list-editor'"
                            :model-value="getPropertyValueAsArray(property)"
                            @update:model-value="(val: Array<Record<string, unknown>>) => handlePropertyChange(property.key, val)"
                            :label="property.label" :item-config="property.control?.itemConfig || []" />

                        <!-- 文件上传器 -->
                        <FileUploader v-else-if="property.control?.type === 'file-uploader'"
                            :model-value="getPropertyValue(property) as { name: string; url: string; description: string }"
                            @update:model-value="(val: { name: string; url: string; description: string }) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" :accept="property.control?.accept"
                            :tip="property.control?.tip" />

                        <!-- 视频上传器 -->
                        <FileUploader v-else-if="property.control?.type === 'video-upload'"
                            :model-value="getPropertyValue(property) as { name: string; url: string; description: string }"
                            @update:model-value="(val: { name: string; url: string; description: string }) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" :accept="property.control?.accept || 'video/*'"
                            :tip="property.control?.tip" />

                        <!-- 富文本编辑器 -->
                        <RichTextEditor v-else-if="property.control?.type === 'rich-text-editor'"
                            :model-value="getPropertyValueAsString(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" />

                        <!-- 分段控制器 -->
                        <SegmentedControl v-else-if="property.control?.type === 'segmented-control'"
                            :model-value="getPropertyValueAsString(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)" :options="(property.control?.options || []).map(opt => ({
                                label: String(opt.label || opt.value || ''),
                                value: String(opt.value)
                            }))" />

                        <!-- 默认文本输入 -->
                        <el-input v-else :model-value="getPropertyValueAsString(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" :size="property.control?.size || 'small'" />
                    </div>
                </div>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<style scoped>
.dynamic-property-form {
    width: 100%;
}

.property-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.property-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background: #fafbfc;
    border-radius: 6px;
    border: 1px solid #f0f2f5;
    transition: all 0.3s ease;
}

.property-item:hover {
    background: #f5f7fa;
    border-color: #e4e7ed;
}

.property-label {
    font-size: 13px;
    color: #303133;
    font-weight: 600;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.upload-control {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.upload-preview {
    padding: 8px;
    background: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
}

.file-name {
    font-size: 12px;
    color: #606266;
    word-break: break-all;
}

.collapse-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.advanced-badge {
    font-size: 10px;
    color: #909399;
    background: #f0f2f5;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: normal;
}

.advanced-group {
    border-left: 2px solid #e4e7ed;
}

.advanced-group .el-collapse-item__header {
    background: #fafbfc;
}

/* 图标选择器样式 */
.radio-group-icon {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    justify-content: center;
}

.icon-option {
    width: 36px;
    height: 36px;
    border: 2px solid #e4e7ed;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #606266;
    background: white;
    position: relative;
}

.icon-option:hover {
    border-color: #409eff;
    color: #409eff;
    background: #f0f9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.icon-option.active {
    border-color: #409eff;
    color: #409eff;
    background: #e6f3ff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 图片上传样式 */
.image-upload-control {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-label {
    font-size: 13px;
    color: #303133;
    font-weight: 600;
    margin-bottom: 4px;
}



.upload-tip {
    font-size: 11px;
    color: #909399;
    line-height: 1.4;
    padding: 4px 8px;
    background: #f5f7fa;
    border-radius: 4px;
    border-left: 3px solid #409eff;
}

/* 按钮组样式 */
.el-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: center;
}

.el-button-group .el-button {
    flex: 1;
    min-width: 60px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.el-button-group .el-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 错误提示样式 */
.property-error {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    background: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 6px;
    color: #f56c6c;
    font-size: 12px;
    margin-top: 4px;
}
</style>