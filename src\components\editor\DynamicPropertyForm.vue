<script setup lang="ts">
import { computed, ref } from 'vue'
import { Icon } from '@iconify/vue'
import type { PropertyConfig, PropertyGroup, ControlConfig } from '../../types/componentConfigs'
import ListEditor from './ListEditor.vue'
import FileUploader from './FileUploader.vue'
import RichTextEditor from './RichTextEditor.vue'
import SegmentedControl from './SegmentedControl.vue'

// 定义组件属性
interface Props {
    groups: PropertyGroup[]
    modelValue: Record<string, any>
}

// 定义组件事件
interface Emits {
    (e: 'update:modelValue', value: Record<string, any>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 属性面板折叠状态
const activeCollapse = ref<string[]>([])

// 初始化折叠状态
const initCollapseState = () => {
    const activeGroups: string[] = []
    props.groups.forEach(group => {
        if (!group.collapsible || !group.collapsed) {
            activeGroups.push(group.name)
        }
    })
    activeCollapse.value = activeGroups
}

// 创建本地数据副本
const localValue = computed({
    get: () => props.modelValue,
    set: (value) => {
        emit('update:modelValue', value)
    }
})

// 处理属性值变更
const handlePropertyChange = (key: string, value: any) => {
    const newValue = { ...localValue.value, [key]: value }
    emit('update:modelValue', newValue)
}

// 获取属性值
const getPropertyValue = (property: PropertyConfig) => {
    return localValue.value[property.key] ?? property.defaultValue
}

// 处理图片上传
const handleImageUpload = (property: PropertyConfig, file: any) => {
    if (file.raw) {
        const reader = new FileReader()
        reader.onload = (e) => {
            handlePropertyChange(property.key, e.target?.result)
        }
        reader.readAsDataURL(file.raw)
    }
}

// 初始化折叠状态
initCollapseState()
</script>

<template>
    <div class="dynamic-property-form">
        <el-collapse v-model="activeCollapse" accordion>
            <el-collapse-item v-for="group in groups" :key="group.name" :title="group.title" :name="group.name"
                :class="{ 'advanced-group': group.collapsible }">
                <template #title>
                    <div class="collapse-title">
                        <span>{{ group.title }}</span>
                        <span v-if="group.collapsible" class="advanced-badge">高级</span>
                    </div>
                </template>
                <div class="property-group">
                    <div v-for="property in group.properties" :key="property.key" class="property-item">
                        <!-- <label>{{ property.label }}</label> -->

                        <!-- 错误提示：缺少control配置 -->
                        <div v-if="!property.control" class="property-error">
                            <Icon icon="material-symbols:error" style="width: 16px; height: 16px; color: #f56c6c;" />
                            <span>属性 "{{ property.key }}" 缺少控制器配置</span>
                        </div>

                        <!-- 文本输入 -->
                        <el-input v-if="property.control?.type === 'text'" :model-value="getPropertyValue(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" :size="property.control?.size || 'small'"
                            :clearable="property.control?.clearable" />

                        <!-- 多行文本输入 -->
                        <el-input v-else-if="property.control?.type === 'textarea'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)"
                            type="textarea" :placeholder="property.control?.placeholder"
                            :size="property.control?.size || 'small'" :rows="property.control?.rows || 3" />

                        <!-- 数字输入 -->
                        <el-input-number v-else-if="property.control?.type === 'number'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: number) => handlePropertyChange(property.key, val)"
                            :min="property.control?.min" :max="property.control?.max" :step="property.control?.step"
                            :size="property.control?.size || 'small'" style="width: 100%" />

                        <!-- 颜色选择器 -->
                        <el-color-picker v-else-if="property.control?.type === 'color'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)"
                            :size="property.control?.size || 'small'" :show-alpha="property.control?.showAlpha" />

                        <!-- 下拉选择 -->
                        <el-select v-else-if="property.control?.type === 'select'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: any) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" :size="property.control?.size || 'small'"
                            style="width: 100%" :clearable="property.control?.clearable"
                            :multiple="property.control?.multiple">
                            <el-option v-for="option in property.control?.options || []" :key="option.value"
                                :label="option.label" :value="option.value" />
                        </el-select>

                        <!-- 开关 -->
                        <el-switch v-else-if="property.control?.type === 'switch'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: boolean) => handlePropertyChange(property.key, val)"
                            :size="property.control?.size || 'small'" :disabled="property.control?.disabled"
                            :loading="property.control?.loading" />

                        <!-- 滑块 -->
                        <el-slider v-else-if="property.control?.type === 'slider'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: number) => handlePropertyChange(property.key, val)"
                            :min="property.control?.min" :max="property.control?.max" :step="property.control?.step"
                            :size="property.control?.size || 'small'" :show-input="property.control?.showInput"
                            :show-stops="property.control?.showStops" :show-tooltip="property.control?.showTooltip"
                            :format-tooltip="property.control?.formatTooltip" />

                        <!-- 单选组 -->
                        <el-radio-group v-else-if="property.control?.type === 'radio-group'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: any) => handlePropertyChange(property.key, val)"
                            :size="property.control?.size || 'small'">
                            <el-radio v-for="option in property.control?.options || []" :key="option.value"
                                :label="option.value">
                                {{ option.label }}
                            </el-radio>
                        </el-radio-group>

                        <!-- 图标选择器 -->
                        <div v-else-if="property.control?.type === 'radio-group-icon'" class="radio-group-icon">
                            <el-tooltip v-for="option in property.control?.options || []" :key="option.value"
                                :content="option.label || option.value" placement="top">
                                <div class="icon-option"
                                    :class="{ active: getPropertyValue(property) === option.value }"
                                    @click="handlePropertyChange(property.key, option.value)">
                                    <Icon :icon="option.icon || 'material-symbols:widgets'"
                                        style="width: 16px; height: 16px;" />
                                </div>
                            </el-tooltip>
                        </div>

                        <!-- 按钮组 -->
                        <el-button-group v-else-if="property.control?.type === 'button-group'">
                            <el-button v-for="option in property.control?.options || []" :key="option.value"
                                :type="getPropertyValue(property) === option.value ? 'primary' : 'default'"
                                :size="property.control?.size || 'small'"
                                @click="handlePropertyChange(property.key, option.value)">
                                <Icon v-if="option.icon" :icon="option.icon" style="width: 14px; height: 14px;" />
                                {{ option.label }}
                            </el-button>
                        </el-button-group>

                        <!-- 图片上传 -->
                        <div v-else-if="property.control?.type === 'image-upload'" class="image-upload-control">
                            <el-upload action="#" :auto-upload="false" :show-file-list="false"
                                :accept="property.control?.accept"
                                @change="(file: any) => handleImageUpload(property, file)">
                                <el-button size="small" type="primary" plain>
                                    <Icon icon="material-symbols:upload" style="width: 16px; height: 16px;" />
                                    上传图片
                                </el-button>
                            </el-upload>
                            <div v-if="getPropertyValue(property)" class="image-preview">
                                <img :src="getPropertyValue(property)" alt="预览" />
                                <el-button size="small" type="danger" circle
                                    @click="handlePropertyChange(property.key, '')">
                                    <Icon icon="material-symbols:delete" style="width: 12px; height: 12px;" />
                                </el-button>
                            </div>
                            <div v-if="property.control?.tip" class="upload-tip">{{ property.control.tip }}</div>
                        </div>

                        <!-- 范围滑块 -->
                        <el-slider v-else-if="property.control?.type === 'range'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: [number, number]) => handlePropertyChange(property.key, val)"
                            range :min="property.control?.min" :max="property.control?.max"
                            :step="property.control?.step" :size="property.control?.size || 'small'"
                            :show-input="property.control?.showInput" :show-stops="property.control?.showStops"
                            :show-tooltip="property.control?.showTooltip" />

                        <!-- 带文字的开关 -->
                        <el-switch v-else-if="property.control?.type === 'toggle'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: boolean) => handlePropertyChange(property.key, val)"
                            :size="property.control?.size || 'small'" :active-text="property.control?.activeText"
                            :inactive-text="property.control?.inactiveText" />

                        <!-- 列表编辑器 -->
                        <ListEditor v-else-if="property.control?.type === 'list-editor'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: Array<Record<string, string>>) => handlePropertyChange(property.key, val)"
                            :label="property.label" :item-config="property.control?.itemConfig || []" />

                        <!-- 文件上传器 -->
                        <FileUploader v-else-if="property.control?.type === 'file-uploader'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: { name: string; url: string; description: string }) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" :accept="property.control?.accept"
                            :tip="property.control?.tip" />

                        <!-- 富文本编辑器 -->
                        <RichTextEditor v-else-if="property.control?.type === 'rich-text-editor'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" />

                        <!-- 分段控制器 -->
                        <SegmentedControl v-else-if="property.control?.type === 'segmented-control'"
                            :model-value="getPropertyValue(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)" :options="(property.control?.options || []).map(opt => ({
                                label: String(opt.label || opt.value || ''),
                                value: String(opt.value)
                            }))" />

                        <!-- 默认文本输入 -->
                        <el-input v-else :model-value="getPropertyValue(property)"
                            @update:model-value="(val: string) => handlePropertyChange(property.key, val)"
                            :placeholder="property.control?.placeholder" :size="property.control?.size || 'small'" />
                    </div>
                </div>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<style scoped>
.dynamic-property-form {
    width: 100%;
}

.property-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.property-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.property-item label {
    font-size: 12px;
    color: #606266;
    font-weight: 500;
}

.upload-control {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.upload-preview {
    padding: 8px;
    background: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
}

.file-name {
    font-size: 12px;
    color: #606266;
    word-break: break-all;
}

.collapse-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.advanced-badge {
    font-size: 10px;
    color: #909399;
    background: #f0f2f5;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: normal;
}

.advanced-group {
    border-left: 2px solid #e4e7ed;
}

.advanced-group .el-collapse-item__header {
    background: #fafbfc;
}

/* 图标选择器样式 */
.radio-group-icon {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.icon-option {
    width: 32px;
    height: 32px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #606266;
    background: white;
}

.icon-option:hover {
    border-color: #409eff;
    color: #409eff;
    background: #f0f9ff;
}

.icon-option.active {
    border-color: #409eff;
    color: #409eff;
    background: #e6f3ff;
}

/* 图片上传样式 */
.image-upload-control {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.image-preview {
    position: relative;
    display: inline-block;
    max-width: 100%;
}

.image-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
}

.image-preview .el-button {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    padding: 0;
}

.upload-tip {
    font-size: 11px;
    color: #909399;
    line-height: 1.4;
}

/* 按钮组样式 */
.el-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.el-button-group .el-button {
    flex: 1;
    min-width: 0;
}

/* 错误提示样式 */
.property-error {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
    color: #f56c6c;
    font-size: 12px;
}
</style>