/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BannerWidget: typeof import('./src/components/widgets/BannerWidget.vue')['default']
    ButtonWidget: typeof import('./src/components/widgets/ButtonWidget.vue')['default']
    CardContainerWidget: typeof import('./src/components/widgets/CardContainerWidget.vue')['default']
    CardWidget: typeof import('./src/components/widgets/CardWidget.vue')['default']
    CarouselWidget: typeof import('./src/components/widgets/CarouselWidget.vue')['default']
    ColumnContainerWidget: typeof import('./src/components/widgets/ColumnContainerWidget.vue')['default']
    ComponentLibrary: typeof import('./src/components/editor/ComponentLibrary.vue')['default']
    ComponentRenderer: typeof import('./src/components/editor/ComponentRenderer.vue')['default']
    DefaultFooter: typeof import('./src/components/DefaultFooter.vue')['default']
    DefaultHeader: typeof import('./src/components/DefaultHeader.vue')['default']
    DesignCanvas: typeof import('./src/components/editor/DesignCanvas.vue')['default']
    DynamicPropertyForm: typeof import('./src/components/editor/DynamicPropertyForm.vue')['default']
    EditorToolbar: typeof import('./src/components/editor/EditorToolbar.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FileListWidget: typeof import('./src/components/widgets/FileListWidget.vue')['default']
    FileUploader: typeof import('./src/components/editor/FileUploader.vue')['default']
    GridContainerWidget: typeof import('./src/components/widgets/GridContainerWidget.vue')['default']
    ImageTextWidget: typeof import('./src/components/widgets/ImageTextWidget.vue')['default']
    ImageUploader: typeof import('./src/components/ImageUploader.vue')['default']
    ImageWidget: typeof import('./src/components/widgets/ImageWidget.vue')['default']
    InfoGridWidget: typeof import('./src/components/widgets/InfoGridWidget.vue')['default']
    ListEditor: typeof import('./src/components/editor/ListEditor.vue')['default']
    OutlinePanel: typeof import('./src/components/editor/OutlinePanel.vue')['default']
    PropertiesPanel: typeof import('./src/components/editor/PropertiesPanel.vue')['default']
    RichTextEditor: typeof import('./src/components/editor/RichTextEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RowContainerWidget: typeof import('./src/components/widgets/RowContainerWidget.vue')['default']
    SegmentedControl: typeof import('./src/components/editor/SegmentedControl.vue')['default']
    SimpleColumnContainer: typeof import('./src/components/widgets/SimpleColumnContainer.vue')['default']
    SimpleRowContainer: typeof import('./src/components/widgets/SimpleRowContainer.vue')['default']
    SmartSearchInput: typeof import('./src/components/SmartSearchInput.vue')['default']
    TextWidget: typeof import('./src/components/widgets/TextWidget.vue')['default']
    TitleWidget: typeof import('./src/components/widgets/TitleWidget.vue')['default']
    VideoListWidget: typeof import('./src/components/widgets/VideoListWidget.vue')['default']
    VideoWidget: typeof import('./src/components/widgets/VideoWidget.vue')['default']
  }
}
