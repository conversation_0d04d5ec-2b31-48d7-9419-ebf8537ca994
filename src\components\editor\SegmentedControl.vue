<template>
    <div class="segmented-control">
        <el-button-group>
            <el-button v-for="option in options" :key="option.value" size="small"
                :type="modelValue === option.value ? 'primary' : ''" @click="handleOptionClick(option.value)">
                {{ option.label }}
            </el-button>
        </el-button-group>
    </div>
</template>

<script setup lang="ts">
interface Option {
    label: string
    value: string
}

interface Props {
    modelValue?: string
    options?: Option[]
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    options: () => []
})

const emit = defineEmits<{
    'update:modelValue': [value: string]
}>()

const handleOptionClick = (value: string) => {
    emit('update:modelValue', value)
}
</script>

<style scoped>
.segmented-control {
    width: 100%;
}

.el-button-group {
    width: 100%;
    display: flex;
}

.el-button-group .el-button {
    flex: 1;
    min-width: 0;
}
</style>