<!-- src/components/editor/widgets/FileListWidget.vue -->
<template>
  <div class="file-list-grid">
    <a v-for="(file, index) in props.files" :key="index" :href="file.url" target="_blank" class="file-item-card">
      <span class="file-icon">{{ getFileIcon(file.name) }}</span>
      <span class="file-description">{{ file.description }}</span>
    </a>
  </div>
</template>

<script setup lang="ts">
// 定义组件属性
interface Props {
  files?: Array<{ name: string; url: string; description: string }>
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  files: () => [
    { name: 'example.doc', url: '#', description: '航天博物馆（大学组）——做新时代科技创新的生力军' },
    { name: 'example.doc', url: '#', description: '航天博物馆（高中组）——育航天精神 促科技创新' },
    { name: 'example.doc', url: '#', description: '航天博物馆（初中组）——航天助力 创新强国' },
    { name: 'example.doc', url: '#', description: '航天博物馆（小学组）——传承航天精神 矢志科创报国' },
  ]
})

// 这是一个简化的辅助函数，用于根据文件名后缀返回图标
// 使用 emoji 作为临时图标解决方案
function getFileIcon(fileName: string): string {
  if (!fileName || typeof fileName !== 'string') {
    return '📄'; // 返回默认图标
  }
  const extension = fileName.split('.').pop()?.toLowerCase();
  const iconMap: Record<string, string> = {
    pdf: '📄',
    doc: '📝',
    docx: '📝',
    xls: '📊',
    xlsx: '📊',
    ppt: '📽️',
    pptx: '📽️',
    txt: '📄',
    zip: '📦',
    rar: '📦',
    mp4: '🎥',
    mp3: '🎵',
    jpg: '🖼️',
    png: '🖼️',
    gif: '🖼️'
  };
  return iconMap[extension || ''] || '📄'; // 返回一个默认图标
}
</script>

<style scoped>
.file-list-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 20px;
  width: 100%;
}

.file-item-card {
  flex: 1 1 45%;
  min-width: 320px;
  max-width: 48%;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  border-radius: 12px;
  text-decoration: none;
  color: #333;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.file-item-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: rgba(64, 158, 255, 0.2);
}

.file-icon {
  font-size: 36px;
  margin-right: 18px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.file-description {
  font-size: 15px;
  line-height: 1.5;
  font-weight: 500;
  color: #1a1a1a;
  flex: 1;
  word-break: break-word;
}

/* 平板端适配 */
@media (max-width: 768px) {
  .file-list-grid {
    gap: 16px;
  }

  .file-item-card {
    flex: 1 1 100%;
    min-width: 0;
    max-width: 100%;
    padding: 16px;
  }

  .file-icon {
    font-size: 32px;
    width: 50px;
    height: 50px;
    margin-right: 16px;
  }

  .file-description {
    font-size: 14px;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .file-list-grid {
    gap: 12px;
  }

  .file-item-card {
    flex-direction: column;
    text-align: center;
    padding: 16px;
    border-radius: 10px;
  }

  .file-icon {
    font-size: 28px;
    width: 50px;
    height: 50px;
    margin-right: 0;
    margin-bottom: 12px;
  }

  .file-description {
    font-size: 13px;
    line-height: 1.4;
  }
}

/* 超小屏幕适配 */
@media (max-width: 320px) {
  .file-list-grid {
    gap: 10px;
  }

  .file-item-card {
    padding: 12px;
    border-radius: 8px;
  }

  .file-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
  }

  .file-description {
    font-size: 12px;
  }
}
</style>