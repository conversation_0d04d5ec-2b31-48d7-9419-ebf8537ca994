<script setup lang="ts">
import { Search, Plus } from '@element-plus/icons-vue'

interface Props {
    searchQuery: string
}

interface Emits {
    (e: 'update:searchQuery', value: string): void
    (e: 'add'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleSearchChange = (value: string) => {
    emit('update:searchQuery', value)
}

const handleAdd = () => {
    emit('add')
}
</script>

<template>
    <div class="toolbar">
        <div class="toolbar-left">
            <el-input :model-value="searchQuery" @update:model-value="handleSearchChange" placeholder="搜索基地名称或城市"
                :prefix-icon="Search" style="width: 300px" />
        </div>
        <div class="toolbar-right">
            <el-button type="primary" @click="handleAdd">
                <el-icon>
                    <Plus />
                </el-icon>
                添加基地
            </el-button>
        </div>
    </div>
</template>

<style scoped>
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: white;
    border-bottom: 1px solid #ebeef5;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    gap: 12px;
}

@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        gap: 16px;
    }

    .toolbar-left,
    .toolbar-right {
        width: 100%;
    }
}
</style>
