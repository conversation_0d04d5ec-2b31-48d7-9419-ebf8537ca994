# 江西师范大学实践基地管理系统

## 项目简介

这是一个基于Vue 3 + TypeScript + Element Plus的实践基地管理系统，支持基地信息的增删改查、分类管理、图片上传等功能。

## 功能特性

- 🔐 用户登录注册
- 🏢 实践基地管理（CRUD）
- 📂 基地分类管理
- 🖼️ 图片上传功能
- 🔍 智能搜索（集成天地图API）
- 📱 响应式设计

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **构建工具**: Vite
- **HTTP客户端**: Axios

## 环境配置

### 1. 天地图API配置

项目集成了天地图搜索服务，需要配置API密钥：

1. 访问 [天地图开发者平台](https://console.tianditu.gov.cn/)
2. 注册并登录账号
3. 创建应用，获取API密钥

在项目根目录创建 `.env` 文件：

```bash
# 天地图API密钥
VITE_TIANDITU_API_KEY=your_tianditu_api_key_here

# 后端API基础URL
VITE_API_BASE_URL=http://*************:8200/
```

### 2. 智能搜索功能

配置完成后，基地名称输入框将支持：

- **实时搜索建议**：输入2个字符以上时自动搜索
- **地址自动填充**：选择POI后自动填充地址和城市信息
- **坐标信息**：获取精确的地理坐标信息
- **防抖优化**：避免频繁请求

## 安装和运行

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 类型检查
npm run type-check
```

## 项目结构

```
src/
├── api/                 # API接口封装
│   ├── config.ts       # Axios配置
│   ├── user.ts         # 用户相关API
│   ├── base.ts         # 基地管理API
│   ├── category.ts     # 分类管理API
│   └── tianditu.ts     # 天地图搜索API
├── components/         # 公共组件
│   ├── ImageUploader.vue    # 图片上传组件
│   └── SmartSearchInput.vue # 智能搜索组件
├── stores/            # Pinia状态管理
├── views/             # 页面组件
│   └── baseAdmin/     # 基地管理模块
└── types/             # TypeScript类型定义
```

## 主要功能模块

### 1. 基地管理

- 基地信息的增删改查
- 支持图片上传
- 分类管理
- 智能搜索

### 2. 分类管理

- 基地分类的CRUD操作
- 颜色标识
- 集成到基地表单中

### 3. 图片上传

- 支持拖拽上传
- 文件类型和大小验证
- 预览和删除功能
- 高度可配置

### 4. 智能搜索

- 基于天地图API的实时搜索
- 自动填充地址信息
- 坐标信息获取

## 注意事项

- 确保已正确配置天地图API密钥
- `.env` 文件包含敏感信息，不要提交到版本控制系统
- 重启开发服务器后环境变量才会生效

## 开发指南

### 添加新的API接口

1. 在 `src/api/` 目录下创建对应的API文件
2. 定义TypeScript接口
3. 在 `src/api/index.ts` 中导出

### 创建新组件

1. 在 `src/components/` 目录下创建Vue组件
2. 使用 `<script setup>` 语法
3. 定义Props和Emits类型

### 状态管理

使用Pinia进行状态管理，在 `src/stores/` 目录下创建store文件。

## 许可证

MIT License
