# JXNU Admin

基于 Vue 3 + TypeScript + Element Plus 的实践基地管理系统

## 环境配置

### 环境变量文件

项目支持多环境配置，包含以下环境变量文件：

- `env.example` - 环境变量示例文件
- `env.development` - 开发环境配置
- `env.test` - 测试环境配置
- `env.production` - 生产环境配置

### 环境变量说明

| 变量名              | 说明         | 默认值                           |
| ------------------- | ------------ | -------------------------------- |
| `VITE_API_BASE_URL` | API 基础地址 | `https://api.dszkmap.com/manage` |
| `VITE_APP_TITLE`    | 应用标题     | `JXNU Admin`                     |
| `VITE_APP_ENV`      | 环境标识     | `development`                    |

### 使用方法

1. **开发环境**

   ```bash
   npm run dev
   ```

2. **测试环境**

   ```bash
   npm run dev:test
   ```

3. **构建生产版本**

   ```bash
   npm run build:prod
   ```

4. **构建测试版本**
   ```bash
   npm run build:test
   ```

### 自定义环境配置

1. 复制 `env.example` 为 `.env.local`
2. 修改 `.env.local` 中的配置
3. 重启开发服务器

## 开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 功能特性

- 🔐 用户登录注册
- 🏢 实践基地管理（CRUD）
- 📂 基地分类管理
- 🖼️ 图片上传功能
- 🔍 智能搜索（集成天地图API）
- 🎨 低代码页面编辑器
- 📱 响应式设计

## 技术栈

- Vue 3
- TypeScript
- Element Plus
- Vite
- Pinia
- Vue Router
- Axios
