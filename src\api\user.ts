import api from './config'

// 用户登录接口
export interface LoginParams {
  phone: string
  password: string
}

export interface LoginResponse {
  code: string
  data: {
    token: string
  }
  msg: string
}

export const login = (params: LoginParams): Promise<LoginResponse> => {
  return api.post('/login', params)
}

// 用户注册接口
export interface RegisterParams {
  phone: string
  password: string
  code: string
}

export interface RegisterResponse {
  code: string
  data: any
  msg: string
}

export const register = (params: RegisterParams): Promise<RegisterResponse> => {
  return api.post('/register', params)
}

// 上传图片接口
export interface UploadImageParams {
  file: File
}

export interface UploadImageResponse {
  code: string
  data: string
  msg: string
}

export const uploadImage = (params: UploadImageParams): Promise<UploadImageResponse> => {
  const formData = new FormData()
  formData.append('file', params.file)

  return api.post('/image/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 获取用户信息
export interface UserInfo {
  id: number
  phone: string
  userName: string
}

export interface GetUserInfoResponse {
  code: string
  data: UserInfo
  msg: string
}

export const getUserInfo = (): Promise<GetUserInfoResponse> => {
  return api.get('/user/info')
}
