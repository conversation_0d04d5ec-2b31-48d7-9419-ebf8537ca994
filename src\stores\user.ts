import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { getUserInfo } from '@/api/user'
import type { UserInfo } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string | null>(localStorage.getItem('admin_token'))
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.userName || '用户')
  const userPhone = computed(() => userInfo.value?.phone || '')
  const userId = computed(() => userInfo.value?.id || 0)

  // 方法
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('admin_token', newToken)
  }

  const clearToken = () => {
    token.value = null
    localStorage.removeItem('admin_token')
  }

  const fetchUserInfo = async () => {
    if (!token.value) {
      return false
    }

    loading.value = true
    try {
      const response = await getUserInfo()
      if (response.code === '200') {
        userInfo.value = response.data
        return true
      } else {
        console.error('获取用户信息失败:', response.msg)
        return false
      }
    } catch (error) {
      console.error('获取用户信息错误:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    userInfo.value = null
    clearToken()
  }

  const initUserInfo = async () => {
    if (token.value && !userInfo.value) {
      await fetchUserInfo()
    }
  }

  return {
    // 状态
    userInfo,
    token,
    loading,

    // 计算属性
    isLoggedIn,
    userName,
    userPhone,
    userId,

    // 方法
    setToken,
    clearToken,
    fetchUserInfo,
    logout,
    initUserInfo,
  }
})
