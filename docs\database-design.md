# 页面配置数据库设计文档

## 概述

本文档描述了优化后的页面配置JSON结构如何映射到MySQL数据库，实现高效存储和查询。

## 数据库表设计

### 1. 页面配置主表 (page_configs)

```sql
CREATE TABLE page_configs (
  -- 主键
  id VARCHAR(50) PRIMARY KEY COMMENT '页面唯一标识',

  -- 基础信息
  name VARCHAR(200) NOT NULL COMMENT '页面名称',
  base_id VARCHAR(50) NOT NULL COMMENT '关联基地ID',
  version VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT '配置版本号',
  schema_version VARCHAR(20) NOT NULL DEFAULT '1.0.0' COMMENT 'JSON Schema版本',

  -- 配置数据（JSON格式）
  config_data JSON NOT NULL COMMENT '完整配置JSON字符串',

  -- 状态信息
  status ENUM('draft', 'published', 'archived', 'deleted') DEFAULT 'draft' COMMENT '页面状态',
  category VARCHAR(100) DEFAULT 'practice-base' COMMENT '分类',
  tags JSON COMMENT '标签数组',

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  -- 创建者信息
  created_by VARCHAR(50) COMMENT '创建者ID',
  updated_by VARCHAR(50) COMMENT '更新者ID',

  -- 索引字段（用于快速查询）
  search_keywords TEXT COMMENT '搜索关键词（用于全文搜索）',

  -- 索引
  INDEX idx_base_id (base_id),
  INDEX idx_status (status),
  INDEX idx_category (category),
  INDEX idx_created_at (created_at),
  INDEX idx_updated_at (updated_at),
  INDEX idx_created_by (created_by),
  FULLTEXT INDEX idx_search (name, search_keywords),

  -- 外键约束
  FOREIGN KEY (base_id) REFERENCES practice_bases(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面配置表';
```

### 2. 页面版本历史表 (page_config_versions)

```sql
CREATE TABLE page_config_versions (
  -- 主键
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '版本记录ID',
  page_id VARCHAR(50) NOT NULL COMMENT '页面ID',

  -- 版本信息
  version VARCHAR(20) NOT NULL COMMENT '版本号',
  version_name VARCHAR(100) COMMENT '版本名称',
  version_description TEXT COMMENT '版本描述',

  -- 配置数据
  config_data JSON NOT NULL COMMENT '该版本的完整配置',

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  created_by VARCHAR(50) COMMENT '创建者ID',

  -- 索引
  INDEX idx_page_id (page_id),
  INDEX idx_version (version),
  INDEX idx_created_at (created_at),

  -- 外键约束
  FOREIGN KEY (page_id) REFERENCES page_configs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面配置版本历史表';
```

### 3. 组件统计表 (component_statistics)

```sql
CREATE TABLE component_statistics (
  -- 主键
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '统计记录ID',
  page_id VARCHAR(50) NOT NULL COMMENT '页面ID',

  -- 统计信息
  component_count INT NOT NULL DEFAULT 0 COMMENT '组件总数',
  component_types JSON COMMENT '组件类型统计',
  total_size BIGINT NOT NULL DEFAULT 0 COMMENT '配置总大小（字节）',

  -- 更新时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  -- 索引
  INDEX idx_page_id (page_id),
  INDEX idx_component_count (component_count),

  -- 外键约束
  FOREIGN KEY (page_id) REFERENCES page_configs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组件统计表';
```

## JSON结构映射

### 优化后的JSON结构

```json
{
  "id": "practice_base_001",
  "name": "井冈山革命烈士陵园",
  "baseId": "practice_base_001",
  "version": "1.0.0",
  "schemaVersion": "1.0.0",
  "components": [
    {
      "id": "comp_banner_001",
      "type": "banner",
      "order": 1,
      "properties": {
        "mainTitle": "井冈山革命烈士陵园",
        "subTitle": "传承红色基因，弘扬革命精神"
      }
    }
  ],
  "layout": {
    "width": "100%",
    "height": "auto",
    "backgroundColor": "#ffffff",
    "seo": {
      "title": "井冈山革命烈士陵园 - 实践基地",
      "description": "井冈山革命烈士陵园实践基地页面",
      "keywords": "井冈山,革命烈士陵园,实践基地"
    },
    "globalStyles": {
      "fontFamily": "'PingFang SC', sans-serif",
      "fontSize": "16px",
      "colors": {
        "primary": "#c00",
        "text": "#333333"
      }
    }
  },
  "theme": {
    "header": {
      "title": "井冈山革命烈士陵园",
      "backgroundColor": "#c00",
      "textColor": "#ffffff"
    },
    "footer": {
      "contact": {
        "name": "韦雅云",
        "phone": "010-68755926"
      },
      "copyright": "版权所有 © 2016-2025"
    }
  },
  "metadata": {
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T16:45:00.000Z",
    "status": "published",
    "category": "practice-base",
    "tags": ["井冈山", "革命烈士陵园", "实践基地"]
  }
}
```

### 数据库存储映射

| JSON字段             | 数据库字段       | 类型         | 说明         |
| -------------------- | ---------------- | ------------ | ------------ |
| `id`                 | `id`             | VARCHAR(50)  | 主键         |
| `name`               | `name`           | VARCHAR(200) | 页面名称     |
| `baseId`             | `base_id`        | VARCHAR(50)  | 基地ID       |
| `version`            | `version`        | VARCHAR(20)  | 版本号       |
| `schemaVersion`      | `schema_version` | VARCHAR(20)  | Schema版本   |
| 完整JSON             | `config_data`    | JSON         | 完整配置数据 |
| `metadata.status`    | `status`         | ENUM         | 页面状态     |
| `metadata.category`  | `category`       | VARCHAR(100) | 分类         |
| `metadata.tags`      | `tags`           | JSON         | 标签数组     |
| `metadata.createdAt` | `created_at`     | TIMESTAMP    | 创建时间     |
| `metadata.updatedAt` | `updated_at`     | TIMESTAMP    | 更新时间     |
| `metadata.createdBy` | `created_by`     | VARCHAR(50)  | 创建者       |
| `metadata.updatedBy` | `updated_by`     | VARCHAR(50)  | 更新者       |

## Java实体类映射

### PageConfig实体类

```java
@Entity
@Table(name = "page_configs")
public class PageConfigEntity {

    @Id
    @Column(name = "id", length = 50)
    private String id;

    @Column(name = "name", nullable = false, length = 200)
    private String name;

    @Column(name = "base_id", nullable = false, length = 50)
    private String baseId;

    @Column(name = "version", nullable = false, length = 20)
    private String version;

    @Column(name = "schema_version", nullable = false, length = 20)
    private String schemaVersion;

    @Column(name = "config_data", columnDefinition = "JSON")
    private String configData;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PageStatus status;

    @Column(name = "category", length = 100)
    private String category;

    @Column(name = "tags", columnDefinition = "JSON")
    private String tags;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "created_by", length = 50)
    private String createdBy;

    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    @Column(name = "search_keywords", columnDefinition = "TEXT")
    private String searchKeywords;

    // Getters and Setters...
}
```

### 数据传输对象 (DTO)

```java
public class PageConfigDTO {
    private String id;
    private String name;
    private String baseId;
    private String version;
    private String schemaVersion;
    private PageConfigData configData;
    private PageStatus status;
    private String category;
    private List<String> tags;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;

    // Getters and Setters...
}

public class PageConfigData {
    private List<ComponentConfig> components;
    private LayoutConfig layout;
    private ThemeConfig theme;
    private PageMetadata metadata;

    // Getters and Setters...
}
```

## 查询优化

### 1. 基础查询

```sql
-- 根据基地ID查询页面列表
SELECT id, name, version, status, created_at, updated_at
FROM page_configs
WHERE base_id = ? AND status != 'deleted'
ORDER BY updated_at DESC;

-- 根据状态查询页面
SELECT id, name, base_id, version, category
FROM page_configs
WHERE status = ? AND category = ?
ORDER BY created_at DESC;

-- 全文搜索
SELECT id, name, base_id, version, status
FROM page_configs
WHERE MATCH(name, search_keywords) AGAINST(? IN NATURAL LANGUAGE MODE)
AND status != 'deleted';
```

### 2. 组件统计查询

```sql
-- 获取组件使用统计
SELECT
    JSON_EXTRACT(component_types, '$') as types,
    component_count,
    total_size
FROM component_statistics
WHERE page_id = ?;

-- 获取基地下所有页面的组件统计
SELECT
    pc.name,
    cs.component_count,
    cs.total_size,
    cs.updated_at
FROM page_configs pc
JOIN component_statistics cs ON pc.id = cs.page_id
WHERE pc.base_id = ?
ORDER BY cs.updated_at DESC;
```

### 3. 版本历史查询

```sql
-- 获取页面版本历史
SELECT version, version_name, version_description, created_at, created_by
FROM page_config_versions
WHERE page_id = ?
ORDER BY created_at DESC;

-- 获取特定版本的配置
SELECT config_data
FROM page_config_versions
WHERE page_id = ? AND version = ?;
```

## 性能优化建议

### 1. 索引优化

```sql
-- 复合索引
CREATE INDEX idx_base_status ON page_configs(base_id, status);
CREATE INDEX idx_category_status ON page_configs(category, status);
CREATE INDEX idx_created_by_status ON page_configs(created_by, status);

-- 部分索引（只索引非删除状态）
CREATE INDEX idx_active_pages ON page_configs(base_id, updated_at)
WHERE status != 'deleted';
```

### 2. JSON字段优化

```sql
-- 为JSON字段创建虚拟列和索引
ALTER TABLE page_configs
ADD COLUMN page_title VARCHAR(200)
GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(config_data, '$.layout.seo.title'))) VIRTUAL;

CREATE INDEX idx_page_title ON page_configs(page_title);

-- 为组件类型创建虚拟列
ALTER TABLE page_configs
ADD COLUMN component_types JSON
GENERATED ALWAYS AS (JSON_EXTRACT(config_data, '$.components[*].type')) VIRTUAL;
```

### 3. 分区策略

```sql
-- 按基地ID分区
ALTER TABLE page_configs
PARTITION BY HASH(CRC32(base_id))
PARTITIONS 10;

-- 按时间分区
ALTER TABLE page_configs
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 数据迁移策略

### 1. 从旧格式迁移

```sql
-- 创建迁移函数
DELIMITER //
CREATE FUNCTION migrate_page_config(old_config JSON)
RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE new_config JSON;

    -- 转换逻辑
    SET new_config = JSON_OBJECT(
        'id', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.id')),
        'name', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.name')),
        'baseId', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.baseId')),
        'version', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.version')),
        'schemaVersion', '1.0.0',
        'components', JSON_EXTRACT(old_config, '$.components'),
        'layout', JSON_OBJECT(
            'width', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.settings.width')),
            'height', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.settings.height')),
            'backgroundColor', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.settings.backgroundColor')),
            'seo', JSON_OBJECT(
                'title', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.settings.title')),
                'description', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.settings.description')),
                'keywords', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.settings.keywords')),
                'viewport', JSON_UNQUOTE(JSON_EXTRACT(old_config, '$.settings.viewport'))
            )
        ),
        'theme', JSON_OBJECT(
            'header', JSON_EXTRACT(old_config, '$.header'),
            'footer', JSON_EXTRACT(old_config, '$.footer')
        ),
        'metadata', JSON_EXTRACT(old_config, '$.metadata')
    );

    RETURN new_config;
END //
DELIMITER ;
```

### 2. 批量迁移脚本

```sql
-- 批量迁移现有数据
UPDATE page_configs
SET config_data = migrate_page_config(config_data),
    schema_version = '1.0.0'
WHERE schema_version IS NULL OR schema_version < '1.0.0';
```

## 总结

这个优化后的数据库设计提供了：

1. **精简存储**：移除冗余字段，使用JSON存储复杂配置
2. **高效查询**：通过索引和虚拟列优化查询性能
3. **版本控制**：支持配置版本历史管理
4. **统计功能**：提供组件使用统计和分析
5. **向后兼容**：支持旧格式数据迁移
6. **扩展性**：易于添加新的配置字段和功能

通过这种设计，可以高效地存储和查询页面配置，同时保持良好的性能和可维护性。
