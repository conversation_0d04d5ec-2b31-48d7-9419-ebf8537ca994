<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElAutocomplete } from 'element-plus'
import { Search, Location } from '@element-plus/icons-vue'
import { searchTianditu, geocodeAddress, reverseGeocode, type PoiItem } from '@/api/tianditu'

interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
  loading?: boolean
  clearable?: boolean
  size?: 'large' | 'default' | 'small'
  maxResults?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入基地名称进行搜索',
  disabled: false,
  loading: false,
  clearable: true,
  size: 'default',
  maxResults: 10
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'search': [keyword: string]
  'select': [item: PoiItem]
}>()

const inputValue = ref(props.modelValue)
const searchLoading = ref(false)

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

// 监听输入值变化
watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})



// 处理选择
const handleSelect = (item: Record<string, unknown>) => {
  const poiItem = item as unknown as PoiItem
  inputValue.value = poiItem.name
  emit('select', poiItem)
  emit('search', poiItem.name)
}

// 获取建议列表的方法（符合Element Plus Autocomplete的要求）
const querySearch = (queryString: string, cb: (data: PoiItem[]) => void) => {
  if (queryString && queryString.length >= 2) {
    // 直接调用API，不使用中间状态
    searchTianditu({
      keyWord: queryString,
      mapBound: "115.0,28.0,117.0,30.0",
      level: "12",
      queryType: "1",
      start: "0",
      count: props.maxResults.toString(),
      show: "2"
    }).then((response) => {
      if (response.status.infocode === 1000) {
        // 处理不同类型的搜索结果
        if (response.resultType === 1 && response.pois) {
          // POI点数据
          cb(response.pois)
        } else {
          // 如果没有找到POI，尝试地理编码
          geocodeAddress(queryString).then(async (geocodeResponse) => {
            if (geocodeResponse.status === '0' && geocodeResponse.location) {
              const location = geocodeResponse.location

              const reverseGeocodeResponse = await reverseGeocode(parseFloat(location.lon), parseFloat(location.lat))

              if (reverseGeocodeResponse.status === '0' && reverseGeocodeResponse.result) {
                const reverseResult = reverseGeocodeResponse.result
                const geocodeItem: PoiItem = {
                  name: queryString,
                  address: reverseResult.formatted_address,
                  lonlat: `${reverseResult.location.lon},${reverseResult.location.lat}`,
                  poiType: 101,
                  hotPointID: '',
                  source: 'tianditu_geocoder',
                  province: reverseResult.addressComponent.province,
                  city: reverseResult.addressComponent.city,
                  county: reverseResult.addressComponent.county
                }
                cb([geocodeItem])
              } else {
                cb([])
              }
            } else {
              cb([])
            }
          }).catch(() => {
            cb([])
          })
        }
        // else if (response.resultType === 2 && response.statistics) {
        //   // 统计信息，转换为POI格式
        //   const statistics = response.statistics as unknown as Record<string, unknown>
        //   const priorityCitys = statistics.priorityCitys as unknown as Record<string, unknown>[] || []
        //   const poiItems: PoiItem[] = priorityCitys.map((city: Record<string, unknown>) => ({
        //     name: String(city.adminName || ''),
        //     address: String(city.adminName || ''),
        //     lonlat: String(city.lonlat || ''),
        //     poiType: 101,
        //     hotPointID: '',
        //     source: 'tianditu',
        //     province: String(city.adminName || ''),
        //     city: String(city.adminName || '')
        //   }))
        //   cb(poiItems)
        // } else if (response.resultType === 3 && response.area) {
        //   // 行政区数据，转换为POI格式
        //   const area = response.area as unknown as Record<string, unknown>
        //   const areaItem: PoiItem = {
        //     name: String(area.name || ''),
        //     address: String(area.name || ''),
        //     lonlat: String(area.lonlat || ''),
        //     poiType: 101,
        //     hotPointID: '',
        //     source: 'tianditu',
        //     province: String(area.name || ''),
        //     city: String(area.name || '')
        //   }
        //   cb([areaItem])
        // } 

      } else {
        console.warn('天地图搜索返回错误:', response.status.cndesc)
        cb([])
      }
    }).catch((error) => {
      console.error('搜索失败:', error)
      cb([])
    })
  } else {
    cb([])
  }
}

// 格式化地址显示
const formatAddress = (item: PoiItem) => {
  const parts = []
  if (item.province) parts.push(item.province)
  if (item.city) parts.push(item.city)
  if (item.county) parts.push(item.county)
  if (item.address) parts.push(item.address)

  return parts.join(' ')
}

// 格式化坐标
const formatCoordinates = (lonlat: string) => {
  const [lng, lat] = lonlat.split(',')
  return `${parseFloat(lng).toFixed(6)}, ${parseFloat(lat).toFixed(6)}`
}
</script>

<template>
  <div class="smart-search-input">
    <el-autocomplete v-model="inputValue" :fetch-suggestions="querySearch" :placeholder="placeholder"
      :disabled="disabled" :loading="searchLoading || loading" :clearable="clearable" :size="size"
      :trigger-on-focus="false" :highlight-first-item="true" @select="handleSelect">
      <template #prefix>
        <el-icon class="search-icon">
          <Search />
        </el-icon>
      </template>

      <template #default="{ item }">
        <div class="search-suggestion-item">
          <div class="suggestion-main">
            <div class="suggestion-name">{{ (item as PoiItem).name }}</div>
            <div v-if="(item as PoiItem).address || (item as PoiItem).province" class="suggestion-address">
              <el-icon class="location-icon">
                <Location />
              </el-icon>
              {{ formatAddress(item as PoiItem) }}
            </div>
          </div>
          <div v-if="(item as PoiItem).lonlat" class="suggestion-coordinates">
            {{ formatCoordinates((item as PoiItem).lonlat) }}
          </div>
        </div>
      </template>
    </el-autocomplete>
  </div>
</template>

<style scoped>
.smart-search-input {
  width: 100%;
}

.search-icon {
  color: #909399;
}

.search-suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.suggestion-main {
  flex: 1;
  min-width: 0;
}

.suggestion-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1.4;
}

.suggestion-address {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
  line-height: 1.4;
}

.location-icon {
  font-size: 12px;
  color: #c0c4cc;
}

.suggestion-coordinates {
  font-size: 11px;
  color: #c0c4cc;
  text-align: right;
  margin-left: 12px;
  white-space: nowrap;
}

/* Element Plus 自动完成组件样式覆盖 */
:deep(.el-autocomplete-suggestion) {
  max-height: 300px !important;
}

:deep(.el-autocomplete-suggestion__list) {
  max-height: none !important;
}

:deep(.el-autocomplete-suggestion__wrap) {
  max-height: none !important;
}
</style>
