<script setup lang="ts">
import { computed } from 'vue'

// 定义组件属性
interface Props {
  text?: string
  textStyle?: 'body' | 'heading' | 'subtitle' | 'emphasis' | 'quote' | 'code'
  textAlign?: string
  color?: string
  maxWidth?: string
  backgroundColor?: string
  width?: number
  height?: number
  isBasicComponent?: boolean // 用于区分是否为基础组件
}

const props = withDefaults(defineProps<Props>(), {
  text: '请输入文本内容',
  textStyle: 'body',
  textAlign: 'center',
  color: '#333333',
  maxWidth: 'auto',
  backgroundColor: 'white',
  width: undefined, // 全宽
  height: undefined, // 高度自适应
  isBasicComponent: true,
})

// 预设的文本样式
const textStyles = {
  body: {
    fontSize: '16px',
    fontWeight: '400',
    lineHeight: '1.7',
    fontFamily:
      '"Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    letterSpacing: '0.3px',
  },
  heading: {
    fontSize: '32px',
    fontWeight: '700',
    lineHeight: '1.2',
    fontFamily:
      '"Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    letterSpacing: '-0.5px',
  },
  subtitle: {
    fontSize: '24px',
    fontWeight: '600',
    lineHeight: '1.4',
    fontFamily:
      '"Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    letterSpacing: '-0.2px',
  },
  emphasis: {
    fontSize: '20px',
    fontWeight: '600',
    lineHeight: '1.5',
    fontFamily:
      '"Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    letterSpacing: '0px',
  },
  quote: {
    fontSize: '18px',
    fontWeight: '400',
    lineHeight: '1.6',
    fontStyle: 'italic',
    fontFamily: '"Times New Roman", serif',
    borderLeft: '4px solid #409eff',
    paddingLeft: '20px',
    color: '#666',
  },
  code: {
    fontSize: '14px',
    fontWeight: '400',
    lineHeight: '1.5',
    fontFamily: 'Consolas, Monaco, "Courier New", monospace',
    backgroundColor: '#f8f9fa',
    padding: '12px 16px',
    borderRadius: '6px',
    border: '1px solid #e9ecef',
    color: '#495057',
  },
}

// 计算样式
const widgetStyle = computed(() => {
  const baseStyle = textStyles[props.textStyle] || textStyles.body

  return {
    ...baseStyle,
    color: props.color,
    textAlign: props.textAlign,
    backgroundColor: props.backgroundColor,
    width: props.width ? `${props.width}px` : '100%',
    height: props.height ? `${props.height}px` : '100%',
    minHeight: props.height ? `${props.height}px` : '120px',
    maxWidth: props.width ? `${props.width}px` : '100%',
    boxSizing: 'border-box',
    // 改善文本显示
    wordBreak: 'break-word',
    overflowWrap: 'break-word',
    whiteSpace: 'pre-wrap',
    // 移除输入框样式
    borderRadius: '8px',
    outline: 'none',
    resize: 'none',
    // 添加内边距和样式
    margin: '0',
    padding: '12px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: props.textAlign === 'left' ? 'flex-start' : props.textAlign === 'right' ? 'flex-end' : 'center',
    cursor: 'text',
    transition: 'all 0.3s ease',
    flex: '1',
  }
})
</script>

<template>
  <div class="text-widget" :style="widgetStyle as any">
    <div v-if="textStyle === 'quote'" class="quote-text" v-html="text"></div>
    <div v-else-if="textStyle === 'code'" class="code-text" v-html="text"></div>
    <div v-else class="text-content" v-html="text"></div>
  </div>
</template>

<style scoped>
.text-widget {
  position: relative;
  transition: all 0.3s ease;
  /* 移除所有输入框相关的样式 */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: transparent;
  box-shadow: none;
  /* 确保完全填充父容器 */
  width: 100% !important;
  height: 100% !important;
  box-sizing: border-box;
}

/* 悬停效果现在由外层容器处理 */

.text-content {
  display: flex;
  align-items: center;
  justify-content: inherit;
  width: 100%;
  height: 100%;
  min-height: inherit;
  /* 确保文本内容看起来自然 */
  cursor: text;
  user-select: text;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.text-content :deep(p) {
  margin: 0 0 12px 0;
  line-height: inherit;
}

.text-content :deep(p:last-child) {
  margin-bottom: 0;
}

.text-content :deep(ul),
.text-content :deep(ol) {
  margin: 12px 0;
  padding-left: 24px;
}

.text-content :deep(li) {
  margin: 6px 0;
  line-height: inherit;
}

.text-content :deep(strong) {
  font-weight: bold;
}

.text-content :deep(em) {
  font-style: italic;
}

.text-content :deep(u) {
  text-decoration: underline;
}

.text-content :deep(h1),
.text-content :deep(h2),
.text-content :deep(h3),
.text-content :deep(h4),
.text-content :deep(h5),
.text-content :deep(h6) {
  margin: 16px 0 8px 0;
  line-height: inherit;
  font-weight: inherit;
}

.text-content :deep(h1:first-child),
.text-content :deep(h2:first-child),
.text-content :deep(h3:first-child),
.text-content :deep(h4:first-child),
.text-content :deep(h5:first-child),
.text-content :deep(h6:first-child) {
  margin-top: 0;
}

.quote-text {
  display: block;
  color: #666;
  font-style: italic;
  position: relative;
}

.quote-text :deep(p) {
  margin: 0;
}

.code-text {
  display: block;
  color: #333;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px 16px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  position: relative;
  overflow-x: auto;
}

.code-text :deep(p) {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .text-widget {
    font-size: clamp(14px, 4vw, 18px) !important;
  }
}

/* 打印样式 */
@media print {
  .text-widget {
    color: #000 !important;
    background: transparent !important;
  }
}

/* 确保文本组件在画布中看起来自然 */
.text-widget:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
  outline: none;
}

/* 焦点状态 */
.text-widget:active {
  transform: none;
}
</style>
