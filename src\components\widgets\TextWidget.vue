<script setup lang="ts">
import { computed } from 'vue'

// 定义组件属性
interface Props {
  text?: string
  textStyle?: 'body' | 'heading' | 'subtitle' | 'emphasis' | 'quote' | 'code'
  textAlign?: string
  color?: string
  maxWidth?: string
  spacing?: 'compact' | 'normal' | 'loose'
  backgroundColor?: string
  borderRadius?: string
  [key: string]: any
}

const props = withDefaults(defineProps<Props>(), {
  text: '请输入文本内容',
  textStyle: 'body',
  textAlign: 'left',
  color: '#333333',
  maxWidth: 'auto',
  spacing: 'normal',
  backgroundColor: 'transparent',
  borderRadius: '0px'
})

// 预设的文本样式
const textStyles = {
  body: {
    fontSize: '16px',
    fontWeight: '400',
    lineHeight: '1.6',
    fontFamily: '"Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
  },
  heading: {
    fontSize: '28px',
    fontWeight: '600',
    lineHeight: '1.3',
    fontFamily: '"Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
  },
  subtitle: {
    fontSize: '20px',
    fontWeight: '500',
    lineHeight: '1.4',
    fontFamily: '"Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
  },
  emphasis: {
    fontSize: '18px',
    fontWeight: '600',
    lineHeight: '1.5',
    fontFamily: '"Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
  },
  quote: {
    fontSize: '16px',
    fontWeight: '400',
    lineHeight: '1.6',
    fontStyle: 'italic',
    fontFamily: '"Times New Roman", serif',
    borderLeft: '4px solid #409eff',
    paddingLeft: '16px'
  },
  code: {
    fontSize: '14px',
    fontWeight: '400',
    lineHeight: '1.5',
    fontFamily: 'Consolas, Monaco, "Courier New", monospace',
    backgroundColor: '#f5f5f5',
    padding: '8px 12px',
    borderRadius: '4px',
    border: '1px solid #e4e7ed'
  }
}

// 间距配置
const spacingConfig = {
  compact: {
    margin: '8px 0',
    padding: '8px 0'
  },
  normal: {
    margin: '16px 0',
    padding: '12px 0'
  },
  loose: {
    margin: '24px 0',
    padding: '16px 0'
  }
}

// 计算样式
const widgetStyle = computed(() => {
  const baseStyle = textStyles[props.textStyle] || textStyles.body
  const spacing = spacingConfig[props.spacing] || spacingConfig.normal

  return {
    ...baseStyle,
    ...spacing,
    color: props.color,
    textAlign: props.textAlign,
    backgroundColor: props.backgroundColor,
    borderRadius: props.borderRadius,
    maxWidth: props.maxWidth,
    width: '100%',
    boxSizing: 'border-box'
  }
})
</script>

<template>
  <div class="text-widget" :style="widgetStyle as any">
    <span v-if="textStyle === 'quote'" class="quote-text">{{ text }}</span>
    <span v-else-if="textStyle === 'code'" class="code-text">{{ text }}</span>
    <span v-else>{{ text }}</span>
  </div>
</template>

<style scoped>
.text-widget {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.quote-text {
  display: block;
  color: #666;
}

.code-text {
  display: inline-block;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .text-widget {
    font-size: clamp(14px, 4vw, 18px) !important;
  }
}

/* 打印样式 */
@media print {
  .text-widget {
    color: #000 !important;
    background: transparent !important;
  }
}
</style>