<!-- src/components/editor/widgets/FooterWidget.vue -->
<template>
    <footer class="footer-container" :style="{
        backgroundColor: props.backgroundColor,
        color: props.textColor
    }">
        <div class="footer-content">
            <div class="top-line">
                <span v-if="props.contactName || props.contactPhone">
                    联系方式: {{ props.contactName }} {{ props.contactPhone }}
                </span>
                <span v-if="props.address">
                    地址: {{ props.address }}
                </span>
            </div>
            <div class="bottom-lines">
                <p v-if="props.copyrightInfo">{{ props.copyrightInfo }}</p>
                <p v-if="props.licenseInfo">{{ props.licenseInfo }}</p>
            </div>
        </div>
    </footer>
</template>

<script setup lang="ts">
// 定义组件属性
interface Props {
    contactName?: string
    contactPhone?: string
    address?: string
    copyrightInfo?: string
    licenseInfo?: string
    backgroundColor?: string
    textColor?: string
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
    contactName: '韦雅云',
    contactPhone: '010-68755926',
    address: '北京市丰台区万源路1号',
    copyrightInfo: '版权所有 © 2016-2025 北京高校思想政治理论课高精尖创新中心',
    licenseInfo: '增值电信业务经营许可证:京B2-20190536 京ICP备10054422号-13 京公网安备110108002480号',
    backgroundColor: '#c00',
    textColor: '#FFFFFF',
})
</script>

<style scoped>
.footer-container {
    width: 100%;
    padding: 20px 50px;
    box-sizing: border-box;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    font-size: 13px;
    line-height: 1.8;
    opacity: 0.9;
}

.top-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.bottom-lines {
    text-align: center;
}

.bottom-lines p {
    margin: 4px 0;
}
</style>