<script setup lang="ts">
import { User } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '../../../stores/user'

const router = useRouter()
const userStore = useUserStore()

const handleLogout = () => {
    ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        userStore.logout()
        router.push('/login')
    })
}
</script>

<template>
    <div class="admin-header">
        <div class="header-left">
            <h1>实践基地管理</h1>
        </div>
        <div class="header-right">
            <el-dropdown trigger="hover">
                <div class="user-info">
                    <el-avatar :size="32" :icon="User" />
                    <span class="username">{{ userStore.userName }}</span>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item disabled>
                            <div class="user-detail">
                                <div class="user-name">{{ userStore.userName }}</div>
                                <div class="user-phone">{{ userStore.userPhone }}</div>
                            </div>
                        </el-dropdown-item>
                        <el-dropdown-item divided @click="handleLogout">
                            退出登录
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </div>
</template>

<style scoped>
.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: white;
    border-bottom: 1px solid #ebeef5;
}

.header-left h1 {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.user-info:hover {
    background-color: #f5f7fa;
}

.username {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-detail {
    padding: 4px 0;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
}

.user-phone {
    font-size: 12px;
    color: #909399;
}
</style>
