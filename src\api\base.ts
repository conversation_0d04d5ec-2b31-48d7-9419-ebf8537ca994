import api from './config'

// 基地信息接口类型定义
export interface BaseInfo {
  id?: number
  baseName: string
  baseDesc: string
  address: string
  province: string
  provinceCode: string
  city: string
  cityCode: string
  district: string
  districtCode: string
  lat: number
  lng: number
  phone: string
  openTime: string
  image: string
  categoryId: number
  categoryName?: string
  score: number
}

// 新增基地信息
export type AddBaseInfoParams = Omit<BaseInfo, 'id'>

export interface AddBaseInfoResponse {
  code: string
  msg: string
}

export const addBaseInfo = (params: AddBaseInfoParams): Promise<AddBaseInfoResponse> => {
  return api.post('/base/addBaseInfo', params)
}

// 删除基地信息
export interface DeleteBaseInfoParams {
  id: number
}

export interface DeleteBaseInfoResponse {
  code: string
  msg: string
}

export const deleteBaseInfo = (params: DeleteBaseInfoParams): Promise<DeleteBaseInfoResponse> => {
  return api.get(`/base/deleteBaseInfo?id=${params.id}`)
}

// 获取所有基地详情
export interface ListAllBaseInfoResponse {
  code: string
  data: BaseInfo[]
  msg: string
}

export const listAllBaseInfo = (): Promise<ListAllBaseInfoResponse> => {
  return api.get('/base/listAll')
}

// 更新基地信息
export interface UpdateBaseInfoParams extends BaseInfo {
  id: number // 更新时必须提供id
}

export interface UpdateBaseInfoResponse {
  code: string
  data: BaseInfo
  msg: string
}

export const updateBaseInfo = (params: UpdateBaseInfoParams): Promise<UpdateBaseInfoResponse> => {
  return api.post('/base/updateBaseInfo', params)
}
