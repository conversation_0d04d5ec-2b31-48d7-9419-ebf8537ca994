<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Edit, Delete, Search, ArrowLeft, User, Setting } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { mockBases } from '../data/mockData'
import type { PracticeBase } from '../types'

const router = useRouter()
const tableData = ref<PracticeBase[]>([])
const searchQuery = ref('')
const loading = ref(false)
const dialogVisible = ref(false)
const editingBase = ref<PracticeBase | null>(null)
const baseForm = ref({
  name: '',
  city: '',
  category: '',
  address: '',
  description: '',
  phone: '',
  openHours: '',
  imageUrl: '',
  features: [] as string[],
  rating: 5.0
})

const cities = ['南昌市', '井冈山市', '瑞金市', '九江市', '萍乡市', '新余市', '鹰潭市', '赣州市', '宜春市', '上饶市', '吉安市', '抚州市']
const categories = ['革命历史', '爱国主义', '红色文化', '党史教育', '英烈纪念']

const checkAuth = () => {
  const token = localStorage.getItem('admin_token')
  if (!token) {
    ElMessage.error('请先登录')
    router.push('/login')
    return false
  }
  return true
}

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    localStorage.removeItem('admin_token')
    router.push('/login')
  })
}

const goBack = () => {
  // 跳转到主项目首页
  window.location.href = '/'
}

const loadData = () => {
  loading.value = true
  setTimeout(() => {
    tableData.value = [...mockBases]
    loading.value = false
  }, 500)
}

const handleAdd = () => {
  editingBase.value = null
  baseForm.value = {
    name: '',
    city: '',
    category: '',
    address: '',
    description: '',
    phone: '',
    openHours: '',
    imageUrl: '',
    features: [],
    rating: 5.0
  }
  dialogVisible.value = true
}

const handleEdit = (base: PracticeBase) => {
  editingBase.value = base
  baseForm.value = {
    name: base.name,
    city: base.city,
    category: base.category,
    address: base.address,
    description: base.description,
    phone: base.phone || '',
    openHours: base.openHours || '',
    imageUrl: base.imageUrl,
    features: base.features || [],
    rating: base.rating
  }
  dialogVisible.value = true
}

const handleDelete = (base: PracticeBase) => {
  ElMessageBox.confirm(`确定要删除"${base.name}"吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === base.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  })
}

const handleSave = () => {
  if (!baseForm.value.name || !baseForm.value.city || !baseForm.value.address) {
    ElMessage.error('请填写必要信息')
    return
  }

  if (editingBase.value) {
    // Update existing
    const index = tableData.value.findIndex(item => item.id === editingBase.value!.id)
    if (index > -1) {
      tableData.value[index] = { 
        ...baseForm.value, 
        id: editingBase.value.id,
        coordinates: editingBase.value.coordinates
      }
    }
    ElMessage.success('更新成功')
  } else {
    // Add new
    const newBase: PracticeBase = {
      ...baseForm.value,
      id: Date.now().toString(),
      coordinates: { lat: 28.682, lng: 115.858 } // Default coordinates
    }
    tableData.value.push(newBase)
    ElMessage.success('添加成功')
  }
  
  dialogVisible.value = false
}

const handlePageConfig = (base: PracticeBase) => {
  ElMessage.info(`正在进入 "${base.name}" 的页面配置编辑器`)
  router.push(`/lowcode-editor/${base.id}`)
}

const filteredData = computed(() => {
  if (!searchQuery.value) return tableData.value
  return tableData.value.filter(base => 
    base.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    base.city.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

onMounted(() => {
  if (checkAuth()) {
    loadData()
  }
})
</script>

<template>
  <div class="admin-container">
    <!-- Header -->
    <div class="admin-header">
      <div class="header-left">
        <el-button @click="goBack" circle>
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <h1>实践基地管理</h1>
      </div>
      <div class="header-right">
        <el-dropdown>
          <el-button type="primary">
            <el-icon><User /></el-icon>
            管理员
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleLogout">
                <el-icon><ArrowLeft /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- Toolbar -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索基地名称或城市"
          :prefix-icon="Search"
          style="width: 300px"
        />
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          添加基地
        </el-button>
      </div>
    </div>

    <!-- Table -->
    <div class="table-container">
      <el-table 
        :data="filteredData" 
        :loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="基地名称" min-width="200" />
        <el-table-column prop="city" label="城市" width="100" />
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="address" label="地址" min-width="200" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="rating" label="评分" width="80">
          <template #default="{ row }">
            <el-rate 
              v-model="row.rating" 
              disabled 
              text-color="#ff9900"
              score-template="{value}"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button @click="handleEdit(row)" type="primary" size="small">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button @click="handlePageConfig(row)" type="success" size="small" title="页面配置">
              <el-icon><Setting /></el-icon>
            </el-button>
            <el-button @click="handleDelete(row)" type="danger" size="small">
              <el-icon><Delete /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingBase ? '编辑基地' : '添加基地'"
      width="600px"
    >
      <el-form :model="baseForm" label-width="100px">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="基地名称" required>
              <el-input v-model="baseForm.name" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市" required>
              <el-select v-model="baseForm.city" placeholder="请选择城市">
                <el-option
                  v-for="city in cities"
                  :key="city"
                  :label="city"
                  :value="city"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="分类">
              <el-select v-model="baseForm.category" placeholder="请选择分类">
                <el-option
                  v-for="category in categories"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话">
              <el-input v-model="baseForm.phone" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="地址" required>
          <el-input v-model="baseForm.address" />
        </el-form-item>

        <el-form-item label="开放时间">
          <el-input v-model="baseForm.openHours" placeholder="例如：周一至周日 9:00-17:00" />
        </el-form-item>

        <el-form-item label="图片URL">
          <el-input v-model="baseForm.imageUrl" />
        </el-form-item>

        <el-form-item label="详细描述">
          <el-input
            v-model="baseForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入基地的详细介绍"
          />
        </el-form-item>

        <el-form-item label="评分">
          <el-rate v-model="baseForm.rating" text-color="#ff9900" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">
            {{ editingBase ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.admin-container {
  min-height: 100vh;
  background: #f5f7fa;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #ebeef5;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 12px;
}

.table-container {
  padding: 24px;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}

@media (max-width: 768px) {
  .admin-header {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }

  .table-container {
    padding: 12px;
  }
}
</style>
