<template>
    <div class="list-editor">
        <div class="list-header">
            <h4>{{ label }}</h4>
            <el-button type="primary" size="small" @click="addItem">
                <Icon icon="material-symbols:add" style="width: 16px; height: 16px;" />
                添加项
            </el-button>
        </div>

        <div class="list-items">
            <div v-for="(item, index) in localValue" :key="index" class="list-item">
                <div class="item-content">
                    <div v-for="field in itemConfig" :key="field.key" class="field-group">
                        <label>{{ field.label }}</label>

                        <!-- 图片上传 -->
                        <div v-if="field.type === 'image-upload'" class="image-upload-control">
                            <ImageUploader :model-value="(item[field.key] as string) || ''"
                                @update:model-value="(url: string) => handleImageUploadSuccess(item, field.key, url)"
                                @remove="() => handleImageRemove(item, field.key)" :accept="field.accept || 'image/*'"
                                :placeholder="''" :uploading-text="''" :max-size="5" width="80px" height="80px"
                                layout="square" border-radius="6px" />
                        </div>

                        <!-- 视频上传 -->
                        <div v-else-if="field.type === 'video-upload'" class="video-upload-control">
                            <!-- <el-upload action="#" :auto-upload="false" :show-file-list="false"
                                :accept="field.accept || 'video/*'"
                                @change="(file: { raw?: File }) => handleVideoUpload(item, field.key, file)"> -->
                            <el-button size="small" type="primary" plain @click="handleVideoUpload()">
                                <Icon icon="material-symbols:video-library" style="width: 16px; height: 16px;" />
                                上传视频
                            </el-button>
                            <!-- </el-upload> -->
                            <div v-if="item[field.key]" class="video-preview">
                                <div class="video-info">
                                    <Icon icon="material-symbols:video-library" style="width: 24px; height: 24px;" />
                                    <span>视频已上传</span>
                                </div>
                                <el-button size="small" type="danger" circle
                                    @click="item[field.key] = ''; updateValue()">
                                    <Icon icon="material-symbols:delete" style="width: 12px; height: 12px;" />
                                </el-button>
                            </div>
                            <div v-if="field.tip" class="upload-tip">{{ field.tip }}</div>
                        </div>

                        <!-- 文件上传器 -->
                        <FileUploader v-else-if="field.type === 'file-uploader' || field.type === 'video-upload'"
                            :model-value="field.key === 'downloadFile' ? (item[field.key] as { name: string; url: string; description: string } | undefined) : {
                                name: (item.name as string) || '',
                                url: (item.url as string) || '',
                                description: (item.description as string) || ''
                            }" @update:model-value="(val) => {
                                // 根据字段名决定更新逻辑
                                if (field.key === 'downloadFile') {
                                    item[field.key] = val
                                } else {
                                    // 更新文件信息到对应的字段
                                    item.name = val.name
                                    item.url = val.url
                                    // 描述字段单独管理，不由文件上传器更新
                                }
                                updateValue()
                            }" :placeholder="field.placeholder" :accept="field.accept" />



                        <!-- 多行文本输入 -->
                        <el-input v-else-if="field.type === 'textarea'" v-model="item[field.key]" type="textarea"
                            :placeholder="field.placeholder" size="small" :rows="3" @input="updateValue" />

                        <!-- 文本输入 -->
                        <el-input v-else v-model="item[field.key]" :placeholder="field.placeholder" size="small"
                            @input="updateValue" />
                    </div>
                </div>
                <div class="item-actions">
                    <el-button type="danger" size="small" circle @click="removeItem(index)">
                        <el-icon>
                            <Delete />
                        </el-icon>
                    </el-button>
                </div>
            </div>
        </div>

        <div v-if="localValue.length === 0" class="empty-state">
            <Icon icon="material-symbols:list" style="width: 48px; height: 48px; color: #c0c4cc;" />
            <p>暂无信息项，点击"添加项"开始创建</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'

import { Icon } from '@iconify/vue'
import FileUploader from './FileUploader.vue'
import ImageUploader from '../ImageUploader.vue'

interface Props {
    modelValue: Array<Record<string, unknown>>
    label: string
    itemConfig: Array<{
        label: string
        key: string
        type: string
        placeholder?: string
        accept?: string
        tip?: string
    }>
}

const props = defineProps<Props>()
const emit = defineEmits<{
    'update:modelValue': [value: Array<Record<string, unknown>>]
}>()

const localValue = ref<Array<Record<string, unknown>>>([])

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
    localValue.value = [...newValue]
}, { immediate: true, deep: true })

// 添加新项
const addItem = () => {
    const newItem: Record<string, unknown> = {}
    props.itemConfig.forEach(field => {
        // 为文件上传器类型初始化正确的数据结构
        if (field.type === 'file-uploader' || field.type === 'video-upload') {
            // 对于文件/视频上传类型，根据字段名决定数据结构
            if (field.key === 'downloadFile') {
                newItem[field.key] = undefined
            } else {
                newItem.name = ''
                newItem.url = ''
                newItem.description = ''
            }
        } else {
            // 为关键信息网格提供默认值
            if (field.key === 'label') {
                newItem[field.key] = '新信息项'
            } else if (field.key === 'value') {
                newItem[field.key] = '请输入内容'
            } else {
                newItem[field.key] = field.placeholder || ''
            }
        }
    })
    localValue.value.push(newItem)
    updateValue()
}

// 删除项
const removeItem = (index: number) => {
    localValue.value.splice(index, 1)
    updateValue()
}

// 处理图片上传成功 - 使用ImageUploader组件获取图片URL
const handleImageUploadSuccess = (item: Record<string, unknown>, key: string, url: string) => {
    item[key] = url
    updateValue()
}

// 处理图片移除
const handleImageRemove = (item: Record<string, unknown>, key: string) => {
    item[key] = ''
    updateValue()
}

// 处理视频上传
const handleVideoUpload = () => {
    ElMessage.error('视频上传正在开发中')
}

// 更新值
const updateValue = () => {
    emit('update:modelValue', [...localValue.value])
}
</script>

<style scoped>
.list-editor {
    width: 100%;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.list-header h4 {
    margin: 0;
    font-size: 14px;
    color: #303133;
    font-weight: 600;
}

.list-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.list-item {
    display: flex;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background: #fafbfc;
}

.item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.field-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.field-group label {
    font-size: 12px;
    color: #909399;
    font-weight: 500;
}

.item-actions {
    display: flex;
    align-items: flex-start;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;
}

.empty-state p {
    margin: 16px 0 0 0;
    font-size: 14px;
}

/* 图片上传样式 */
.image-upload-control {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 视频上传样式 */
.video-upload-control {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.video-preview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
}

.video-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #409eff;
    font-size: 14px;
}

.video-preview .el-button {
    width: 20px;
    height: 20px;
    padding: 0;
}
</style>