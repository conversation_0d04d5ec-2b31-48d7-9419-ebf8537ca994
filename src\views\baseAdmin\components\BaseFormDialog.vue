<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import { Close, Plus, Edit, Delete, Location, LocationFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { BaseInfo } from '../../../api/base'
import {
  addCategory,
  deleteCategory,
  listAllCategory,
  updateCategory,
  type Category,
} from '../../../api/category'
import ImageUploader from '../../../components/ImageUploader.vue'
import SmartSearchInput from '../../../components/SmartSearchInput.vue'
import { geocodeAddress } from '../../../api/tianditu'

interface Props {
  visible: boolean
  editingBase: BaseInfo | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'save', baseData: Partial<BaseInfo>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const baseForm = ref({
  baseName: '',
  city: '',
  categoryId: 1,
  categoryName: '',
  address: '',
  baseDesc: '',
  phone: '',
  openTime: '',
  openTimeRange: null as [Date, Date] | null,
  image: '',
  province: '江西省',
  provinceCode: '360000',
  cityCode: '',
  district: '',
  districtCode: '',
  lat: 28.682,
  lng: 115.858,
  score: 5.0,
})

// 选项数据 - 已移除，城市现在通过地址自动获取

// 分类管理相关
const categoryList = ref<Category[]>([])
const categoryLoading = ref(false)
const categoryDialogVisible = ref(false)
const categoryListVisible = ref(false)
const editingCategory = ref<Category | null>(null)
const categoryForm = ref({
  title: '',
  color: '#409EFF',
})

// 获取分类列表
const loadCategoryList = async () => {
  categoryLoading.value = true
  try {
    const response = await listAllCategory()
    if (response.code === '200') {
      categoryList.value = response.data
    }
  } catch (error) {
    console.error('获取分类列表错误:', error)
  } finally {
    categoryLoading.value = false
  }
}

// 分类管理方法
const handleAddCategory = () => {
  editingCategory.value = null
  categoryForm.value = { title: '', color: '#409EFF' }
  categoryDialogVisible.value = true
}

const handleManageCategories = () => {
  categoryListVisible.value = true
}

const handleEditCategory = (category: Category) => {
  editingCategory.value = category
  categoryForm.value = { title: category.title, color: category.color }
  categoryDialogVisible.value = true
}

// 删除分类
const handleDeleteCategory = async (category: Category) => {
  if (!category.id) return
  try {
    const response = await deleteCategory({ id: category.id })
    if (response.code === '200') {
      ElMessage.success('分类删除成功')
      loadCategoryList()
    }
  } catch (error) {
    console.error('删除分类错误:', error)
  }
}

// 保存分类
const handleSaveCategory = async () => {
  try {
    const response = editingCategory.value
      ? await updateCategory({
        id: editingCategory.value.id!,
        title: categoryForm.value.title,
        color: categoryForm.value.color,
      })
      : await addCategory({
        title: categoryForm.value.title,
        color: categoryForm.value.color,
      })

    if (response.code === '200') {
      ElMessage.success(`分类${editingCategory.value ? '更新' : '添加'}成功`)
      categoryDialogVisible.value = false
      categoryForm.value = { title: '', color: '#409EFF' }
      editingCategory.value = null
      loadCategoryList()
    }
  } catch (error) {
    console.error('保存分类错误:', error)
  }
}

// 初始化时获取分类列表
onMounted(() => {
  loadCategoryList()
})

// 重置表单
const resetForm = () => {
  baseForm.value = {
    baseName: '',
    city: '',
    categoryId: 1,
    categoryName: '',
    address: '',
    baseDesc: '',
    phone: '',
    openTime: '',
    openTimeRange: null,
    image: '',
    province: '江西省',
    provinceCode: '360000',
    cityCode: '',
    district: '',
    districtCode: '',
    lat: 28.682,
    lng: 115.858,
    score: 5.0,
  }
}

// 计算属性
const dialogTitle = computed(() => (props.editingBase ? '编辑基地' : '添加基地'))

// 监听对话框显示状态和编辑数据变化
watch(
  [() => props.visible, () => props.editingBase],
  ([visible, editingBase]) => {
    if (visible) {
      // 对话框打开时，根据是否有编辑数据来决定是新增还是编辑
      if (editingBase) {
        // 编辑模式：回显数据
        baseForm.value = {
          baseName: editingBase.baseName,
          city: editingBase.city,
          categoryId: editingBase.categoryId,
          categoryName: editingBase.categoryName || '',
          address: editingBase.address,
          baseDesc: editingBase.baseDesc,
          phone: editingBase.phone || '',
          openTime: editingBase.openTime || '',
          openTimeRange: parseOpenTimeToRange(editingBase.openTime || ''),
          image: editingBase.image || '',
          province: editingBase.province || '江西省',
          provinceCode: editingBase.provinceCode || '360000',
          cityCode: editingBase.cityCode || '',
          district: editingBase.district || '',
          districtCode: editingBase.districtCode || '',
          lat: editingBase.lat || 28.682,
          lng: editingBase.lng || 115.858,
          score: editingBase.score || 5.0,
        }
      } else {
        // 新增模式：重置为空
        baseForm.value = {
          baseName: '',
          city: '',
          categoryId: 1,
          categoryName: '',
          address: '',
          baseDesc: '',
          phone: '',
          openTime: '',
          openTimeRange: null,
          image: '',
          province: '江西省',
          provinceCode: '360000',
          cityCode: '',
          district: '',
          districtCode: '',
          lat: 28.682,
          lng: 115.858,
          score: 5.0,
        }
      }
    }
  },
  { immediate: false },
)

// 关闭对话框
const handleClose = () => {
  // 关闭对话框时重置表单
  resetForm()
  emit('update:visible', false)
}

// 保存数据
const handleSave = () => {
  if (!baseForm.value.baseName || !baseForm.value.city || !baseForm.value.address) {
    ElMessage.error('请填写必要信息')
    return
  }

  emit('save', { ...baseForm.value })
  handleClose()
}

// 图片上传处理函数
const handleImageUploadSuccess = () => {
  ElMessage.success('图片上传成功')
}

const handleImageUploadError = (error: Error) => {
  ElMessage.error('图片上传失败：' + error.message)
}

// 处理POI选择
const handlePoiSelect = (poi: {
  name: string
  address?: string
  city?: string
  lonlat?: string
}) => {
  // 自动填充地址信息
  if (poi.address) {
    baseForm.value.address = poi.address
  }

  // 自动填充城市信息
  if (poi.city) {
    baseForm.value.city = poi.city
  }

  // 如果有坐标信息，直接保存到表单中
  if (poi.lonlat) {
    const [lng, lat] = poi.lonlat.split(',')
    baseForm.value.lng = parseFloat(lng)
    baseForm.value.lat = parseFloat(lat)
    console.log('选择的坐标:', { lng: parseFloat(lng), lat: parseFloat(lat) })
  }

  ElMessage.success(`已选择: ${poi.name}`)
}

// 处理分类选择
const handleSelectCategory = (category: Category) => {
  baseForm.value.categoryId = category.id || 1
  baseForm.value.categoryName = category.title
  categoryListVisible.value = false
  ElMessage.success(`已选择分类: ${category.title}`)
}

// 根据地址获取坐标和城市信息
const getCoordinatesFromAddress = async (address: string) => {
  if (!address.trim()) return

  try {
    const response = await geocodeAddress(address)
    if (response.status === '0' && response.result?.location) {
      const { lon, lat } = response.result.location
      baseForm.value.lng = lon
      baseForm.value.lat = lat
      console.log('获取到坐标:', { lng: lon, lat: lat })

      // 获取城市信息
      if (response.result.addressComponent?.city) {
        baseForm.value.city = response.result.addressComponent.city
      }
    }
  } catch (error) {
    console.error('获取坐标失败:', error)
  }
}

// 监听地址变化，自动获取坐标
watch(
  () => baseForm.value.address,
  (newAddress) => {
    if (newAddress && newAddress.trim()) {
      // 延迟获取坐标，避免频繁请求
      setTimeout(() => {
        getCoordinatesFromAddress(newAddress)
      }, 1000)
    }
  },
)

// 解析开放时间字符串为日期范围
const parseOpenTimeToRange = (openTimeStr: string): [Date, Date] | null => {
  if (!openTimeStr || !openTimeStr.includes(' - ')) {
    return null
  }

  try {
    const [startStr, endStr] = openTimeStr.split(' - ')

    // 解析格式：MM月DD日 周X HH:MM
    const parseTimeString = (timeStr: string): Date => {
      const match = timeStr.match(/(\d{1,2})月(\d{1,2})日 周[一二三四五六日] (\d{1,2}):(\d{1,2})/)
      if (match) {
        const month = parseInt(match[1]) - 1 // 月份从0开始
        const day = parseInt(match[2])
        const hours = parseInt(match[3])
        const minutes = parseInt(match[4])

        // 使用当前年份，或者可以根据需要调整
        const year = new Date().getFullYear()
        return new Date(year, month, day, hours, minutes)
      }
      return new Date()
    }

    const startDate = parseTimeString(startStr)
    const endDate = parseTimeString(endStr)

    return [startDate, endDate]
  } catch (error) {
    console.error('解析开放时间失败:', error)
    return null
  }
}

// 处理开放时间变化
const handleOpenTimeChange = (value: [Date, Date] | null) => {
  if (value && value.length === 2) {
    const startDate = value[0]
    const endDate = value[1]

    // 格式化开放时间字符串
    const formatTime = (date: Date) => {
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekday = weekdays[date.getDay()]
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')

      return `${month}月${day}日 ${weekday} ${hours}:${minutes}`
    }

    const startTime = formatTime(startDate)
    const endTime = formatTime(endDate)
    baseForm.value.openTime = `${startTime} - ${endTime}`
  } else {
    baseForm.value.openTime = ''
  }
}
</script>

<template>
  <el-dialog :model-value="visible" :title="dialogTitle" width="800px" @close="handleClose"
    @update:model-value="(val: boolean) => emit('update:visible', val)">
    <el-form :model="baseForm" label-width="100px">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="基地名称" required>
            <SmartSearchInput v-model="baseForm.baseName" placeholder="请输入基地名称进行搜索" :max-results="15"
              @select="handlePoiSelect" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="城市" required>
            <el-input v-model="baseForm.city" readonly placeholder="城市将根据地址自动获取" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="基地分类">
            <el-select v-model="baseForm.categoryName" placeholder="请选择分类" :loading="categoryLoading"
              class="category-select" @click="handleManageCategories">
              <el-option v-for="category in categoryList" :key="category.id" :label="category.title"
                :value="category.title">
                <div class="category-option">
                  <el-icon class="category-icon">
                    <Location />
                  </el-icon>
                  <span>{{ category.title }}</span>
                </div>
              </el-option>
              <template #suffix>
                <el-icon class="category-add-icon" @click.stop="handleManageCategories" title="管理分类">
                  <Plus />
                </el-icon>
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话">
            <el-input v-model="baseForm.phone" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="地址" required>
        <el-input v-model="baseForm.address" />
      </el-form-item>

      <el-form-item label="开放时间">
        <el-date-picker :model-value="baseForm.openTimeRange || undefined" type="datetimerange" start-placeholder="开始时间"
          end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD" time-format="HH:mm:ss"
          @update:model-value="(val) => baseForm.openTimeRange = val" @change="handleOpenTimeChange" />
      </el-form-item>

      <el-form-item label="基地图片">
        <ImageUploader v-model="baseForm.image" :width="200" :height="150" layout="rectangle" :max-size="2"
          placeholder="拖拽图片到此处或点击上传" accept="image/jpeg,image/png" @upload-success="handleImageUploadSuccess"
          @upload-error="handleImageUploadError" />
        <div class="upload-tip">支持 jpg/png 格式，文件大小不超过 2MB</div>
      </el-form-item>

      <el-form-item label="详细描述">
        <el-input v-model="baseForm.baseDesc" type="textarea" :rows="4" placeholder="请输入基地的详细介绍" />
      </el-form-item>

      <el-form-item label="评分">
        <el-rate v-model="baseForm.score" text-color="#ff9900" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">
          {{ editingBase ? '更新' : '添加' }}
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 分类管理对话框 -->
  <el-dialog v-model="categoryListVisible" title="分类管理" width="620px">
    <div class="category-management">
      <!-- 分类列表 -->
      <div v-if="!categoryDialogVisible" class="category-list-section">
        <div class="category-list-header">
          <h4>现有分类</h4>
          <el-button type="primary" size="small" @click="handleAddCategory">
            <el-icon>
              <Plus />
            </el-icon>
            新增分类
          </el-button>
        </div>
        <div class="category-list">
          <div v-if="categoryLoading" class="loading">加载中...</div>
          <div v-else-if="categoryList.length === 0" class="empty">暂无分类</div>
          <div v-else class="category-items">
            <div v-for="category in categoryList" :key="category.id" class="category-item">
              <div class="category-info" @click="handleSelectCategory(category)" style="cursor: pointer">
                <el-icon class="category-icon" :style="{ color: category.color }" size="27">
                  <LocationFilled />
                </el-icon>
                <div class="category-details">
                  <span class="category-title">{{ category.title }}</span>
                </div>
              </div>
              <div class="category-actions">
                <el-button type="primary" circle size="small" @click="handleEditCategory(category)" title="编辑">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </el-button>
                <el-button type="danger" circle size="small" @click="handleDeleteCategory(category)" title="删除">
                  <el-icon>
                    <Delete />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类表单 -->
      <div v-else class="category-form-section">
        <div class="category-form-header">
          <h4>{{ editingCategory ? '编辑分类' : '新增分类' }}</h4>
          <el-button link @click="categoryDialogVisible = false" class="back-btn">
            <el-icon>
              <Close />
            </el-icon>
          </el-button>
        </div>
        <el-form :model="categoryForm" label-width="80px" class="category-form">
          <el-form-item label="分类名称" required>
            <el-input v-model="categoryForm.title" placeholder="请输入分类名称" />
          </el-form-item>
          <el-form-item label="分类颜色">
            <el-color-picker v-model="categoryForm.color" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSaveCategory">
              {{ editingCategory ? '更新' : '添加' }}
            </el-button>
            <el-button @click="categoryDialogVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.upload-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}

/* 分类管理样式 */
.category-select {
  width: 100%;
}

.category-add-icon {
  cursor: pointer;
  color: #409eff;
  transition: all 0.3s ease;
  margin-right: 8px;
}

.category-add-icon:hover {
  color: #66b1ff;
  transform: scale(1.1);
}

.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-icon {
  font-size: 16px;
  flex-shrink: 0;
}

/* 分类管理对话框样式 */
.category-management {
  min-height: 400px;
}

.category-list-section,
.category-form-section {
  height: 100%;
}

.category-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.category-list-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.category-list {
  max-height: 320px;
  overflow-y: auto;
}

.loading,
.empty {
  text-align: center;
  padding: 40px;
  color: #909399;
  font-size: 14px;
}

.category-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.3s ease;
}

.category-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.category-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.category-details {
  display: flex;
  flex-direction: column;
}

.category-title {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.category-actions {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.category-actions .el-button {
  transition: all 0.3s ease;
}

.category-actions .el-button:hover {
  transform: scale(1.1);
}

/* 分类表单样式 */
.category-form-section {
  padding: 0;
}

.category-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.category-form-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.back-btn {
  color: #909399;
  transition: color 0.3s ease;
}

.back-btn:hover {
  color: #409eff;
}

.category-form {
  padding: 0;
}

.category-form .el-form-item:last-child {
  margin-bottom: 0;
  text-align: center;
}

.category-form .el-form-item:last-child .el-button {
  margin: 0 8px;
}
</style>
