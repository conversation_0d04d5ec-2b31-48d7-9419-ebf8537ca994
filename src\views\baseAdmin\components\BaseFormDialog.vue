<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import { Close, Plus, Edit, Delete, Location } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { BaseInfo } from '../../../api/base'
import { addCategory, deleteCategory, listAllCategory, updateCategory, type Category } from '../../../api/category'
import ImageUploader from '../../../components/ImageUploader.vue'
import SmartSearchInput from '../../../components/SmartSearchInput.vue'

interface Props {
    visible: boolean
    editingBase: BaseInfo | null
}

interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'save', baseData: Partial<BaseInfo>): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const baseForm = ref({
    baseName: '',
    city: '',
    categoryId: 1,
    categoryName: '',
    address: '',
    baseDesc: '',
    phone: '',
    openTime: '',
    image: '',
    province: '江西省',
    provinceCode: '360000',
    cityCode: '',
    district: '',
    districtCode: '',
    lat: 28.682,
    lng: 115.858,
    score: 5.0
})



// 选项数据
const cities = ['南昌市', '井冈山市', '瑞金市', '九江市', '萍乡市', '新余市', '鹰潭市', '赣州市', '宜春市', '上饶市', '吉安市', '抚州市']

// 分类管理相关
const categoryList = ref<Category[]>([])
const categoryLoading = ref(false)
const categoryDialogVisible = ref(false)
const categoryListVisible = ref(false)
const editingCategory = ref<Category | null>(null)
const categoryForm = ref({
    title: '',
    color: '#409EFF'
})

// 获取分类列表
const fetchCategoryList = async () => {
    categoryLoading.value = true
    try {
        const response = await listAllCategory()
        if (response.code === '200') {
            categoryList.value = response.data
        } else {
            ElMessage.error(response.msg || '获取分类列表失败')
        }
    } catch (error) {
        console.error('获取分类列表错误:', error)
        ElMessage.error('获取分类列表失败，请检查网络连接')
    } finally {
        categoryLoading.value = false
    }
}

// 分类管理方法
const handleAddCategory = () => {
    editingCategory.value = null
    categoryForm.value = { title: '', color: '#409EFF' }
    categoryDialogVisible.value = true
}

const handleManageCategories = () => {
    categoryListVisible.value = true
}

const handleEditCategory = (category: Category) => {
    editingCategory.value = category
    categoryForm.value = { title: category.title, color: category.color }
    categoryDialogVisible.value = true
}

const handleDeleteCategory = async (category: Category) => {
    if (!category.id) return

    try {
        const response = await deleteCategory({ id: category.id })
        if (response.code === '200') {
            ElMessage.success('删除分类成功')
            fetchCategoryList()
        } else {
            ElMessage.error(response.msg || '删除分类失败')
        }
    } catch (error) {
        console.error('删除分类错误:', error)
        ElMessage.error('删除分类失败，请检查网络连接')
    }
}

const handleSaveCategory = async () => {
    if (!categoryForm.value.title.trim()) {
        ElMessage.error('请输入分类名称')
        return
    }

    try {
        if (editingCategory.value) {
            // 更新分类
            const response = await updateCategory({
                id: editingCategory.value.id!,
                title: categoryForm.value.title,
                color: categoryForm.value.color
            })
            if (response.code === '200') {
                ElMessage.success('更新分类成功')
                categoryDialogVisible.value = false
                fetchCategoryList()
            } else {
                ElMessage.error(response.msg || '更新分类失败')
            }
        } else {
            // 新增分类
            const response = await addCategory({
                title: categoryForm.value.title,
                color: categoryForm.value.color
            })
            if (response.code === '200') {
                ElMessage.success('新增分类成功')
                categoryDialogVisible.value = false
                fetchCategoryList()
            } else {
                ElMessage.error(response.msg || '新增分类失败')
            }
        }
    } catch (error) {
        console.error('保存分类错误:', error)
        ElMessage.error('保存分类失败，请检查网络连接')
    }
}

// 初始化时获取分类列表
onMounted(() => {
    fetchCategoryList()
})

// 重置表单
const resetForm = () => {
    baseForm.value = {
        baseName: '',
        city: '',
        categoryId: 1,
        categoryName: '',
        address: '',
        baseDesc: '',
        phone: '',
        openTime: '',
        image: '',
        province: '江西省',
        provinceCode: '360000',
        cityCode: '',
        district: '',
        districtCode: '',
        lat: 28.682,
        lng: 115.858,
        score: 5.0
    }
}

// 计算属性
const dialogTitle = computed(() => props.editingBase ? '编辑基地' : '添加基地')

// 监听对话框显示状态和编辑数据变化
watch([() => props.visible, () => props.editingBase], ([visible, editingBase]) => {
    if (visible) {
        // 对话框打开时，根据是否有编辑数据来决定是新增还是编辑
        if (editingBase) {
            // 编辑模式：回显数据
            baseForm.value = {
                baseName: editingBase.baseName,
                city: editingBase.city,
                categoryId: editingBase.categoryId,
                categoryName: editingBase.categoryName || '',
                address: editingBase.address,
                baseDesc: editingBase.baseDesc,
                phone: editingBase.phone || '',
                openTime: editingBase.openTime || '',
                image: editingBase.image || '',
                province: editingBase.province || '江西省',
                provinceCode: editingBase.provinceCode || '360000',
                cityCode: editingBase.cityCode || '',
                district: editingBase.district || '',
                districtCode: editingBase.districtCode || '',
                lat: editingBase.lat || 28.682,
                lng: editingBase.lng || 115.858,
                score: editingBase.score || 5.0
            }
        } else {
            // 新增模式：重置为空
            resetForm()
        }
    }
}, { immediate: false })

// 关闭对话框
const handleClose = () => {
    // 关闭对话框时重置表单
    resetForm()
    emit('update:visible', false)
}

// 保存数据
const handleSave = () => {
    if (!baseForm.value.baseName || !baseForm.value.city || !baseForm.value.address) {
        ElMessage.error('请填写必要信息')
        return
    }

    emit('save', { ...baseForm.value })
    handleClose()
}

// 图片上传处理函数
const handleImageUploadSuccess = () => {
    ElMessage.success('图片上传成功')
}

const handleImageUploadError = (error: Error) => {
    ElMessage.error('图片上传失败：' + error.message)
}

// 处理POI选择
const handlePoiSelect = (poi: { name: string; address?: string; city?: string; lonlat?: string }) => {
    // 自动填充地址信息
    if (poi.address) {
        baseForm.value.address = poi.address
    }

    // 自动填充城市信息
    if (poi.city) {
        baseForm.value.city = poi.city
    }

    // 如果有坐标信息，可以保存到表单中
    if (poi.lonlat) {
        const [lng, lat] = poi.lonlat.split(',')
        // 这里可以将坐标保存到表单的隐藏字段中
        console.log('选择的坐标:', { lng: parseFloat(lng), lat: parseFloat(lat) })
    }

    ElMessage.success(`已选择: ${poi.name}`)
}

// 处理分类选择
const handleSelectCategory = (category: Category) => {
    baseForm.value.categoryId = category.id || 1
    baseForm.value.categoryName = category.title
    categoryListVisible.value = false
    ElMessage.success(`已选择分类: ${category.title}`)
}
</script>

<template>
    <el-dialog :model-value="visible" :title="dialogTitle" width="800px" @close="handleClose"
        @update:model-value="(val: boolean) => emit('update:visible', val)">
        <el-form :model="baseForm" label-width="100px">
            <el-row :gutter="16">
                <el-col :span="12">
                    <el-form-item label="基地名称" required>
                        <SmartSearchInput v-model="baseForm.baseName" placeholder="请输入基地名称进行搜索" :max-results="15"
                            @select="handlePoiSelect" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="城市" required>
                        <el-select v-model="baseForm.city" placeholder="请选择城市">
                            <el-option v-for="city in cities" :key="city" :label="city" :value="city" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="16">
                <el-col :span="12">
                    <el-form-item label="基地分类">
                        <el-select v-model="baseForm.categoryName" placeholder="请选择分类" :loading="categoryLoading"
                            class="category-select" @click="handleManageCategories">
                            <el-option v-for="category in categoryList" :key="category.id" :label="category.title"
                                :value="category.title">
                                <div class="category-option">
                                    <el-icon class="category-icon">
                                        <Location />
                                    </el-icon>
                                    <span>{{ category.title }}</span>
                                </div>
                            </el-option>
                            <template #suffix>
                                <el-icon class="category-add-icon" @click.stop="handleManageCategories" title="管理分类">
                                    <Plus />
                                </el-icon>
                            </template>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="联系电话">
                        <el-input v-model="baseForm.phone" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-form-item label="地址" required>
                <el-input v-model="baseForm.address" />
            </el-form-item>

            <el-form-item label="开放时间">
                <el-input v-model="baseForm.openTime" placeholder="例如：周一至周日 9:00-17:00" />
            </el-form-item>

            <el-form-item label="基地图片">
                <ImageUploader v-model="baseForm.image" :width="200" :height="150" layout="rectangle" :max-size="2"
                    placeholder="拖拽图片到此处或点击上传" accept="image/jpeg,image/png" @upload-success="handleImageUploadSuccess"
                    @upload-error="handleImageUploadError" />
                <div class="upload-tip">
                    支持 jpg/png 格式，文件大小不超过 2MB
                </div>
            </el-form-item>

            <el-form-item label="详细描述">
                <el-input v-model="baseForm.baseDesc" type="textarea" :rows="4" placeholder="请输入基地的详细介绍" />
            </el-form-item>

            <el-form-item label="评分">
                <el-rate v-model="baseForm.score" text-color="#ff9900" />
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSave">
                    {{ editingBase ? '更新' : '添加' }}
                </el-button>
            </span>
        </template>
    </el-dialog>



    <!-- 分类管理对话框 -->
    <el-dialog v-model="categoryListVisible" title="分类管理" width="620px">
        <div class="category-management">
            <!-- 分类列表 -->
            <div v-if="!categoryDialogVisible" class="category-list-section">
                <div class="category-list-header">
                    <h4>现有分类</h4>
                    <el-button type="primary" size="small" @click="handleAddCategory">
                        <el-icon>
                            <Plus />
                        </el-icon>
                        新增分类
                    </el-button>
                </div>
                <div class="category-list">
                    <div v-if="categoryLoading" class="loading">加载中...</div>
                    <div v-else-if="categoryList.length === 0" class="empty">暂无分类</div>
                    <div v-else class="category-items">
                        <div v-for="category in categoryList" :key="category.id" class="category-item">
                            <div class="category-info" @click="handleSelectCategory(category)" style="cursor: pointer;">
                                <el-icon class="category-icon" :style="{ color: category.color }" size="27">
                                    <LocationFilled />
                                </el-icon>
                                <div class="category-details">
                                    <span class="category-title">{{ category.title }}</span>
                                </div>
                            </div>
                            <div class="category-actions">
                                <el-button type="primary" circle size="small" @click="handleEditCategory(category)"
                                    title="编辑">
                                    <el-icon>
                                        <Edit />
                                    </el-icon>
                                </el-button>
                                <el-button type="danger" circle size="small" @click="handleDeleteCategory(category)"
                                    title="删除">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分类表单 -->
            <div v-else class="category-form-section">
                <div class="category-form-header">
                    <h4>{{ editingCategory ? '编辑分类' : '新增分类' }}</h4>
                    <el-button type="text" @click="categoryDialogVisible = false" class="back-btn">
                        <el-icon>
                            <Close />
                        </el-icon>
                    </el-button>
                </div>
                <el-form :model="categoryForm" label-width="80px" class="category-form">
                    <el-form-item label="分类名称" required>
                        <el-input v-model="categoryForm.title" placeholder="请输入分类名称" />
                    </el-form-item>
                    <el-form-item label="分类颜色">
                        <el-color-picker v-model="categoryForm.color" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSaveCategory">
                            {{ editingCategory ? '更新' : '添加' }}
                        </el-button>
                        <el-button @click="categoryDialogVisible = false">取消</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </el-dialog>
</template>

<style scoped>
.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.upload-tip {
    margin-top: 8px;
    color: #909399;
    font-size: 12px;
}

/* 分类管理样式 */
.category-select {
    width: 100%;
}

.category-add-icon {
    cursor: pointer;
    color: #409eff;
    transition: all 0.3s ease;
    margin-right: 8px;
}

.category-add-icon:hover {
    color: #66b1ff;
    transform: scale(1.1);
}

.category-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.category-icon {
    font-size: 16px;
    flex-shrink: 0;
}

/* 分类管理对话框样式 */
.category-management {
    min-height: 400px;
}

.category-list-section,
.category-form-section {
    height: 100%;
}

.category-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;
}

.category-list-header h4 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
}

.category-list {
    max-height: 320px;
    overflow-y: auto;
}

.loading,
.empty {
    text-align: center;
    padding: 40px;
    color: #909399;
    font-size: 14px;
}

.category-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #ffffff;
    transition: all 0.3s ease;
}

.category-item:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.category-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.category-details {
    display: flex;
    flex-direction: column;
}

.category-title {
    color: #303133;
    font-size: 14px;
    font-weight: 500;
}

.category-actions {
    display: flex;
    gap: 6px;
    flex-shrink: 0;
}

.category-actions .el-button {
    transition: all 0.3s ease;
}

.category-actions .el-button:hover {
    transform: scale(1.1);
}

/* 分类表单样式 */
.category-form-section {
    padding: 0;
}

.category-form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;
}

.category-form-header h4 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
}

.back-btn {
    color: #909399;
    transition: color 0.3s ease;
}

.back-btn:hover {
    color: #409eff;
}

.category-form {
    padding: 0;
}

.category-form .el-form-item:last-child {
    margin-bottom: 0;
    text-align: center;
}

.category-form .el-form-item:last-child .el-button {
    margin: 0 8px;
}
</style>
