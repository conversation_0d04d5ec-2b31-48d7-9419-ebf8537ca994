<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import type { CanvasComponent } from '../../types/editor'
import { getComponentConfig } from '../../types/componentConfigs'
import DynamicPropertyForm from './DynamicPropertyForm.vue'

// 定义组件属性
interface Props {
    selectedComponent: CanvasComponent | null
    componentProperties: Record<string, unknown>
    pageSettings?: {
        title: string
        description: string
        keywords: string
        viewport: string
        width: string
        height: string
        backgroundColor: string
    }
    isHeaderSelected?: boolean
    headerSettings?: {
        title: string
        backgroundColor: string
        textColor: string
        height: string
        fontSize: string
        fontWeight: string
    }
    isFooterSelected?: boolean
    footerSettings?: {
        contactName: string
        contactPhone: string
        address: string
        copyrightInfo: string
        licenseInfo: string
        backgroundColor: string
        textColor: string
    }
}

// 定义组件事件
interface Emits {
    (e: 'propertyChange', properties: Record<string, unknown>): void
    (e: 'pageSettingsChange', settings: Props['pageSettings']): void
    (e: 'headerSettingsChange', settings: Props['headerSettings']): void
    (e: 'footerSettingsChange', settings: Props['footerSettings']): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 获取当前组件的配置
const currentComponentConfig = computed(() => {
    if (!props.selectedComponent) return null
    return getComponentConfig(props.selectedComponent.type)
})

// 属性变更处理
const handlePropertyChange = (properties: Record<string, unknown>) => {
    emit('propertyChange', properties)
}

// 注意：页面设置、Header设置、Footer设置的变更通过computed属性的setter直接处理

// 计算属性处理页面设置的双向绑定
const pageTitle = computed({
    get: () => props.pageSettings?.title || '',
    set: (value: string) => {
        if (props.pageSettings) {
            // 同时更新页面标题和Header标题，保持同步
            emit('pageSettingsChange', { ...props.pageSettings, title: value })
            if (props.headerSettings) {
                emit('headerSettingsChange', { ...props.headerSettings, title: value })
            }
        }
    }
})

// 页面描述
const pageDescription = computed({
    get: () => props.pageSettings?.description || '',
    set: (value: string) => {
        if (props.pageSettings) {
            emit('pageSettingsChange', { ...props.pageSettings, description: value })
        }
    }
})

// 页面宽度
const pageWidth = computed({
    get: () => props.pageSettings?.width || '100%',
    set: (value: string) => {
        if (props.pageSettings) {
            emit('pageSettingsChange', { ...props.pageSettings, width: value })
        }
    }
})

// 页面高度
const pageHeight = computed({
    get: () => props.pageSettings?.height || 'auto',
    set: (value: string) => {
        if (props.pageSettings) {
            emit('pageSettingsChange', { ...props.pageSettings, height: value })
        }
    }
})

// 页面背景颜色
const pageBackgroundColor = computed({
    get: () => props.pageSettings?.backgroundColor || '#ffffff',
    set: (value: string) => {
        if (props.pageSettings) {
            emit('pageSettingsChange', { ...props.pageSettings, backgroundColor: value })
        }
    }
})

const pageKeywords = computed({
    get: () => props.pageSettings?.keywords || '',
    set: (value: string) => {
        if (props.pageSettings) {
            emit('pageSettingsChange', { ...props.pageSettings, keywords: value })
        }
    }
})

// 计算属性处理Header设置的双向绑定
const headerTitle = computed({
    get: () => props.headerSettings?.title || '',
    set: (value: string) => {
        if (props.headerSettings) {
            emit('headerSettingsChange', { ...props.headerSettings, title: value })
        }
    }
})

const headerBackgroundColor = computed({
    get: () => props.headerSettings?.backgroundColor || '#c00',
    set: (value: string) => {
        if (props.headerSettings) {
            emit('headerSettingsChange', { ...props.headerSettings, backgroundColor: value })
        }
    }
})

const headerTextColor = computed({
    get: () => props.headerSettings?.textColor || '#ffffff',
    set: (value: string) => {
        if (props.headerSettings) {
            emit('headerSettingsChange', { ...props.headerSettings, textColor: value })
        }
    }
})

const headerHeight = computed({
    get: () => props.headerSettings?.height || '60px',
    set: (value: string) => {
        if (props.headerSettings) {
            emit('headerSettingsChange', { ...props.headerSettings, height: value })
        }
    }
})

const headerFontSize = computed({
    get: () => props.headerSettings?.fontSize || '18px',
    set: (value: string) => {
        if (props.headerSettings) {
            emit('headerSettingsChange', { ...props.headerSettings, fontSize: value })
        }
    }
})

const headerFontWeight = computed({
    get: () => props.headerSettings?.fontWeight || '600',
    set: (value: string) => {
        if (props.headerSettings) {
            emit('headerSettingsChange', { ...props.headerSettings, fontWeight: value })
        }
    }
})

// 计算属性处理Footer设置的双向绑定
const footerContactName = computed({
    get: () => props.footerSettings?.contactName || '',
    set: (value: string) => {
        if (props.footerSettings) {
            emit('footerSettingsChange', { ...props.footerSettings, contactName: value })
        }
    }
})

const footerContactPhone = computed({
    get: () => props.footerSettings?.contactPhone || '',
    set: (value: string) => {
        if (props.footerSettings) {
            emit('footerSettingsChange', { ...props.footerSettings, contactPhone: value })
        }
    }
})

const footerAddress = computed({
    get: () => props.footerSettings?.address || '',
    set: (value: string) => {
        if (props.footerSettings) {
            emit('footerSettingsChange', { ...props.footerSettings, address: value })
        }
    }
})

const footerCopyrightInfo = computed({
    get: () => props.footerSettings?.copyrightInfo || '',
    set: (value: string) => {
        if (props.footerSettings) {
            emit('footerSettingsChange', { ...props.footerSettings, copyrightInfo: value })
        }
    }
})

const footerLicenseInfo = computed({
    get: () => props.footerSettings?.licenseInfo || '',
    set: (value: string) => {
        if (props.footerSettings) {
            emit('footerSettingsChange', { ...props.footerSettings, licenseInfo: value })
        }
    }
})

const footerBackgroundColor = computed({
    get: () => props.footerSettings?.backgroundColor || '#990000',
    set: (value: string) => {
        if (props.footerSettings) {
            emit('footerSettingsChange', { ...props.footerSettings, backgroundColor: value })
        }
    }
})

const footerTextColor = computed({
    get: () => props.footerSettings?.textColor || '#FFFFFF',
    set: (value: string) => {
        if (props.footerSettings) {
            emit('footerSettingsChange', { ...props.footerSettings, textColor: value })
        }
    }
})
</script>

<template>
    <div class="properties-panel">
        <div class="panel-header">
            <h4>
                <Icon icon="material-symbols:tune" style="width: 18px; height: 18px;" />
                属性配置
            </h4>
        </div>

        <div class="panel-content">
            <div v-if="!selectedComponent && !isHeaderSelected && !isFooterSelected" class="page-settings">
                <div class="settings-header">
                    <Icon icon="material-symbols:settings" style="width: 18px; height: 18px;" />
                    <h5>页面设置</h5>
                </div>

                <div class="settings-section">
                    <label>页面标题</label>
                    <el-input v-model="pageTitle" placeholder="请输入页面标题" readonly="true" />
                </div>

                <div class="settings-section">
                    <label>背景颜色</label>
                    <el-color-picker v-model="pageBackgroundColor" show-alpha />
                </div>

                <!-- <div class="settings-section">
                    <label>主题颜色</label>
                    <el-color-picker v-model="pageBackgroundColor" show-alpha />
                </div> -->
            </div>

            <div v-else-if="isHeaderSelected" class="header-settings">
                <div class="settings-header">
                    <Icon icon="material-symbols:settings" style="width: 18px; height: 18px;" />
                    <h5>页眉设置</h5>
                </div>

                <div class="settings-section">
                    <label>标题</label>
                    <el-input v-model="headerTitle" placeholder="请输入页眉标题" />
                </div>

                <div class="settings-section">
                    <label>背景颜色</label>
                    <el-color-picker v-model="headerBackgroundColor" show-alpha />
                </div>

                <div class="settings-section">
                    <label>文字颜色</label>
                    <el-color-picker v-model="headerTextColor" show-alpha />
                </div>

                <div class="settings-section">
                    <label>高度</label>
                    <el-input v-model="headerHeight" placeholder="例如: 60px" />
                </div>

                <div class="settings-section">
                    <label>字体大小</label>
                    <el-input v-model="headerFontSize" placeholder="例如: 18px" />
                </div>

                <div class="settings-section">
                    <label>字体粗细</label>
                    <el-select v-model="headerFontWeight" placeholder="选择字体粗细">
                        <el-option label="正常 (400)" value="400" />
                        <el-option label="中等 (500)" value="500" />
                        <el-option label="粗体 (600)" value="600" />
                        <el-option label="加粗 (700)" value="700" />
                        <el-option label="特粗 (800)" value="800" />
                    </el-select>
                </div>
            </div>

            <div v-else-if="isFooterSelected" class="footer-settings">
                <div class="settings-header">
                    <Icon icon="material-symbols:settings" style="width: 18px; height: 18px;" />
                    <h5>底部版权设置</h5>
                </div>

                <div class="settings-section">
                    <label>联系人姓名</label>
                    <el-input v-model="footerContactName" placeholder="请输入联系人姓名" />
                </div>

                <div class="settings-section">
                    <label>联系电话</label>
                    <el-input v-model="footerContactPhone" placeholder="请输入联系电话" />
                </div>

                <div class="settings-section">
                    <label>联系地址</label>
                    <el-input v-model="footerAddress" placeholder="请输入联系地址" />
                </div>

                <div class="settings-section">
                    <label>版权信息</label>
                    <el-input v-model="footerCopyrightInfo" type="textarea" :rows="2" placeholder="请输入版权信息" />
                </div>

                <div class="settings-section">
                    <label>许可证信息</label>
                    <el-input v-model="footerLicenseInfo" type="textarea" :rows="2" placeholder="请输入许可证信息（可选）" />
                </div>

                <div class="settings-section">
                    <label>背景颜色</label>
                    <el-color-picker v-model="footerBackgroundColor" show-alpha />
                </div>

                <div class="settings-section">
                    <label>文字颜色</label>
                    <el-color-picker v-model="footerTextColor" show-alpha />
                </div>
            </div>

            <div v-else-if="!currentComponentConfig" class="no-config">
                <Icon icon="material-symbols:error" style="width: 48px; height: 48px; color: #f56c6c;" />
                <p>未找到组件配置</p>
            </div>

            <div v-else class="dynamic-properties">
                <div class="component-info">
                    <h5>{{ currentComponentConfig.name }}</h5>
                    <p>组件类型: {{ selectedComponent?.type }}</p>
                </div>

                <DynamicPropertyForm :groups="currentComponentConfig.groups" :model-value="componentProperties"
                    @update:model-value="handlePropertyChange" />
            </div>
        </div>
    </div>
</template>

<style scoped>
.properties-panel {
    width: 320px;
    background: white;
    border-left: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100vh;
}

.panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f2f5;
    background: #fafbfc;
}

.panel-header h4 {
    margin: 0;
    font-size: 14px;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.no-selection {
    text-align: center;
    color: #909399;
    padding: 40px 20px;
}

.no-selection p {
    margin: 16px 0 0 0;
    font-size: 14px;
}

.no-config {
    text-align: center;
    color: #909399;
    padding: 40px 20px;
}

.no-config p {
    margin: 16px 0 0 0;
    font-size: 14px;
}

.page-settings,
.header-settings,
.footer-settings {
    width: 100%;
}

.settings-header {
    margin-bottom: 20px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;
    border-left: 3px solid #67c23a;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-header h5 {
    margin: 0;
    font-size: 14px;
    color: #303133;
    font-weight: 600;
}

.settings-section {
    margin-bottom: 16px;
}

.settings-section label {
    display: block;
    font-size: 12px;
    color: #909399;
    margin-bottom: 6px;
}

.dynamic-properties {
    width: 100%;
}

.component-info {
    margin-bottom: 20px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;
    border-left: 3px solid #409eff;
}

.component-info h5 {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #303133;
    font-weight: 600;
}

.component-info p {
    margin: 0;
    font-size: 12px;
    color: #909399;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
    width: 6px;
}

.panel-content::-webkit-scrollbar-track {
    background: #f1f3f4;
}

.panel-content::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
    background: #909399;
}
</style>