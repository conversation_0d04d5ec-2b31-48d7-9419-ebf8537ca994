# 江西师范大学红色教育实践基地管理后台

这是江西师范大学红色教育实践基地数字地图项目的独立管理后台系统。

## 🚀 功能特性

- **基地管理**: 添加、编辑、删除实践基地信息
- **页面配置**: 低代码编辑器，可视化配置基地详情页面
- **用户认证**: 管理员登录验证
- **响应式设计**: 支持桌面端和移动端访问

## 📋 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4

## 🛠️ 开发环境

### 环境要求

- Node.js >= 20.19.0
- npm >= 7.0.0

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

默认运行在 `http://localhost:5174`

### 构建生产版本

```bash
npm run build
```

## 🔐 登录信息

- **用户名**: admin
- **密码**: admin123

## 📁 项目结构

```
src/
├── views/              # 页面组件
│   ├── AdminLogin.vue     # 管理员登录页
│   ├── AdminDashboard.vue # 管理后台主页
│   └── LowCodeEditor.vue  # 低代码编辑器
├── types/              # TypeScript 类型定义
├── data/               # 模拟数据
├── router/             # 路由配置
└── stores/             # Pinia 状态管理
```

## 🔗 与主项目的关系

这是一个独立的管理后台项目，与主项目（地图展示系统）分离部署：

- **主项目**: 用户端地图展示系统
- **管理后台**: 管理员端内容管理系统

通过主项目右上角的"系统管理"按钮可以跳转到此管理后台。

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```
