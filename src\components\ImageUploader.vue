<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage, ElUpload } from 'element-plus'
import { Plus, Delete, Loading, View } from '@element-plus/icons-vue'
import { uploadImage } from '@/api/user'

interface Props {
    modelValue?: string
    disabled?: boolean
    // 尺寸配置
    width?: string | number
    height?: string | number
    // 布局配置
    layout?: 'square' | 'rectangle' | 'custom'
    // 显示配置
    showPreview?: boolean
    showDelete?: boolean
    showView?: boolean
    // 样式配置
    borderStyle?: 'dashed' | 'solid' | 'none'
    borderRadius?: string
    // 上传配置
    maxSize?: number // MB
    accept?: string
    // 提示文本
    placeholder?: string
    uploadingText?: string
    // 预览配置
    previewWidth?: string
    previewHeight?: string
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    disabled: false,
    width: '200px',
    height: '200px',
    layout: 'square',
    showPreview: true,
    showDelete: true,
    showView: true,
    borderStyle: 'dashed',
    borderRadius: '6px',
    maxSize: 5,
    accept: 'image/*',
    placeholder: '点击上传图片',
    uploadingText: '上传中...',
    previewWidth: '600px',
    previewHeight: '400px'
})

const emit = defineEmits<{
    'update:modelValue': [value: string]
    'upload-success': [url: string]
    'upload-error': [error: Error]
    'remove': []
}>()

const imageUrl = ref(props.modelValue)
const uploading = ref(false)
const previewVisible = ref(false)

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
    imageUrl.value = newValue
})

// 计算样式
const containerStyle = computed(() => {
    const style: Record<string, string> = {
        width: typeof props.width === 'number' ? `${props.width}px` : props.width,
        height: typeof props.height === 'number' ? `${props.height}px` : props.height,
        borderRadius: props.borderRadius
    }

    if (props.borderStyle !== 'none') {
        style.border = `1px ${props.borderStyle} #d9d9d9`
    }

    return style
})

const handleUpload = async (file: File) => {
    if (!file) return false

    // 检查文件类型
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
    }

    // 检查文件大小
    const isLtMax = file.size / 1024 / 1024 < props.maxSize
    if (!isLtMax) {
        ElMessage.error(`图片大小不能超过 ${props.maxSize}MB!`)
        return false
    }

    uploading.value = true

    try {
        const response = await uploadImage({ file })

        if (response.code === '200') {
            const url = response.data
            imageUrl.value = url
            emit('update:modelValue', url)
            emit('upload-success', url)
            ElMessage.success('上传成功')
        } else {
            ElMessage.error(response.msg || '上传失败')
            emit('upload-error', new Error(response.msg))
        }
    } catch (error) {
        console.error('上传错误:', error)
        ElMessage.error('上传失败，请检查网络连接')
        emit('upload-error', error instanceof Error ? error : new Error(String(error)))
    } finally {
        uploading.value = false
    }

    return false // 阻止默认上传行为
}

const handleRemove = () => {
    imageUrl.value = ''
    emit('update:modelValue', '')
    emit('remove')
}

const handlePreview = () => {
    if (imageUrl.value) {
        previewVisible.value = true
    }
}

const beforeUpload = (file: File) => {
    return handleUpload(file)
}
</script>

<template>
    <div class="image-uploader">
        <el-upload class="upload-demo" :show-file-list="false" :before-upload="beforeUpload"
            :disabled="disabled || uploading" :accept="accept">
            <div v-if="imageUrl" class="image-preview" :style="containerStyle">
                <img :src="imageUrl" class="preview-image" />
                <div class="image-overlay">
                    <div class="image-actions">
                        <el-button v-if="showView" type="primary" size="small" circle @click.stop="handlePreview"
                            :disabled="disabled" title="预览图片">
                            <el-icon>
                                <View />
                            </el-icon>
                        </el-button>
                        <el-button v-if="showDelete" type="danger" size="small" circle @click.stop="handleRemove"
                            :disabled="disabled" title="删除图片">
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </div>
                </div>
            </div>
            <div v-else class="upload-placeholder" :style="containerStyle">
                <el-icon v-if="uploading" class="uploading-icon">
                    <Loading />
                </el-icon>
                <el-icon v-else class="upload-icon">
                    <Plus />
                </el-icon>
                <div class="upload-text">
                    {{ uploading ? uploadingText : placeholder }}
                </div>
            </div>
        </el-upload>

        <!-- 图片预览对话框 -->
        <el-dialog v-model="previewVisible" title="图片预览" :width="previewWidth" center>
            <div class="preview-container">
                <img :src="imageUrl" alt="预览图" :style="{ maxWidth: '100%', maxHeight: previewHeight }" />
            </div>
        </el-dialog>
    </div>
</template>

<style scoped>
.image-uploader {
    width: 100%;
}

.upload-demo {
    width: 100%;
}

.image-preview {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    transition: border-color 0.3s;
}

.image-preview:hover {
    border-color: #409eff;
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: filter 0.3s ease;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-preview:hover .image-overlay {
    opacity: 1;
}

.image-preview:hover .preview-image {
    filter: brightness(0.7);
}

.image-actions {
    display: flex;
    gap: 8px;
}

.upload-placeholder {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: border-color 0.3s;
    background: #fafafa;
}

.upload-placeholder:hover {
    border-color: #409eff;
}

.upload-icon,
.uploading-icon {
    font-size: 28px;
    color: #8c939d;
    margin-bottom: 8px;
}

.uploading-icon {
    animation: rotate 1s linear infinite;
}

.upload-text {
    color: #8c939d;
    font-size: 14px;
    text-align: center;
}

.preview-container {
    text-align: center;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {

    .upload-icon,
    .uploading-icon {
        font-size: 24px;
    }

    .upload-text {
        font-size: 12px;
    }

    .image-actions {
        gap: 6px;
    }
}
</style>