export interface PracticeBase {
  id: string
  name: string
  city: string
  category: string
  address: string
  description: string
  phone?: string
  openHours?: string
  imageUrl: string
  features?: string[]
  rating: number
  coordinates: {
    lat: number
    lng: number
  }
  websiteUrl?: string // 官方网站链接（可选）

  // 新增思政课实践基地相关字段
  virtualHallUrl?: string // 虚拟展馆链接
  contactPerson?: string // 联系人
  suggestedTimeSlots?: string[] // 建议时间段
  suggestedCapacity?: number // 建议课程容量
  practiceActivities?: PracticeActivity[] // 实践活动
  demonstrationCourses?: DemonstrationCourse[] // 示范课程
  demonstrationPlans?: DemonstrationPlan[] // 示范教案
  specialImages?: string[] // 特色实景图片
  baseIntroduction?: string // 基地简介（详细版）
}

export interface PracticeActivity {
  id: string
  title: string
  description: string
  duration: string
  capacity: number
  imageUrl?: string
}

export interface DemonstrationCourse {
  id: string
  title: string
  instructor: string
  duration: string
  description: string
  tags: string[]
  videoUrl?: string
  downloadUrl?: string
}

export interface DemonstrationPlan {
  id: string
  title: string
  subject: string
  grade: string
  author: string
  description: string
  downloadUrl?: string
}

export interface FilterOptions {
  city: string
  category: string
  search: string
}

// 页面配置相关类型 - 优化版本
export interface PageConfig {
  // 核心标识信息
  id: string // 页面唯一标识
  name: string // 页面名称
  baseId: string // 关联基地ID

  // 版本控制
  version: string // 配置版本号
  schemaVersion: string // JSON Schema版本

  // 页面内容
  components: ComponentConfig[] // 组件配置列表
  layout: LayoutConfig // 布局配置
  theme: ThemeConfig // 主题配置

  // 元数据
  metadata: PageMetadata // 页面元数据
}

// 组件配置 - 支持基础组件位置信息
export interface ComponentConfig {
  id: string // 组件唯一标识
  type: string // 组件类型
  order: number // 组件顺序（用于排序）
  properties: Record<string, any> // 组件属性

  // 基础组件位置信息（业务组件不使用这些字段）
  x?: number // X坐标（基础组件绝对定位）
  y?: number // Y坐标（基础组件绝对定位）
  width?: number // 宽度（像素）
  height?: number // 高度（像素）

  // 组件分类标识
  isBasicComponent?: boolean // 是否为基础组件
}

// 布局配置 - 统一管理页面布局
export interface LayoutConfig {
  // 页面基础设置
  width: string // 页面宽度
  height: string // 页面高度
  backgroundColor: string // 背景颜色

  // SEO信息
  seo: SEOSettings // SEO设置

  // 全局样式
  globalStyles: GlobalStyles // 全局样式
}

// SEO设置
export interface SEOSettings {
  title: string // 页面标题
  description: string // 页面描述
  keywords: string // 关键词
  viewport: string // 视口设置
}

// 全局样式
export interface GlobalStyles {
  // 字体设置
  fontFamily: string // 字体族
  fontSize: string // 基础字体大小
  lineHeight: string // 行高

  // 间距设置
  spacing: SpacingConfig // 间距配置

  // 颜色主题
  colors: ColorConfig // 颜色配置
}

// 间距配置
export interface SpacingConfig {
  xs: string // 超小间距
  sm: string // 小间距
  md: string // 中等间距
  lg: string // 大间距
  xl: string // 超大间距
}

// 颜色配置
export interface ColorConfig {
  primary: string // 主色调
  secondary: string // 次要色
  success: string // 成功色
  warning: string // 警告色
  error: string // 错误色
  text: string // 文本色
  background: string // 背景色
}

// 主题配置 - 统一管理Header/Footer等全局元素
export interface ThemeConfig {
  header: HeaderConfig // 页眉配置
  footer: FooterConfig // 页脚配置
}

// 页眉配置
export interface HeaderConfig {
  title: string // 页眉标题
  backgroundColor: string // 背景颜色
  textColor: string // 文字颜色
  height: string // 高度
  fontSize: string // 字体大小
  fontWeight: string // 字体粗细
}

// 页脚配置
export interface FooterConfig {
  contact: ContactInfo // 联系信息
  copyright: string // 版权信息
  license: string // 许可证信息
  backgroundColor: string // 背景颜色
  textColor: string // 文字颜色
}

// 联系信息
export interface ContactInfo {
  name: string // 联系人姓名
  phone: string // 联系电话
  address: string // 联系地址
}

// 页面元数据 - 精简版
export interface PageMetadata {
  // 时间戳
  createdAt: string // 创建时间 (ISO 8601)
  updatedAt: string // 更新时间 (ISO 8601)

  // 状态管理
  status: PageStatus // 页面状态
  version: string // 版本号

  // 分类标签
  category: string // 分类
  tags: string[] // 标签列表

  // 创建者信息
  createdBy?: string // 创建者ID
  updatedBy?: string // 更新者ID
}

// 页面状态枚举
export type PageStatus = 'draft' | 'published' | 'archived' | 'deleted'

// API请求/响应类型 - 优化版
export interface SavePageRequest {
  pageConfig: PageConfig // 页面配置
  baseId: string // 基地ID
  userId?: string // 用户ID
}

export interface SavePageResponse {
  success: boolean // 操作是否成功
  code: string // 响应代码
  message: string // 响应消息
  data?: {
    pageId: string // 页面ID
    savedAt: string // 保存时间
    version: string // 版本号
  }
  error?: string // 错误信息
}

export interface LoadPageRequest {
  pageId: string // 页面ID
  baseId?: string // 基地ID
  version?: string // 版本号（可选，用于版本控制）
}

export interface LoadPageResponse {
  success: boolean // 操作是否成功
  code: string // 响应代码
  message: string // 响应消息
  data?: PageConfig // 页面配置数据
  error?: string // 错误信息
}

export interface ListPagesRequest {
  baseId?: string // 基地ID
  userId?: string // 用户ID
  status?: PageStatus // 页面状态
  category?: string // 分类
  tags?: string[] // 标签
  page?: number // 页码
  pageSize?: number // 每页大小
  sortBy?: string // 排序字段
  sortOrder?: 'asc' | 'desc' // 排序方向
}

export interface ListPagesResponse {
  success: boolean // 操作是否成功
  code: string // 响应代码
  message: string // 响应消息
  data?: {
    pages: PageConfig[] // 页面列表
    total: number // 总数
    page: number // 当前页
    pageSize: number // 每页大小
    totalPages: number // 总页数
  }
  error?: string // 错误信息
}

// 数据库实体映射类型 - 用于Java后端
export interface PageConfigEntity {
  // 主键
  id: string // 页面ID

  // 基础信息
  name: string // 页面名称
  baseId: string // 基地ID
  version: string // 版本号
  schemaVersion: string // Schema版本

  // 配置数据（JSON格式）
  configData: string // 完整配置JSON字符串

  // 状态信息
  status: PageStatus // 页面状态
  category: string // 分类
  tags: string // 标签（JSON数组字符串）

  // 时间戳
  createdAt: string // 创建时间
  updatedAt: string // 更新时间

  // 创建者信息
  createdBy?: string // 创建者ID
  updatedBy?: string // 更新者ID

  // 索引字段（用于快速查询）
  searchKeywords: string // 搜索关键词（用于全文搜索）
}
