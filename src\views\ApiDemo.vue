<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElTabs, ElTabPane } from 'element-plus'
import { User, Lock, Message } from '@element-plus/icons-vue'
import { login, register, uploadImage, getUserInfo } from '@/api/user'
import { addBaseInfo, deleteBaseInfo, listAllBaseInfo, type BaseInfo } from '@/api/base'
import { addCategory, deleteCategory, listAllCategory, updateCategory, type Category } from '@/api/category'
import ImageUploader from '@/components/ImageUploader.vue'

// 登录表单
const loginForm = ref({
    phone: '',
    password: ''
})
const loginLoading = ref(false)

// 注册表单
const registerForm = ref({
    phone: '',
    password: '',
    confirmPassword: '',
    code: ''
})
const registerLoading = ref(false)

// 图片上传
const uploadedImageUrl = ref('')

// 基地管理测试
const baseList = ref<BaseInfo[]>([])
const baseLoading = ref(false)

// 分类管理测试
const categoryList = ref<Category[]>([])
const categoryLoading = ref(false)

// 用户信息测试
const userInfo = ref<any>(null)
const userInfoLoading = ref(false)

const handleLogin = async () => {
    if (!loginForm.value.phone || !loginForm.value.password) {
        ElMessage.error('请输入手机号和密码')
        return
    }

    loginLoading.value = true

    try {
        const response = await login({
            phone: loginForm.value.phone,
            password: loginForm.value.password
        })

        if (response.code === '200') {
            ElMessage.success('登录成功')
            localStorage.setItem('admin_token', response.data.token)
            console.log('登录成功，token:', response.data.token)
        } else {
            ElMessage.error(response.msg || '登录失败')
        }
    } catch (error) {
        console.error('登录错误:', error)
        ElMessage.error('登录失败，请检查网络连接')
    } finally {
        loginLoading.value = false
    }
}

const handleRegister = async () => {
    if (!registerForm.value.phone || !registerForm.value.password || !registerForm.value.code) {
        ElMessage.error('请填写完整信息')
        return
    }

    if (registerForm.value.password !== registerForm.value.confirmPassword) {
        ElMessage.error('两次输入的密码不一致')
        return
    }

    registerLoading.value = true

    try {
        const response = await register({
            phone: registerForm.value.phone,
            password: registerForm.value.password,
            code: registerForm.value.code
        })

        if (response.code === '200') {
            ElMessage.success('注册成功')
            console.log('注册成功:', response)
        } else {
            ElMessage.error(response.msg || '注册失败')
        }
    } catch (error) {
        console.error('注册错误:', error)
        ElMessage.error('注册失败，请检查网络连接')
    } finally {
        registerLoading.value = false
    }
}

const handleImageUpload = (url: string) => {
    uploadedImageUrl.value = url
    console.log('图片上传成功:', url)
}

const handleImageError = (error: Error) => {
    console.error('图片上传失败:', error)
}

// 基地管理相关函数
const fetchBaseList = async () => {
    baseLoading.value = true
    try {
        const response = await listAllBaseInfo()
        if (response.code === '200') {
            baseList.value = response.data
            console.log('基地列表:', response.data)
        } else {
            ElMessage.error(response.msg || '获取基地列表失败')
        }
    } catch (error) {
        console.error('获取基地列表错误:', error)
        ElMessage.error('获取基地列表失败，请检查网络连接')
    } finally {
        baseLoading.value = false
    }
}

const testAddBase = async () => {
    const testData: BaseInfo = {
        baseName: '测试基地',
        baseDesc: '这是一个测试基地',
        address: '江西省南昌市红谷滩新区',
        province: '江西省',
        provinceCode: '360000',
        city: '南昌市',
        cityCode: '360100',
        district: '红谷滩新区',
        districtCode: '360106',
        lat: 28.698,
        lng: 115.857,
        phone: '0791-12345678',
        openTime: '09:00-17:00',
        image: '',
        categoryId: 1,
        score: 4.5
    }

    try {
        const response = await addBaseInfo(testData)
        if (response.code === '200') {
            ElMessage.success('新增基地成功')
            fetchBaseList()
        } else {
            ElMessage.error(response.msg || '新增基地失败')
        }
    } catch (error) {
        console.error('新增基地错误:', error)
        ElMessage.error('新增基地失败，请检查网络连接')
    }
}

const testDeleteBase = async (id: number) => {
  try {
    const response = await deleteBaseInfo({ id })
    if (response.code === '200') {
      ElMessage.success('删除基地成功')
      fetchBaseList()
    } else {
      ElMessage.error(response.msg || '删除基地失败')
    }
  } catch (error) {
    console.error('删除基地错误:', error)
    ElMessage.error('删除基地失败，请检查网络连接')
  }
}

// 分类管理相关函数
const fetchCategoryList = async () => {
  categoryLoading.value = true
  try {
    const response = await listAllCategory()
    if (response.code === '200') {
      categoryList.value = response.data
      console.log('分类列表:', response.data)
    } else {
      ElMessage.error(response.msg || '获取分类列表失败')
    }
  } catch (error) {
    console.error('获取分类列表错误:', error)
    ElMessage.error('获取分类列表失败，请检查网络连接')
  } finally {
    categoryLoading.value = false
  }
}

const testAddCategory = async () => {
  const testData: Category = {
    title: '测试分类',
    color: '#409EFF'
  }

  try {
    const response = await addCategory(testData)
    if (response.code === '200') {
      ElMessage.success('新增分类成功')
      fetchCategoryList()
    } else {
      ElMessage.error(response.msg || '新增分类失败')
    }
  } catch (error) {
    console.error('新增分类错误:', error)
    ElMessage.error('新增分类失败，请检查网络连接')
  }
}

const testDeleteCategory = async (id: number) => {
  try {
    const response = await deleteCategory({ id })
    if (response.code === '200') {
      ElMessage.success('删除分类成功')
      fetchCategoryList()
    } else {
      ElMessage.error(response.msg || '删除分类失败')
    }
  } catch (error) {
    console.error('删除分类错误:', error)
    ElMessage.error('删除分类失败，请检查网络连接')
  }
}

const testUpdateCategory = async (category: Category) => {
  if (!category.id) {
    ElMessage.error('分类ID不存在')
    return
  }

  const updateData = {
    id: category.id,
    title: `${category.title} - 已更新`,
    color: category.color
  }

  try {
    const response = await updateCategory(updateData)
    if (response.code === '200') {
      ElMessage.success('更新分类成功')
      fetchCategoryList()
    } else {
      ElMessage.error(response.msg || '更新分类失败')
    }
  } catch (error) {
    console.error('更新分类错误:', error)
    ElMessage.error('更新分类失败，请检查网络连接')
  }
}

// 用户信息相关函数
const fetchUserInfo = async () => {
  userInfoLoading.value = true
  try {
    const response = await getUserInfo()
    if (response.code === '200') {
      userInfo.value = response.data
      console.log('用户信息:', response.data)
    } else {
      ElMessage.error(response.msg || '获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息错误:', error)
    ElMessage.error('获取用户信息失败，请检查网络连接')
  } finally {
    userInfoLoading.value = false
  }
}
</script>

<template>
    <div class="api-demo">
        <div class="demo-header">
            <h1>API 功能演示</h1>
            <p>江西红色教育实践基地管理系统 - API 接口测试</p>
        </div>

        <el-tabs type="border-card" class="demo-tabs">
            <el-tab-pane label="用户登录" name="login">
                <div class="tab-content">
                    <h3>登录接口测试</h3>
                    <p>接口地址: POST /login</p>

                    <el-form :model="loginForm" class="demo-form">
                        <el-form-item label="手机号">
                            <el-input v-model="loginForm.phone" placeholder="请输入手机号" :prefix-icon="User" />
                        </el-form-item>

                        <el-form-item label="密码">
                            <el-input v-model="loginForm.password" type="password" placeholder="请输入密码"
                                :prefix-icon="Lock" show-password />
                        </el-form-item>

                        <el-form-item>
                            <el-button type="primary" :loading="loginLoading" @click="handleLogin">
                                {{ loginLoading ? '登录中...' : '登录' }}
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>

            <el-tab-pane label="用户注册" name="register">
                <div class="tab-content">
                    <h3>注册接口测试</h3>
                    <p>接口地址: POST /register</p>

                    <el-form :model="registerForm" class="demo-form">
                        <el-form-item label="手机号">
                            <el-input v-model="registerForm.phone" placeholder="请输入手机号" :prefix-icon="User" />
                        </el-form-item>

                        <el-form-item label="密码">
                            <el-input v-model="registerForm.password" type="password" placeholder="请输入密码"
                                :prefix-icon="Lock" show-password />
                        </el-form-item>

                        <el-form-item label="确认密码">
                            <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请再次输入密码"
                                :prefix-icon="Lock" show-password />
                        </el-form-item>

                        <el-form-item label="验证码">
                            <el-input v-model="registerForm.code" placeholder="请输入验证码" :prefix-icon="Message" />
                        </el-form-item>

                        <el-form-item>
                            <el-button type="primary" :loading="registerLoading" @click="handleRegister">
                                {{ registerLoading ? '注册中...' : '注册' }}
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>

            <el-tab-pane label="图片上传" name="upload">
                <div class="tab-content">
                    <h3>图片上传接口测试</h3>
                    <p>接口地址: POST /image/upload</p>

                    <div class="upload-section">
                        <h4>上传图片</h4>
                        <ImageUploader v-model="uploadedImageUrl" @upload-success="handleImageUpload"
                            @upload-error="handleImageError" />

                        <div v-if="uploadedImageUrl" class="upload-result">
                            <h4>上传结果</h4>
                            <p>图片地址: {{ uploadedImageUrl }}</p>
                            <img :src="uploadedImageUrl" alt="上传的图片" class="result-image" />
                        </div>
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane label="基地管理" name="base">
                <div class="tab-content">
                    <h3>基地管理接口测试</h3>
                    <p>接口地址: GET /base/listAll, POST /base/addBaseInfo, GET /base/deleteBaseInfo</p>

                    <div class="base-section">
                        <div class="base-actions">
                            <el-button type="primary" @click="fetchBaseList" :loading="baseLoading">
                                {{ baseLoading ? '加载中...' : '获取基地列表' }}
                            </el-button>
                            <el-button type="success" @click="testAddBase">
                                测试新增基地
                            </el-button>
                        </div>

                        <div class="base-list">
                            <h4>基地列表 ({{ baseList.length }}条)</h4>
                            <div v-if="baseList.length === 0" class="empty">暂无基地信息</div>
                            <div v-else class="base-items">
                                <div v-for="base in baseList" :key="base.id" class="base-item">
                                    <div class="base-info">
                                        <h5>{{ base.baseName }}</h5>
                                        <p><strong>地址:</strong> {{ base.address }}</p>
                                        <p><strong>电话:</strong> {{ base.phone }}</p>
                                        <p><strong>评分:</strong> {{ base.score }}</p>
                                    </div>
                                    <div class="base-actions">
                                        <el-button size="small" type="danger" @click="testDeleteBase(base.id!)">
                                            删除
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                                     </div>
           </div>
         </div>
       </el-tab-pane>

       <el-tab-pane label="分类管理" name="category">
         <div class="tab-content">
           <h3>分类管理接口测试</h3>
           <p>接口地址: GET /category/listAll, POST /category/addCategory, POST /category/deleteCategory, POST /category/updateCategory</p>
           
           <div class="category-section">
             <div class="category-actions">
               <el-button type="primary" @click="fetchCategoryList" :loading="categoryLoading">
                 {{ categoryLoading ? '加载中...' : '获取分类列表' }}
               </el-button>
               <el-button type="success" @click="testAddCategory">
                 测试新增分类
               </el-button>
             </div>
             
             <div class="category-list">
               <h4>分类列表 ({{ categoryList.length }}条)</h4>
               <div v-if="categoryList.length === 0" class="empty">暂无分类信息</div>
               <div v-else class="category-items">
                 <div v-for="category in categoryList" :key="category.id" class="category-item">
                   <div class="category-info">
                     <div class="category-color" :style="{ backgroundColor: category.color }"></div>
                     <div class="category-details">
                       <h5>{{ category.title }}</h5>
                       <p><strong>ID:</strong> {{ category.id }}</p>
                       <p><strong>颜色:</strong> {{ category.color }}</p>
                     </div>
                   </div>
                   <div class="category-actions">
                     <el-button size="small" type="primary" @click="testUpdateCategory(category)">
                       更新
                     </el-button>
                     <el-button size="small" type="danger" @click="testDeleteCategory(category.id!)">
                       删除
                     </el-button>
                   </div>
                 </div>
               </div>
             </div>
           </div>
         </div>
       </el-tab-pane>

       <el-tab-pane label="用户信息" name="userinfo">
         <div class="tab-content">
           <h3>用户信息接口测试</h3>
           <p>接口地址: GET /user/info</p>
           
           <div class="userinfo-section">
             <div class="userinfo-actions">
               <el-button type="primary" @click="fetchUserInfo" :loading="userInfoLoading">
                 {{ userInfoLoading ? '加载中...' : '获取用户信息' }}
               </el-button>
             </div>
             
             <div v-if="userInfo" class="userinfo-result">
               <h4>用户信息</h4>
               <div class="userinfo-details">
                 <p><strong>用户ID:</strong> {{ userInfo.id }}</p>
                 <p><strong>手机号:</strong> {{ userInfo.phone }}</p>
                 <p><strong>用户名:</strong> {{ userInfo.userName }}</p>
               </div>
             </div>
           </div>
         </div>
       </el-tab-pane>
     </el-tabs>
   </div>
 </template>

<style scoped>
.api-demo {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.demo-header {
    text-align: center;
    margin-bottom: 30px;
}

.demo-header h1 {
    color: #303133;
    margin-bottom: 10px;
}

.demo-header p {
    color: #909399;
    font-size: 14px;
}

.demo-tabs {
    margin-bottom: 20px;
}

.tab-content {
    padding: 20px 0;
}

.tab-content h3 {
    color: #303133;
    margin-bottom: 10px;
}

.tab-content p {
    color: #606266;
    margin-bottom: 20px;
    font-size: 14px;
}

.demo-form {
    max-width: 400px;
}

.demo-form .el-form-item {
    margin-bottom: 20px;
}

.upload-section {
    max-width: 500px;
}

.upload-section h4 {
    color: #303133;
    margin-bottom: 15px;
}

.upload-result {
    margin-top: 30px;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 8px;
}

.upload-result h4 {
    color: #303133;
    margin-bottom: 10px;
}

.upload-result p {
    color: #606266;
    margin-bottom: 15px;
    word-break: break-all;
}

.result-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
}

.base-section {
    max-width: 600px;
}

.base-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.base-list h4 {
    color: #303133;
    margin-bottom: 15px;
}

.empty {
    text-align: center;
    padding: 40px;
    color: #909399;
}

.base-items {
    display: grid;
    gap: 15px;
}

.base-item {
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.base-info h5 {
    margin: 0 0 10px 0;
    color: #303133;
}

.base-info p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.category-section {
  max-width: 600px;
}

.category-actions {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.category-list h4 {
  color: #303133;
  margin-bottom: 15px;
}

.category-items {
  display: grid;
  gap: 15px;
}

.category-item {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.category-color {
  width: 30px;
  height: 30px;
  border-radius: 6px;
  flex-shrink: 0;
}

.category-details h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

.category-details p {
  margin: 3px 0;
  color: #606266;
  font-size: 14px;
}

.userinfo-section {
  max-width: 500px;
}

.userinfo-actions {
  margin-bottom: 20px;
}

.userinfo-result {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
}

.userinfo-result h4 {
  color: #303133;
  margin-bottom: 15px;
}

.userinfo-details p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}
</style>