<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '../../stores/user'
import { addBaseInfo, deleteBaseInfo, listBaseInfo, updateBaseInfo, type BaseInfo } from '../../api/base'

// 导入子组件
import BaseHeader from './components/BaseHeader.vue'
import BaseToolbar from './components/BaseToolbar.vue'
import BaseDataTable from './components/BaseDataTable.vue'
import BaseFormDialog from './components/BaseFormDialog.vue'

const router = useRouter()
const userStore = useUserStore()

// 数据状态
const tableData = ref<BaseInfo[]>([])
const total = ref(0)
const loading = ref(false)
const dialogVisible = ref(false)
const editingBase = ref<BaseInfo | null>(null)

// 分页状态持久化Key
const PAGINATION_STORAGE_KEY = 'baseAdmin_pagination_state'
const SEARCH_STORAGE_KEY = 'baseAdmin_search_state'

// 从localStorage/sessionStorage恢复状态
const restorePaginationState = () => {
    try {
        const savedPagination = sessionStorage.getItem(PAGINATION_STORAGE_KEY)
        const savedSearch = sessionStorage.getItem(SEARCH_STORAGE_KEY)

        return {
            pagination: savedPagination ? JSON.parse(savedPagination) : { pageNo: 1, pageSize: 10 },
            searchQuery: savedSearch || ''
        }
    } catch (error) {
        console.warn('恢复分页状态失败:', error)
        return {
            pagination: { pageNo: 1, pageSize: 10 },
            searchQuery: ''
        }
    }
}

// 保存状态到storage
const savePaginationState = () => {
    try {
        sessionStorage.setItem(PAGINATION_STORAGE_KEY, JSON.stringify(pagination.value))
        sessionStorage.setItem(SEARCH_STORAGE_KEY, searchQuery.value)
    } catch (error) {
        console.warn('保存分页状态失败:', error)
    }
}

// 清除保存的状态（可选功能）
const clearPaginationState = () => {
    try {
        sessionStorage.removeItem(PAGINATION_STORAGE_KEY)
        sessionStorage.removeItem(SEARCH_STORAGE_KEY)
        pagination.value = { pageNo: 1, pageSize: 10 }
        searchQuery.value = ''
        console.log('🧹 分页状态已清除，重置为初始状态')
        loadData()
    } catch (error) {
        console.warn('清除分页状态失败:', error)
    }
}

// 初始化状态
const initialState = restorePaginationState()

// 分页和搜索状态
const pagination = ref(initialState.pagination)
const searchQuery = ref(initialState.searchQuery)

// 权限检查已由路由守卫处理，这里不再需要
const checkAuth = () => {
    return true
}

// 加载数据
const loadData = async () => {
    loading.value = true
    try {
        const params = {
            pageNo: pagination.value.pageNo,
            pageSize: pagination.value.pageSize,
            title: searchQuery.value || undefined
        }

        const response = await listBaseInfo(params)
        if (response.code === '200') {
            tableData.value = response.data.data
            total.value = response.data.total
        } else {
            ElMessage.error(response.msg || '获取基地列表失败')
        }
    } catch (error) {
        console.error('获取基地列表错误:', error)
        ElMessage.error('获取基地列表失败，请检查网络连接')
    } finally {
        loading.value = false
    }
}

// 处理搜索
const handleSearch = () => {
    pagination.value.pageNo = 1 // 重置到第一页
    savePaginationState() // 保存状态
    loadData()
}

// 处理分页变化
const handlePageChange = (page: number) => {
    pagination.value.pageNo = page
    savePaginationState() // 保存状态
    loadData()
}

const handleSizeChange = (size: number) => {
    pagination.value.pageSize = size
    pagination.value.pageNo = 1 // 重置到第一页
    savePaginationState() // 保存状态
    loadData()
}

// 添加基地
const handleAdd = () => {
    editingBase.value = null
    dialogVisible.value = true
}

// 编辑基地
const handleEdit = (base: BaseInfo) => {
    editingBase.value = base
    dialogVisible.value = true
}

// 删除基地
const handleDelete = async (base: BaseInfo) => {
    if (!base.id) return

    try {
        await ElMessageBox.confirm(`确定要删除"${base.baseName}"吗？`, '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })

        const response = await deleteBaseInfo({ id: base.id })
        if (response.code === '200') {
            ElMessage.success('删除成功')
            loadData() // 重新加载数据
        } else {
            ElMessage.error(response.msg || '删除失败')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除基地错误:', error)
            ElMessage.error('删除失败，请检查网络连接')
        }
    }
}

// 页面配置
const handlePageConfig = (base: BaseInfo) => {
    // 跳转前保存当前分页状态
    savePaginationState()
    console.log(`🔄 跳转到低代码编辑器，保存分页状态: 第${pagination.value.pageNo}页，每页${pagination.value.pageSize}条`)
    router.push(`/lowcode-editor/${base.id}`)
}

// 保存基地数据
const handleSave = async (baseData: Partial<BaseInfo>) => {
    try {
        if (editingBase.value) {
            // 更新现有基地
            const updateData = {
                ...editingBase.value,
                ...baseData,
                id: editingBase.value.id!
            }

            const response = await updateBaseInfo(updateData)
            if (response.code === '200') {
                ElMessage.success('更新成功')
                loadData() // 重新加载数据
            } else {
                ElMessage.error(response.msg || '更新失败')
            }
        } else {
            // 添加新基地
            const addData: BaseInfo = {
                baseName: baseData.baseName || '',
                baseDesc: baseData.baseDesc || '',
                address: baseData.address || '',
                province: baseData.province || '江西省',
                provinceCode: baseData.provinceCode || '360000',
                city: baseData.city || '',
                cityCode: baseData.cityCode || '',
                codeInfoId: 1,
                district: baseData.district || '',
                districtCode: baseData.districtCode || '',
                lat: baseData.lat || 28.682,
                lng: baseData.lng || 115.858,
                phone: baseData.phone || '',
                openTime: baseData.openTime || '',
                image: baseData.image || '',
                categoryId: baseData.categoryId || 1,
                score: baseData.score || 5.0
            }

            const response = await addBaseInfo(addData)
            if (response.code === '200') {
                ElMessage.success('添加成功')
                loadData() // 重新加载数据
            } else {
                ElMessage.error(response.msg || '添加失败')
            }
        }
    } catch (error) {
        console.error('保存基地错误:', error)
        ElMessage.error('保存失败，请检查网络连接')
    }
}

// 监听搜索输入变化，实时保存状态
watch(searchQuery, () => {
    savePaginationState()
}, { immediate: false })

// 初始化
onMounted(async () => {
    // 路由守卫已经处理了权限检查和用户信息初始化

    // 输出恢复的状态信息
    console.log(`📄 基地管理页面初始化:`)
    console.log(`  📍 分页状态: 第${pagination.value.pageNo}页，每页${pagination.value.pageSize}条`)
    console.log(`  🔍 搜索条件: "${searchQuery.value}"`)
    console.log(`  💡 如需重置分页状态，请在控制台运行: window.clearBaseAdminPagination()`)

    // 暴露清除分页状态函数到全局（方便调试）
    interface WindowWithClearPagination extends Window {
        clearBaseAdminPagination?: () => void
    }
    ; (window as WindowWithClearPagination).clearBaseAdminPagination = clearPaginationState

    loadData()
})
</script>

<template>
    <div class="admin-container">
        <!-- 头部组件 -->
        <BaseHeader />

        <!-- 工具栏组件 -->
        <BaseToolbar v-model:searchQuery="searchQuery" @add="handleAdd" @search="handleSearch" />

        <!-- 数据表格组件 -->
        <BaseDataTable :data="tableData" :loading="loading" @edit="handleEdit" @delete="handleDelete"
            @page-config="handlePageConfig" />

        <!-- 分页组件 -->
        <div class="pagination-container">
            <el-pagination v-model:current-page="pagination.pageNo" v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
                prev-text="上一页" next-text="下一页" :pager-count="7" @size-change="handleSizeChange"
                @current-change="handlePageChange" />
        </div>

        <!-- 表单对话框组件 -->
        <BaseFormDialog v-model:visible="dialogVisible" :editing-base="editingBase" @save="handleSave" />
    </div>
</template>

<style scoped>
.admin-container {
    min-height: 100vh;
    background: #f5f7fa;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 20px 24px;
    background: white;
    border-top: 1px solid #ebeef5;
}

.pagination-container .el-pagination {
    justify-content: center;
}
</style>
