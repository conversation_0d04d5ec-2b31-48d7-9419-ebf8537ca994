<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '../../stores/user'
import { addBaseInfo, deleteBaseInfo, listAllBaseInfo, updateBaseInfo, type BaseInfo } from '../../api/base'

// 导入子组件
import BaseHeader from './components/BaseHeader.vue'
import BaseToolbar from './components/BaseToolbar.vue'
import BaseDataTable from './components/BaseDataTable.vue'
import BaseFormDialog from './components/BaseFormDialog.vue'

const router = useRouter()
const userStore = useUserStore()

// 数据状态
const tableData = ref<BaseInfo[]>([])
const searchQuery = ref('')
const loading = ref(false)
const dialogVisible = ref(false)
const editingBase = ref<BaseInfo | null>(null)

// 权限检查
const checkAuth = () => {
    const token = localStorage.getItem('admin_token')
    if (!token) {
        ElMessage.error('请先登录')
        router.push('/login')
        return false
    }
    return true
}

// 加载数据
const loadData = async () => {
    loading.value = true
    try {
        const response = await listAllBaseInfo()
        if (response.code === '200') {
            tableData.value = response.data
        } else {
            ElMessage.error(response.msg || '获取基地列表失败')
        }
    } catch (error) {
        console.error('获取基地列表错误:', error)
        ElMessage.error('获取基地列表失败，请检查网络连接')
    } finally {
        loading.value = false
    }
}

// 过滤数据
const filteredData = computed(() => {
    if (!searchQuery.value) return tableData.value
    return tableData.value.filter(base =>
        base.baseName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        base.city.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
})

// 添加基地
const handleAdd = () => {
    editingBase.value = null
    dialogVisible.value = true
}

// 编辑基地
const handleEdit = (base: BaseInfo) => {
    editingBase.value = base
    dialogVisible.value = true
}

// 删除基地
const handleDelete = async (base: BaseInfo) => {
    if (!base.id) return

    try {
        await ElMessageBox.confirm(`确定要删除"${base.baseName}"吗？`, '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })

        const response = await deleteBaseInfo({ id: base.id })
        if (response.code === '200') {
            ElMessage.success('删除成功')
            loadData() // 重新加载数据
        } else {
            ElMessage.error(response.msg || '删除失败')
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除基地错误:', error)
            ElMessage.error('删除失败，请检查网络连接')
        }
    }
}

// 页面配置
const handlePageConfig = (base: BaseInfo) => {
    ElMessage.info(`正在进入 "${base.baseName}" 的页面配置编辑器`)
    router.push(`/lowcode-editor/${base.id}`)
}

// 保存基地数据
const handleSave = async (baseData: Partial<BaseInfo>) => {
    try {
        if (editingBase.value) {
            // 更新现有基地
            const updateData = {
                ...editingBase.value,
                ...baseData,
                id: editingBase.value.id!
            }

            const response = await updateBaseInfo(updateData)
            if (response.code === '200') {
                ElMessage.success('更新成功')
                loadData() // 重新加载数据
            } else {
                ElMessage.error(response.msg || '更新失败')
            }
        } else {
            // 添加新基地
            const addData: BaseInfo = {
                baseName: baseData.baseName || '',
                baseDesc: baseData.baseDesc || '',
                address: baseData.address || '',
                province: baseData.province || '江西省',
                provinceCode: baseData.provinceCode || '360000',
                city: baseData.city || '',
                cityCode: baseData.cityCode || '',
                district: baseData.district || '',
                districtCode: baseData.districtCode || '',
                lat: baseData.lat || 28.682,
                lng: baseData.lng || 115.858,
                phone: baseData.phone || '',
                openTime: baseData.openTime || '',
                image: baseData.image || '',
                categoryId: baseData.categoryId || 1,
                score: baseData.score || 5.0
            }

            const response = await addBaseInfo(addData)
            if (response.code === '200') {
                ElMessage.success('添加成功')
                loadData() // 重新加载数据
            } else {
                ElMessage.error(response.msg || '添加失败')
            }
        }
    } catch (error) {
        console.error('保存基地错误:', error)
        ElMessage.error('保存失败，请检查网络连接')
    }
}

// 初始化
onMounted(async () => {
    if (checkAuth()) {
        await userStore.initUserInfo()
        loadData()
    }
})
</script>

<template>
    <div class="admin-container">
        <!-- 头部组件 -->
        <BaseHeader />

        <!-- 工具栏组件 -->
        <BaseToolbar v-model:searchQuery="searchQuery" @add="handleAdd" />

        <!-- 数据表格组件 -->
        <BaseDataTable :data="filteredData" :loading="loading" @edit="handleEdit" @delete="handleDelete"
            @page-config="handlePageConfig" />

        <!-- 表单对话框组件 -->
        <BaseFormDialog v-model:visible="dialogVisible" :editing-base="editingBase" @save="handleSave" />
    </div>
</template>

<style scoped>
.admin-container {
    min-height: 100vh;
    background: #f5f7fa;
}
</style>
