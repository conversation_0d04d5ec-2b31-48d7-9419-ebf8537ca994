# API 接口使用说明

## 基础配置

### 接口地址

- 基础URL: `http://120.55.15.172:8200`
- 超时时间: 10秒
- 请求头: `Content-Type: application/json`

### 认证方式

- 使用 `Authorization` 请求头传递用户token
- Token 格式: `Bearer {token}` 或直接传递token字符串

## 接口列表

### 1. 用户登录

**接口地址:** `POST /login`

**请求参数:**

```json
{
  "phone": "手机号",
  "password": "密码"
}
```

**响应格式:**

```json
{
  "code": "200",
  "data": {
    "token": "用户token"
  },
  "msg": "登录成功"
}
```

**使用示例:**

```javascript
import { login } from '@/api/user'

const response = await login({
  phone: '13800138000',
  password: '123456',
})

if (response.code === '200') {
  localStorage.setItem('admin_token', response.data.token)
  console.log('登录成功')
}
```

### 2. 用户注册

**接口地址:** `POST /register`

**请求参数:**

```json
{
  "phone": "手机号",
  "password": "密码",
  "code": "验证码"
}
```

**响应格式:**

```json
{
  "code": "200",
  "data": {},
  "msg": "注册成功"
}
```

**使用示例:**

```javascript
import { register } from '@/api/user'

const response = await register({
  phone: '13800138000',
  password: '123456',
  code: '123456',
})

if (response.code === '200') {
  console.log('注册成功')
}
```

### 3. 图片上传

**接口地址:** `POST /image/upload`

**请求参数:**

- 使用 `FormData` 格式
- 文件字段名: `file`

**响应格式:**

```json
{
  "code": "200",
  "data": {
    "url": "图片访问地址",
    "filename": "文件名"
  },
  "msg": "上传成功"
}
```

**使用示例:**

```javascript
import { uploadImage } from '@/api/user'

const file = document.getElementById('fileInput').files[0]
const response = await uploadImage({ file })

if (response.code === '200') {
  console.log('图片地址:', response.data.url)
}
```

### 4. 基地信息管理

#### 4.1 新增基地信息

**接口地址:** `POST /base/addBaseInfo`

**请求参数:**

```json
{
  "baseName": "基地名称",
  "baseDesc": "基地描述",
  "address": "详细地址",
  "province": "省份",
  "provinceCode": "省份代码",
  "city": "城市",
  "cityCode": "城市代码",
  "district": "区县",
  "districtCode": "区县代码",
  "lat": 28.698,
  "lng": 115.857,
  "phone": "联系电话",
  "openTime": "开放时间",
  "image": "图片地址",
  "categoryId": 1,
  "score": 4.5
}
```

**响应格式:**

```json
{
  "code": "200",
  "msg": "新增成功"
}
```

#### 4.2 删除基地信息

**接口地址:** `GET /base/deleteBaseInfo`

**请求参数:**

- `id`: 基地ID (query参数)

**响应格式:**

```json
{
  "code": "200",
  "msg": "删除成功"
}
```

#### 4.3 获取所有基地详情

**接口地址:** `GET /base/listAll`

**响应格式:**

```json
{
  "code": "200",
  "data": [
    {
      "id": 1,
      "baseName": "基地名称",
      "baseDesc": "基地描述",
      "address": "详细地址",
      "province": "省份",
      "provinceCode": "省份代码",
      "city": "城市",
      "cityCode": "城市代码",
      "district": "区县",
      "districtCode": "区县代码",
      "lat": 28.698,
      "lng": 115.857,
      "phone": "联系电话",
      "openTime": "开放时间",
      "image": "图片地址",
      "categoryId": 1,
      "categoryName": "分类名称",
      "score": 4.5
    }
  ],
  "msg": "获取成功"
}
```

#### 4.4 更新基地信息

**接口地址:** `POST /base/updateBaseInfo`

**请求参数:**

```json
{
  "id": 1,
  "baseName": "基地名称",
  "baseDesc": "基地描述",
  "address": "详细地址",
  "province": "省份",
  "provinceCode": "省份代码",
  "city": "城市",
  "cityCode": "城市代码",
  "district": "区县",
  "districtCode": "区县代码",
  "lat": 28.698,
  "lng": 115.857,
  "phone": "联系电话",
  "openTime": "开放时间",
  "image": "图片地址",
  "categoryId": 1,
  "score": 4.5
}
```

**响应格式:**

```json
{
  "code": "200",
  "data": {
    "id": 1,
    "baseName": "基地名称",
    "baseDesc": "基地描述",
    "address": "详细地址",
    "province": "省份",
    "provinceCode": "省份代码",
    "city": "城市",
    "cityCode": "城市代码",
    "district": "区县",
    "districtCode": "区县代码",
    "lat": 28.698,
    "lng": 115.857,
    "phone": "联系电话",
    "openTime": "开放时间",
    "image": "图片地址",
    "categoryId": 1,
    "categoryName": "分类名称",
    "score": 4.5
  },
  "msg": "更新成功"
}
```

**使用示例:**

```javascript
import { addBaseInfo, deleteBaseInfo, listAllBaseInfo, updateBaseInfo } from '@/api/base'

// 新增基地
const response = await addBaseInfo({
  baseName: '测试基地',
  baseDesc: '这是一个测试基地',
  address: '江西省南昌市红谷滩新区',
  // ... 其他字段
})

// 获取基地列表
const listResponse = await listAllBaseInfo()

// 删除基地
const deleteResponse = await deleteBaseInfo({ id: 1 })

// 更新基地
const updateResponse = await updateBaseInfo({
  id: 1,
  baseName: '更新后的基地名称',
  // ... 其他字段
})
```

### 5. 分类信息管理

#### 5.1 新增分类

**接口地址:** `POST /category/addCategory`

**请求参数:**

```json
{
  "title": "分类名称",
  "color": "#409EFF"
}
```

**响应格式:**

```json
{
  "code": "200",
  "msg": "新增成功"
}
```

#### 5.2 删除分类

**接口地址:** `POST /category/deleteCategory`

**请求参数:**

```json
{
  "id": 1
}
```

**响应格式:**

```json
{
  "code": "200",
  "msg": "删除成功"
}
```

#### 5.3 获取所有分类

**接口地址:** `GET /category/listAll`

**响应格式:**

```json
{
  "code": "200",
  "data": [
    {
      "id": 1,
      "title": "分类名称",
      "color": "#409EFF"
    }
  ],
  "msg": "获取成功"
}
```

#### 5.4 更新分类

**接口地址:** `POST /category/updateCategory`

**请求参数:**

```json
{
  "id": 1,
  "title": "更新后的分类名称",
  "color": "#67C23A"
}
```

**响应格式:**

```json
{
  "code": "200",
  "msg": "更新成功"
}
```

### 6. 用户信息

#### 6.1 获取用户信息

**接口地址:** `GET /user/info`

**响应格式:**

```json
{
  "code": "200",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "userName": "用户名"
  },
  "msg": "获取成功"
}
```

**使用示例:**

```javascript
import { getUserInfo } from '@/api/user'

const response = await getUserInfo()
if (response.code === '200') {
  console.log('用户信息:', response.data)
}
```

## 错误处理

### 常见错误码

- `401`: 未授权，需要重新登录
- `403`: 拒绝访问
- `404`: 请求的资源不存在
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "code": "400",
  "msg": "错误信息"
}
```

## 项目结构

```
src/
├── api/
│   ├── config.ts      # axios配置和拦截器
│   ├── user.ts        # 用户相关API
│   ├── base.ts        # 基地信息相关API
│   ├── category.ts    # 分类管理相关API
│   └── index.ts       # API统一导出
├── components/
│   └── ImageUploader.vue  # 图片上传组件
└── views/
    ├── AdminLogin.vue     # 登录页面
    ├── AdminRegister.vue  # 注册页面
    ├── ApiDemo.vue        # API演示页面
    ├── BaseManagement.vue # 基地管理页面
    └── CategoryManagement.vue # 分类管理页面
```

## 使用说明

### 1. 登录功能

- 访问 `/login` 页面进行登录
- 登录成功后会自动保存token到localStorage
- 后续请求会自动携带token

### 2. 注册功能

- 访问 `/register` 页面进行注册
- 需要提供手机号、密码和验证码
- 注册成功后跳转到登录页面

### 3. 图片上传

- 使用 `ImageUploader` 组件
- 支持拖拽上传和点击上传
- 自动处理文件类型和大小验证
- 上传成功后返回图片URL

### 4. API测试

- 访问 `/api-demo` 页面
- 可以测试所有API接口
- 包含详细的请求和响应信息

### 5. 基地管理

- 访问 `/base-management` 页面
- 专门用于测试基地信息管理功能
- 支持新增、删除、查看基地信息

### 6. 分类管理

- 访问 `/category-management` 页面
- 专门用于测试分类信息管理功能
- 支持新增、删除、更新、查看分类信息

### 7. 用户信息

- 在API演示页面测试用户信息获取功能
- 需要先登录获取token

## 注意事项

1. **Token管理**: 登录成功后需要保存token，后续请求会自动携带
2. **错误处理**: 所有API调用都应该包含错误处理逻辑
3. **文件上传**: 图片上传使用FormData格式，注意文件大小限制
4. **网络请求**: 确保网络连接正常，接口地址可访问
5. **CORS**: 如果遇到跨域问题，需要后端配置CORS

## 开发建议

1. **类型安全**: 使用TypeScript接口定义请求和响应类型
2. **错误提示**: 使用Element Plus的Message组件显示错误信息
3. **加载状态**: 在请求过程中显示加载状态
4. **表单验证**: 在发送请求前进行表单验证
5. **用户体验**: 提供友好的用户反馈和错误提示
