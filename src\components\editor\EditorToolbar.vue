<script setup lang="ts">
import {
  ArrowLeft,
  View,
  DocumentCopy,
  Monitor,
  Iphone,
  Download,
  Upload,
} from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ElMessage } from 'element-plus'

// 定义组件属性
interface Props {
  currentBase?: {
    baseName: string
  } | null
  deviceType: 'desktop' | 'tablet' | 'mobile'
  zoom: number
  previewMode: boolean
}

// 定义组件事件
interface Emits {
  (e: 'goBack'): void
  (e: 'deviceChange', device: 'desktop' | 'tablet' | 'mobile'): void
  (e: 'zoomChange', zoom: number): void
  (e: 'previewToggle'): void
  (e: 'save'): void
  (e: 'export'): void
  (e: 'import'): void
  (e: 'togglePageSettings'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 工具栏操作
const handleSave = () => {
  emit('save')
}

const handlePreview = () => {
  emit('previewToggle')
  ElMessage.info(props.previewMode ? '已切换到编辑模式' : '已切换到预览模式')
}

const handleExport = () => {
  emit('export')
  ElMessage.success('页面配置已导出')
}

const handleImport = () => {
  emit('import')
  ElMessage.success('页面配置已导入')
}

const handleDeviceChange = (device: 'desktop' | 'tablet' | 'mobile') => {
  emit('deviceChange', device)
}

const handleZoomChange = (zoom: number) => {
  emit('zoomChange', zoom)
}

const handleGoBack = () => {
  emit('goBack')
}

const handleTogglePageSettings = () => {
  emit('togglePageSettings')
}
</script>

<template>
  <div class="editor-toolbar">
    <div class="toolbar-left">
      <el-button @click="handleGoBack" :icon="ArrowLeft" text> 返回管理 </el-button>
      <div class="page-info">
        <h3>
          低代码页面编辑器
          <span v-if="currentBase" class="base-name">- {{ currentBase.baseName }}</span>
        </h3>
        <span class="page-subtitle">
          {{ currentBase ? `为 "${currentBase.baseName}" 配置详情页面` : '可视化页面配置工具' }}
        </span>
      </div>
    </div>

    <div class="toolbar-center">
      <!-- 设备切换 -->
      <div class="device-switcher">
        <el-tooltip content="桌面端预览" placement="bottom">
          <el-button
            :type="deviceType === 'desktop' ? 'primary' : ''"
            :icon="Monitor"
            @click="handleDeviceChange('desktop')"
            size="small"
          />
        </el-tooltip>
        <el-tooltip content="平板预览" placement="bottom">
          <el-button
            :type="deviceType === 'tablet' ? 'primary' : ''"
            @click="handleDeviceChange('tablet')"
            size="small"
          >
            <Icon icon="material-symbols:tablet" style="width: 16px; height: 16px" />
          </el-button>
        </el-tooltip>
        <el-tooltip content="移动端预览" placement="bottom">
          <el-button
            :type="deviceType === 'mobile' ? 'primary' : ''"
            :icon="Iphone"
            @click="handleDeviceChange('mobile')"
            size="small"
          />
        </el-tooltip>
      </div>

      <!-- 缩放控制 -->
      <div class="zoom-control">
        <el-button @click="handleZoomChange(zoom - 10)" size="small" text>
          <Icon icon="material-symbols:zoom-out" style="width: 16px; height: 16px" />
        </el-button>
        <span class="zoom-value">{{ zoom }}%</span>
        <el-button @click="handleZoomChange(zoom + 10)" size="small" text>
          <Icon icon="material-symbols:zoom-in" style="width: 16px; height: 16px" />
        </el-button>
      </div>
    </div>

    <div class="toolbar-right">
      <el-button @click="handleTogglePageSettings" size="small" text>
        <Icon icon="material-symbols:settings" style="width: 16px; height: 16px" />
        页面设置
      </el-button>
      <!-- <el-button @click="handleImport" :icon="Upload" size="small" text>
                导入
            </el-button>
            <el-button @click="handleExport" :icon="Download" size="small" text>
                导出
            </el-button> -->
      <el-button @click="handlePreview" :icon="View" size="small" type="primary" plain>
        {{ previewMode ? '编辑' : '预览' }}
      </el-button>
      <el-button @click="handleSave" :icon="DocumentCopy" size="small" type="primary">
        保存
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.editor-toolbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-info h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.page-subtitle {
  font-size: 12px;
  color: #909399;
}

.base-name {
  color: #409eff;
  font-weight: 500;
  font-size: 14px;
}

.toolbar-center {
  display: flex;
  align-items: center;
  gap: 24px;
}

.device-switcher {
  display: flex;
  gap: 4px;
  padding: 4px;
  background: #f5f7fa;
  border-radius: 6px;
}

.zoom-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background: #f5f7fa;
  border-radius: 6px;
}

.zoom-value {
  font-size: 12px;
  color: #606266;
  min-width: 40px;
  text-align: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
