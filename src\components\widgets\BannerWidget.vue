<script setup lang="ts">
// 定义组件属性
interface Props {
    backgroundImage?: string
    mainTitle?: string
    subTitle?: string
    height?: number
    overlayColor?: string
    textColor?: string
    textAlign?: string
    mainTitleFontSize?: number
    subTitleFontSize?: number
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
    backgroundImage: '',
    mainTitle: 'Banner主标题',
    subTitle: 'Banner描述内容',
    height: 400,
    overlayColor: 'rgba(0, 0, 0, 0.4)',
    textColor: '#FFFFFF',
    textAlign: 'center',
    mainTitleFontSize: 48,
    subTitleFontSize: 18,
})
</script>

<template>
    <div class="banner-widget-container" :style="{
        backgroundImage: `url(${props.backgroundImage})`,
        height: `${props.height}px`
    }">
        <div class="banner-overlay" :style="{ backgroundColor: props.overlayColor }">
            <div class="banner-content" :style="{ textAlign: props.textAlign as any }">
                <h1 v-if="props.mainTitle" class="main-title" :style="{
                    color: props.textColor,
                    fontSize: `${props.mainTitleFontSize}px`
                }">
                    {{ props.mainTitle }}
                </h1>
                <p v-if="props.subTitle" class="sub-title" :style="{
                    color: props.textColor,
                    fontSize: `${props.subTitleFontSize}px`
                }">
                    {{ props.subTitle }}
                </p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.banner-widget-container {
    width: 100%;
    background-size: cover;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.banner-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.banner-content {
    width: 100%;
    box-sizing: border-box;
}

.main-title {
    font-weight: bold;
    margin: 0;
    line-height: 1.2;
}

.sub-title {
    margin: 10px 0 0;
    opacity: 0.9;
}
</style>