import type {
  PageConfig,
  SavePageRequest,
  SavePageResponse,
  LoadPageRequest,
  LoadPageResponse,
  ListPagesRequest,
  ListPagesResponse,
} from '../types'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'

// 通用请求函数
async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  }

  try {
    const response = await fetch(url, defaultOptions)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('API request failed:', error)
    throw error
  }
}

// 页面配置API服务
export const pageService = {
  // 保存页面配置
  async savePage(request: SavePageRequest): Promise<SavePageResponse> {
    return apiRequest<SavePageResponse>('/pages/save', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  },

  // 加载页面配置
  async loadPage(request: LoadPageRequest): Promise<LoadPageResponse> {
    const params = new URLSearchParams({
      pageId: request.pageId,
      ...(request.baseId && { baseId: request.baseId }),
    })

    return apiRequest<LoadPageResponse>(`/pages/load?${params}`, {
      method: 'GET',
    })
  },

  // 获取页面列表
  async listPages(request: ListPagesRequest): Promise<ListPagesResponse> {
    const params = new URLSearchParams()

    if (request.baseId) params.append('baseId', request.baseId)
    if (request.userId) params.append('userId', request.userId)
    if (request.status) params.append('status', request.status)
    if (request.page) params.append('page', request.page.toString())
    if (request.pageSize) params.append('pageSize', request.pageSize.toString())

    return apiRequest<ListPagesResponse>(`/pages/list?${params}`, {
      method: 'GET',
    })
  },

  // 删除页面配置
  async deletePage(pageId: string): Promise<{ success: boolean; message: string }> {
    return apiRequest<{ success: boolean; message: string }>(`/pages/delete/${pageId}`, {
      method: 'DELETE',
    })
  },

  // 发布页面
  async publishPage(pageId: string): Promise<{ success: boolean; message: string }> {
    return apiRequest<{ success: boolean; message: string }>(`/pages/publish/${pageId}`, {
      method: 'POST',
    })
  },

  // 导出页面配置为JSON文件
  async exportPage(pageId: string): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/pages/export/${pageId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.blob()
  },

  // 导入页面配置
  async importPage(file: File, baseId: string): Promise<SavePageResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('baseId', baseId)

    const response = await fetch(`${API_BASE_URL}/pages/import`, {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  },
}

// 本地存储服务（作为备用方案）
export const localPageService = {
  // 保存到本地存储
  saveToLocal(pageConfig: PageConfig): void {
    const key = `page_config_${pageConfig.id}`
    localStorage.setItem(key, JSON.stringify(pageConfig))
    console.log('页面配置已保存到本地存储:', pageConfig.id)
  },

  // 从本地存储加载
  loadFromLocal(pageId: string): PageConfig | null {
    const key = `page_config_${pageId}`
    const data = localStorage.getItem(key)

    if (data) {
      try {
        return JSON.parse(data) as PageConfig
      } catch (error) {
        console.error('解析本地存储数据失败:', error)
        return null
      }
    }

    return null
  },

  // 获取所有本地页面
  getAllLocalPages(): PageConfig[] {
    const pages: PageConfig[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('page_config_')) {
        try {
          const data = localStorage.getItem(key)
          if (data) {
            pages.push(JSON.parse(data) as PageConfig)
          }
        } catch (error) {
          console.error('解析本地存储数据失败:', error)
        }
      }
    }

    return pages
  },

  // 删除本地页面
  deleteFromLocal(pageId: string): void {
    const key = `page_config_${pageId}`
    localStorage.removeItem(key)
    console.log('页面配置已从本地存储删除:', pageId)
  },

  // 导出为JSON文件
  exportToFile(pageConfig: PageConfig): void {
    const dataStr = JSON.stringify(pageConfig, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${pageConfig.name}_配置.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    console.log('页面配置已导出为文件:', pageConfig.name)
  },
}
