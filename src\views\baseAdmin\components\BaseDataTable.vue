<script setup lang="ts">
import { Edit, Delete, Setting } from '@element-plus/icons-vue'
import type { BaseInfo } from '../../../api/base'

interface Props {
  data: BaseInfo[]
  loading: boolean
}

interface Emits {
  (e: 'edit', base: BaseInfo): void
  (e: 'delete', base: BaseInfo): void
  (e: 'pageConfig', base: BaseInfo): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const handleEdit = (base: BaseInfo) => {
  emit('edit', base)
}

const handleDelete = (base: BaseInfo) => {
  emit('delete', base)
}

const handlePageConfig = (base: BaseInfo) => {
  emit('pageConfig', base)
}
</script>

<template>
  <div class="table-container">
    <el-table :data="data" :loading="loading" stripe style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: '600' }">
      <el-table-column type="index" label="序号" width="80" align="center" />
      <el-table-column prop="baseName" label="基地名称" width="auto" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="base-name">{{ row.baseName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="city" label="城市" width="120" align="center" />
      <el-table-column prop="categoryName" label="分类" width="auto" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag v-if="row.categoryName" size="small" type="error">{{ row.categoryName }}</el-tag>
          <span v-else class="no-category">未分类</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="地址" width="auto" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="address-text">{{ row.address }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="phone" label="联系电话" width="auto" align="center">
        <template #default="{ row }">
          <span class="phone-text">{{ row.phone || '暂无' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="score" label="评分" width="120" align="center">
        <template #default="{ row }">
          <div class="rating-container">
            <el-rate v-model="row.score" disabled text-color="#ff9900" score-template="{value}" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right" align="center">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button @click="handleEdit(row)" type="primary" size="small" circle title="编辑">
              <el-icon>
                <Edit />
              </el-icon>
            </el-button>
            <el-button @click="handlePageConfig(row)" type="success" size="small" circle title="页面配置">
              <el-icon>
                <Setting />
              </el-icon>
            </el-button>
            <el-button @click="handleDelete(row)" type="danger" size="small" circle title="删除">
              <el-icon>
                <Delete />
              </el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped>
.table-container {
  padding: 24px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background: #f5f7fa !important;
  color: #606266 !important;
  font-weight: 600 !important;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafafa;
}

:deep(.el-table__body tr:hover > td) {
  background: #f5f7fa !important;
}

/* 基地名称样式 */
.base-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

/* 地址文本样式 */
.address-text {
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}

/* 电话文本样式 */
.phone-text {
  color: #409eff;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

/* 评分容器样式 */
.rating-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-text {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

/* 分类标签样式 */
:deep(.el-tag) {
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

.no-category {
  color: #c0c4cc;
  font-size: 12px;
  font-style: italic;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

:deep(.action-buttons .el-button) {
  transition: all 0.3s ease;
}

:deep(.action-buttons .el-button:hover) {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-container {
    padding: 12px;
  }

  .action-buttons {
    gap: 4px;
  }

  :deep(.el-table .cell) {
    padding: 8px 4px;
  }
}

/* 表格加载状态优化 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.9);
}

/* 空数据状态优化 */
:deep(.el-table__empty-block) {
  min-height: 200px;
}

:deep(.el-table__empty-text) {
  color: #909399;
  font-size: 14px;
}
</style>
