<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { addCategory, deleteCategory, listAllCategory, updateCategory, type Category } from '@/api/category'

// 数据状态
const categoryList = ref<Category[]>([])
const loading = ref(false)

// 获取分类列表
const fetchCategoryList = async () => {
    loading.value = true
    try {
        const response = await listAllCategory()
        if (response.code === '200') {
            categoryList.value = response.data
            console.log('分类列表:', response.data)
        } else {
            ElMessage.error(response.msg || '获取分类列表失败')
        }
    } catch (error) {
        console.error('获取分类列表错误:', error)
        ElMessage.error('获取分类列表失败，请检查网络连接')
    } finally {
        loading.value = false
    }
}

// 测试新增分类
const testAddCategory = async () => {
    const testData: Category = {
        title: '测试分类',
        color: '#409EFF'
    }

    try {
        const response = await addCategory(testData)
        if (response.code === '200') {
            ElMessage.success('新增分类成功')
            fetchCategoryList()
        } else {
            ElMessage.error(response.msg || '新增分类失败')
        }
    } catch (error) {
        console.error('新增分类错误:', error)
        ElMessage.error('新增分类失败，请检查网络连接')
    }
}

// 测试删除分类
const testDeleteCategory = async (id: number) => {
    try {
        const response = await deleteCategory({ id })
        if (response.code === '200') {
            ElMessage.success('删除分类成功')
            fetchCategoryList()
        } else {
            ElMessage.error(response.msg || '删除分类失败')
        }
    } catch (error) {
        console.error('删除分类错误:', error)
        ElMessage.error('删除分类失败，请检查网络连接')
    }
}

// 测试更新分类
const testUpdateCategory = async (category: Category) => {
    if (!category.id) {
        ElMessage.error('分类ID不存在')
        return
    }

    const updateData = {
        id: category.id,
        title: `${category.title} - 已更新`,
        color: category.color
    }

    try {
        const response = await updateCategory(updateData)
        if (response.code === '200') {
            ElMessage.success('更新分类成功')
            fetchCategoryList()
        } else {
            ElMessage.error(response.msg || '更新分类失败')
        }
    } catch (error) {
        console.error('更新分类错误:', error)
        ElMessage.error('更新分类失败，请检查网络连接')
    }
}

// 初始化
onMounted(() => {
    fetchCategoryList()
})
</script>

<template>
    <div class="category-management">
        <div class="page-header">
            <h2>分类信息管理 - API测试</h2>
            <el-button type="primary" @click="testAddCategory">测试新增分类</el-button>
        </div>

        <div class="api-info">
            <h3>API接口信息</h3>
            <ul>
                <li><strong>新增分类:</strong> POST /category/addCategory</li>
                <li><strong>删除分类:</strong> POST /category/deleteCategory</li>
                <li><strong>获取列表:</strong> GET /category/listAll</li>
                <li><strong>更新分类:</strong> POST /category/updateCategory</li>
            </ul>
        </div>

        <div class="category-list">
            <h3>分类列表 ({{ categoryList.length }}条)</h3>
            <div v-if="loading" class="loading">加载中...</div>
            <div v-else-if="categoryList.length === 0" class="empty">暂无分类信息</div>
            <div v-else class="category-items">
                <div v-for="category in categoryList" :key="category.id" class="category-item">
                    <div class="category-info">
                        <div class="category-color" :style="{ backgroundColor: category.color }"></div>
                        <div class="category-details">
                            <h4>{{ category.title }}</h4>
                            <p><strong>ID:</strong> {{ category.id }}</p>
                            <p><strong>颜色:</strong> {{ category.color }}</p>
                        </div>
                    </div>
                    <div class="category-actions">
                        <el-button size="small" type="primary" @click="testUpdateCategory(category)">
                            更新
                        </el-button>
                        <el-button size="small" type="danger" @click="testDeleteCategory(category.id!)">
                            删除
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.category-management {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
    margin: 0;
    color: #303133;
}

.api-info {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.api-info h3 {
    margin-top: 0;
    color: #303133;
}

.api-info ul {
    margin: 0;
    padding-left: 20px;
}

.api-info li {
    margin-bottom: 8px;
    color: #606266;
}

.category-list h3 {
    color: #303133;
    margin-bottom: 20px;
}

.loading,
.empty {
    text-align: center;
    padding: 40px;
    color: #909399;
}

.category-items {
    display: grid;
    gap: 20px;
}

.category-item {
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.category-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.category-color {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    flex-shrink: 0;
}

.category-details h4 {
    margin: 0 0 10px 0;
    color: #303133;
}

.category-details p {
    margin: 5px 0;
    color: #606266;
    font-size: 14px;
}

.category-actions {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}
</style>
