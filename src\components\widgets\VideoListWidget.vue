<template>
    <div class="video-list-widget">
        <div class="video-list-header" v-if="showTitle">
            <h2 class="list-title">{{ title || '实践基地课程' }}</h2>
        </div>
        <div class="video-list-grid">
            <div v-for="(video, index) in props.videos" :key="index" class="video-item">
                <div class="video-thumbnail-wrapper">
                    <!-- 视频缩略图 -->
                    <div class="video-thumbnail" @click="handleVideoPlay(video)">
                        <img v-if="video.thumbnail" :src="video.thumbnail" :alt="video.title" class="thumbnail-image" />
                        <div v-else class="thumbnail-placeholder">
                            <Icon icon="material-symbols:video-library"
                                style="width: 48px; height: 48px; color: #999;" />
                        </div>
                        <!-- 播放按钮覆盖层 -->
                        <div class="play-overlay">
                            <div class="play-button">
                                <Icon icon="material-symbols:play-arrow" style="width: 32px; height: 32px;" />
                            </div>
                        </div>
                        <!-- 视频时长 -->
                        <div v-if="video.duration" class="video-duration">{{ video.duration }}</div>
                    </div>
                </div>

                <div class="video-info">
                    <h3 class="video-title">{{ video.title }}</h3>
                    <p class="video-description">{{ video.description }}</p>
                    <div class="video-meta">
                        <span v-if="video.category" class="video-category">{{ video.category }}</span>
                        <span v-if="video.date" class="video-date">{{ video.date }}</span>
                    </div>
                    <!-- 下载按钮 -->
                    <div class="video-actions">
                        <a v-if="video.downloadFile?.url" :href="video.downloadFile.url"
                            :download="video.downloadFile.name || video.title" class="download-btn" title="点击下载参考教案">
                            <Icon icon="material-symbols:download" style="width: 16px; height: 16px;" />
                            参考教案
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!props.videos || props.videos.length === 0" class="empty-state">
            <Icon icon="material-symbols:video-library" style="width: 64px; height: 64px; color: #c0c4cc;" />
            <p>暂无视频内容</p>
        </div>

        <!-- 视频播放模态框 -->
        <div v-if="showVideoModal" class="video-modal" @click="closeVideoModal">
            <div class="modal-content" @click.stop>
                <div class="modal-header">
                    <h3>{{ currentVideo?.title }}</h3>
                    <button class="close-btn" @click="closeVideoModal">
                        <Icon icon="material-symbols:delete" style="width: 24px; height: 24px;" />
                    </button>
                </div>
                <div class="modal-body">
                    <video v-if="currentVideo?.videoUrl" :src="currentVideo.videoUrl" controls autoplay
                        style="width: 100%; height: auto;">
                        您的浏览器不支持视频播放。
                    </video>
                    <div v-else class="no-video">
                        <p>视频文件不可用</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Icon } from '@iconify/vue'

// 定义视频项接口
interface VideoItem {
    id?: string
    title: string
    description: string
    thumbnail?: string
    videoUrl?: string
    duration?: string
    category?: string
    date?: string
    downloadFile?: {
        name: string
        url: string
        description?: string
    }
}

// 定义组件属性
interface Props {
    title?: string
    showTitle?: boolean
    videos?: VideoItem[]
    gridColumns?: number
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
    title: '实践基地课程',
    showTitle: true,
    gridColumns: 2,
    videos: () => [
        {
            id: '1',
            title: '民之所盼 政之所向（马院）——小学生看到的"12345"',
            description: '本课程通过小学生的视角，了解政府服务热线12345的重要作用，培养学生的公民意识和社会责任感。',
            thumbnail: '',
            videoUrl: '',
            duration: '25:30',
            category: '马院',
            date: '2024-01-15',
            downloadFile: undefined
        },
        {
            id: '2',
            title: '民之所盼 政之所向（初中组）——高考静音不容易 高考静音也容易',
            description: '通过初中生的角度探讨高考期间的静音管理，理解政府在民生服务中的作用和意义。',
            thumbnail: '',
            videoUrl: '',
            duration: '30:45',
            category: '初中组',
            date: '2024-01-20',
            downloadFile: undefined
        },
        {
            id: '3',
            title: '小小热线解决老问题小区大变革',
            description: '讲述社区热线如何解决居民生活中的实际问题，展现基层治理的智慧和效果。',
            thumbnail: '',
            videoUrl: '',
            duration: '22:15',
            category: '社区治理',
            date: '2024-01-25',
            downloadFile: undefined
        },
        {
            id: '4',
            title: '"迎检问诊"为走过路过"良药"',
            description: '探讨如何通过有效的检查和诊断，为社会发展提供良好的解决方案和治理经验。',
            thumbnail: '',
            videoUrl: '',
            duration: '28:20',
            category: '政府治理',
            date: '2024-02-01',
            downloadFile: undefined
        }
    ]
})

// 视频播放相关状态
const showVideoModal = ref(false)
const currentVideo = ref<VideoItem | null>(null)

// 处理视频播放
const handleVideoPlay = (video: VideoItem) => {
    currentVideo.value = video
    showVideoModal.value = true
}

// 关闭视频模态框
const closeVideoModal = () => {
    showVideoModal.value = false
    currentVideo.value = null
}
</script>

<style scoped>
.video-list-widget {
    width: 100%;
    padding: 32px 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.video-list-header {
    margin-bottom: 40px;
    text-align: center;
}

.list-title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
    padding-bottom: 16px;
    border-bottom: 3px solid #409eff;
    display: inline-block;
    letter-spacing: 0.5px;
}

.video-list-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 32px;
    margin-bottom: 48px;
    width: 100%;
}

.video-item {
    flex: 1 1 45%;
    min-width: 300px;
    max-width: 48%;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 4px 16px rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.04);
}

.video-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 16px 32px rgba(0, 0, 0, 0.08);
    border-color: rgba(64, 158, 255, 0.2);
}

.video-thumbnail-wrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    /* 16:9 aspect ratio */
    background: #f5f5f5;
    overflow: hidden;
}

.video-thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-thumbnail:hover .play-overlay {
    opacity: 1;
}

.play-button {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    transition: transform 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.video-info {
    padding: 24px;
}

.video-title {
    font-size: 17px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 12px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    letter-spacing: 0.2px;
    word-break: break-word;
}

.video-description {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin: 0 0 18px 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
}

.video-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 18px;
    font-size: 13px;
    flex-wrap: wrap;
}

.video-category {
    background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
    color: #409eff;
    padding: 6px 12px;
    border-radius: 16px;
    font-weight: 500;
    font-size: 12px;
    border: 1px solid rgba(64, 158, 255, 0.2);
}

.video-date {
    color: #999;
    font-weight: 400;
}

.video-actions {
    display: flex;
    justify-content: flex-start;
}

.download-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.download-btn:hover {
    background: linear-gradient(135deg, #337ecc 0%, #2b6cb0 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state p {
    margin: 16px 0 0 0;
    font-size: 16px;
}

/* 视频播放模态框 */
.video-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90%;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #666;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background: #f5f5f5;
}

.modal-body {
    padding: 20px;
}

.no-video {
    text-align: center;
    padding: 40px;
    color: #999;
}

/* 平板端适配 */
@media (max-width: 768px) {
    .video-list-widget {
        padding: 24px 16px;
    }

    .video-list-header {
        margin-bottom: 32px;
        text-align: center;
    }

    .list-title {
        font-size: 24px;
    }

    .video-list-grid {
        gap: 24px;
    }

    .video-item {
        flex: 1 1 100%;
        min-width: 0;
        max-width: 100%;
        width: 100%;
    }

    .video-info {
        padding: 20px;
    }

    .video-title {
        font-size: 16px;
    }

    .video-description {
        font-size: 13px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-header,
    .modal-body {
        padding: 15px;
    }
}

/* 移动端适配 */
@media (max-width: 480px) {
    .video-list-widget {
        padding: 16px 12px;
    }

    .video-list-header {
        margin-bottom: 24px;
        text-align: center;
    }

    .list-title {
        font-size: 20px;
        padding-bottom: 12px;
    }

    .video-list-grid {
        gap: 16px;
    }

    .video-item {
        border-radius: 12px;
        width: 100%;
        min-width: 0;
        max-width: 100%;
    }

    .video-info {
        padding: 16px;
    }

    .video-title {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .video-description {
        font-size: 12px;
        margin-bottom: 12px;
    }

    .video-meta {
        gap: 8px;
        margin-bottom: 12px;
        font-size: 12px;
        flex-wrap: wrap;
    }

    .video-category {
        padding: 4px 8px;
        font-size: 11px;
    }

    .download-btn {
        padding: 8px 16px;
        font-size: 12px;
    }

    .play-button {
        width: 50px;
        height: 50px;
    }
}

/* 超小屏幕适配 */
@media (max-width: 320px) {
    .video-list-widget {
        padding: 12px 8px;
    }

    .list-title {
        font-size: 18px;
    }

    .video-list-grid {
        gap: 12px;
    }

    .video-info {
        padding: 12px;
    }

    .video-title {
        font-size: 14px;
    }

    .video-description {
        font-size: 11px;
    }

    .video-meta {
        gap: 6px;
        font-size: 11px;
        flex-wrap: wrap;
    }

    .video-category {
        padding: 3px 6px;
        font-size: 10px;
    }

    .download-btn {
        padding: 6px 12px;
        font-size: 11px;
    }

    .play-button {
        width: 40px;
        height: 40px;
    }
}
</style>