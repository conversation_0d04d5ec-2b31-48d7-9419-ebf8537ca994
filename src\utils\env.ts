// 环境配置工具

// 获取当前环境
export const getCurrentEnv = () => {
  return import.meta.env.VITE_APP_ENV || import.meta.env.MODE || 'development'
}

// 获取 API 基础地址
export const getApiBaseUrl = () => {
  return import.meta.env.VITE_API_BASE_URL || 'https://api.dszkmap.com/manage'
}

// 获取应用标题
export const getAppTitle = () => {
  return import.meta.env.VITE_APP_TITLE || 'JXNU Admin'
}

// 判断是否为开发环境
export const isDevelopment = () => {
  return getCurrentEnv() === 'development'
}

// 判断是否为测试环境
export const isTest = () => {
  return getCurrentEnv() === 'test'
}

// 判断是否为生产环境
export const isProduction = () => {
  return getCurrentEnv() === 'production'
}

// 获取环境配置信息
export const getEnvConfig = () => {
  return {
    env: getCurrentEnv(),
    apiBaseUrl: getApiBaseUrl(),
    appTitle: getAppTitle(),
    isDev: isDevelopment(),
    isTest: isTest(),
    isProd: isProduction(),
  }
}
