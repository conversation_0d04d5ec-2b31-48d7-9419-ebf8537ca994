// 基础组件导入
import TextWidget from './TextWidget.vue'
import TitleWidget from './TitleWidget.vue'
import ImageWidget from './ImageWidget.vue'
import ButtonWidget from './ButtonWidget.vue'
import VideoWidget from './VideoWidget.vue'

// 业务组件导入
import BannerWidget from './BannerWidget.vue'
import ImageTextWidget from './ImageTextWidget.vue'
import InfoGridWidget from './InfoGridWidget.vue'
import FileListWidget from './FileListWidget.vue'
import CarouselWidget from './CarouselWidget.vue'
import CardWidget from './CardWidget.vue'
import VideoListWidget from './VideoListWidget.vue'

// 全局组件导入
import DefaultHeader from '../DefaultHeader.vue'
import FooterWidget from '../DefaultFooter.vue'

// 组件映射表
export const widgetMap: Record<string, any> = {
  // 基础组件
  title: TitleWidget,
  text: TextWidget,
  image: ImageWidget,
  video: VideoWidget,
  button: ButtonWidget,

  // 业务组件
  banner: BannerWidget,
  imageText: ImageTextWidget,
  infoGrid: InfoGridWidget,
  fileList: FileListWidget,
  carousel: CarouselWidget,
  card: CardWidget,
  videoList: VideoListWidget,

  // 全局组件
  defaultHeader: DefaultHeader,
  footer: FooterWidget,
}

// 组件类型定义
export type WidgetType = keyof typeof widgetMap

// 导出所有组件（用于动态导入）
export {
  TitleWidget,
  TextWidget,
  ImageWidget,
  VideoWidget,
  ButtonWidget,
  BannerWidget,
  ImageTextWidget,
  InfoGridWidget,
  FileListWidget,
  CarouselWidget,
  CardWidget,
  VideoListWidget,
  DefaultHeader,
  FooterWidget,
}
