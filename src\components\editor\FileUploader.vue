<template>
    <div class="file-uploader">
        <el-upload action="#" :auto-upload="false" :show-file-list="false" :accept="accept" @change="handleFileChange"
            :disabled="uploading">
            <el-button size="small" type="primary" plain :loading="uploading">
                <Icon icon="material-symbols:upload" style="width: 16px; height: 16px;" />
                {{ uploading ? '上传中...' : '选择文件' }}
            </el-button>
        </el-upload>

        <div v-if="currentFile" class="file-preview">
            <div class="file-info">
                <span class="file-icon">{{ getFileIcon(currentFile.name) }}</span>
                <div class="file-details">
                    <div class="file-name">{{ currentFile.name }}</div>
                    <div class="file-size">{{ formatFileSize(currentFile.size) }}</div>
                </div>
            </div>
            <!-- <el-button size="small" type="danger" circle @click="removeFile">
                <Icon icon="material-symbols:delete" style="width: 12px; height: 12px;" />
            </el-button> -->
        </div>

        <div v-if="tip" class="upload-tip">{{ tip }}</div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { ElMessage } from 'element-plus'
import { uploadImage } from '@/api/user'

interface Props {
    modelValue?: { name: string; url: string; description: string }
    placeholder?: string
    accept?: string
    tip?: string
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({ name: '', url: '', description: '' }),
    placeholder: '请选择要上传的文件',
    accept: '*/*',
    tip: '支持常见文档、图片、视频等格式'
})

const emit = defineEmits<{
    'update:modelValue': [value: { name: string; url: string; description: string }]
}>()

const currentFile = ref<File | null>(null)
const uploading = ref(false)

// 监听 modelValue 变化，如果有已上传的文件信息，显示预览
watch(() => props.modelValue, (newValue) => {
    if (newValue?.name && newValue?.url) {
        // 如果有文件信息但没有当前文件对象，创建一个模拟的文件对象用于显示
        if (!currentFile.value) {
            currentFile.value = {
                name: newValue.name,
                size: 0, // 无法获取实际大小，设为0
            } as File
        }
    } else {
        currentFile.value = null
    }
}, { immediate: true })

// 处理文件选择
const handleFileChange = async (file: any) => {
    if (file.raw) {
        currentFile.value = file.raw

        // 检查文件类型
        const isImage = file.raw.type.startsWith('image/')
        if (!isImage) {
            ElMessage.error('只能上传图片文件!')
            return
        }

        // 检查文件大小 (5MB)
        const isLt5M = file.raw.size / 1024 / 1024 < 5
        if (!isLt5M) {
            ElMessage.error('图片大小不能超过 5MB!')
            return
        }

        uploading.value = true

        try {
            const response = await uploadImage({ file: file.raw })

            if (response.code === '200') {
                const url = response.data
                const fileData = {
                    name: file.raw.name,
                    url: url,
                    description: props.modelValue?.description || ''
                }
                emit('update:modelValue', fileData)
                ElMessage.success('上传成功')
            } else {
                ElMessage.error(response.msg || '上传失败')
            }
        } catch (error) {
            console.error('上传错误:', error)
            ElMessage.error('上传失败，请检查网络连接')
        } finally {
            uploading.value = false
        }
    }
}

// 移除文件
const removeFile = () => {
    currentFile.value = null
    const fileData = {
        name: '',
        url: '',
        description: props.modelValue?.description || ''
    }
    emit('update:modelValue', fileData)
}

// 获取文件图标
const getFileIcon = (fileName: string) => {
    if (!fileName || typeof fileName !== 'string') {
        return '📄'
    }
    const extension = fileName.split('.').pop()?.toLowerCase()
    const iconMap: Record<string, string> = {
        pdf: '📄',
        doc: '📝',
        docx: '📝',
        xls: '📊',
        xlsx: '📊',
        ppt: '📽️',
        pptx: '📽️',
        txt: '📄',
        zip: '📦',
        rar: '📦',
        mp4: '🎥',
        mp3: '🎵',
        jpg: '🖼️',
        png: '🖼️',
        gif: '🖼️'
    }
    return iconMap[extension || ''] || '📄'
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-uploader {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-preview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.file-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    word-break: break-all;
}

.file-size {
    font-size: 12px;
    color: #909399;
}

.upload-tip {
    font-size: 11px;
    color: #909399;
    line-height: 1.4;
}
</style>