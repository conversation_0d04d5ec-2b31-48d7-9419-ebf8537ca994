<template>
    <div class="file-uploader">
        <el-upload action="#" :auto-upload="false" :show-file-list="false" :accept="accept" @change="handleFileChange"
            :disabled="uploading" class="file-upload-area">
            <div class="upload-button" :class="{ 'uploading': uploading }">
                <Icon v-if="uploading" icon="material-symbols:upload" class="upload-icon rotating" />
                <Icon v-else icon="material-symbols:upload" class="upload-icon" />
                <span class="upload-text">{{ uploading ? '上传中...' : '选择文件' }}</span>
            </div>
        </el-upload>

        <div v-if="currentFile" class="file-preview">
            <div class="file-info">
                <span class="file-icon">{{ getFileIcon(currentFile.name) }}</span>
                <div class="file-details">
                    <div class="file-name">{{ currentFile.name }}</div>
                    <div class="file-size">{{ formatFileSize(currentFile.size) }}</div>
                </div>
            </div>
            <!-- <el-button size="small" type="danger" circle @click="removeFile">
                <Icon icon="material-symbols:delete" style="width: 12px; height: 12px;" />
            </el-button> -->
        </div>

        <div v-if="tip" class="upload-tip">{{ tip }}</div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { ElMessage } from 'element-plus'
import { uploadImage } from '@/api/user'

interface Props {
    modelValue?: { name: string; url: string; description: string }
    placeholder?: string
    accept?: string
    tip?: string
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({ name: '', url: '', description: '' }),
    placeholder: '请选择要上传的文件',
    accept: '*/*',
    tip: '支持常见文档、图片、视频等格式'
})

const emit = defineEmits<{
    'update:modelValue': [value: { name: string; url: string; description: string }]
}>()

const currentFile = ref<File | null>(null)
const uploading = ref(false)

// 监听 modelValue 变化，如果有已上传的文件信息，显示预览
watch(() => props.modelValue, (newValue) => {
    if (newValue?.name && newValue?.url) {
        // 如果有文件信息但没有当前文件对象，创建一个模拟的文件对象用于显示
        if (!currentFile.value) {
            currentFile.value = {
                name: newValue.name,
                size: 0, // 无法获取实际大小，设为0
            } as File
        }
    } else {
        currentFile.value = null
    }
}, { immediate: true })

// 处理文件选择
const handleFileChange = async (file: { raw?: File }) => {
    if (file.raw) {
        currentFile.value = file.raw

        // 检查文件大小限制（如果需要可以根据文件类型调整）
        // 移除了文件类型限制，因为uploadImage接口支持各种格式

        // 检查文件大小 - 根据文件类型调整限制
        const fileType = file.raw.type
        const fileSizeMB = file.raw.size / 1024 / 1024

        let maxSize = 5 // 默认5MB
        if (fileType.startsWith('video/')) {
            maxSize = 100 // 视频文件限制为100MB
        } else if (fileType.startsWith('image/')) {
            maxSize = 10 // 图片文件限制为10MB
        }

        if (fileSizeMB > maxSize) {
            ElMessage.error(`文件大小不能超过 ${maxSize}MB!`)
            return
        }

        uploading.value = true

        try {
            // 使用uploadImage接口上传文件（虽然名字是uploadImage，但支持各种文件格式）
            const response = await uploadImage({ file: file.raw })

            if (response.code === '200') {
                const url = response.data
                const fileData = {
                    name: file.raw.name,
                    url: url,
                    description: props.modelValue?.description || ''
                }
                emit('update:modelValue', fileData)
                ElMessage.success('文件上传成功')
            } else {
                ElMessage.error(response.msg || '上传失败')
            }
        } catch (error) {
            console.error('文件上传错误:', error)
            ElMessage.error('上传失败，请检查网络连接')
        } finally {
            uploading.value = false
        }
    }
}

// 移除文件（当前未在模板中使用，如需要可以启用）
// const removeFile = () => {
//     currentFile.value = null
//     const fileData = {
//         name: '',
//         url: '',
//         description: props.modelValue?.description || ''
//     }
//     emit('update:modelValue', fileData)
// }

// 获取文件图标
const getFileIcon = (fileName: string) => {
    if (!fileName || typeof fileName !== 'string') {
        return '📄'
    }
    const extension = fileName.split('.').pop()?.toLowerCase()
    const iconMap: Record<string, string> = {
        // 文档类
        pdf: '📄',
        doc: '📝',
        docx: '📝',
        txt: '📄',
        rtf: '📝',
        // 表格类
        xls: '📊',
        xlsx: '📊',
        csv: '📊',
        // 演示文稿类
        ppt: '📽️',
        pptx: '📽️',
        // 压缩文件类
        zip: '📦',
        rar: '📦',
        '7z': '📦',
        tar: '📦',
        gz: '📦',
        // 图片类
        jpg: '🖼️',
        jpeg: '🖼️',
        png: '🖼️',
        gif: '🖼️',
        bmp: '🖼️',
        svg: '🖼️',
        webp: '🖼️',
        // 视频类
        mp4: '🎥',
        avi: '🎥',
        mov: '🎥',
        wmv: '🎥',
        flv: '🎥',
        mkv: '🎥',
        // 音频类
        mp3: '🎵',
        wav: '🎵',
        flac: '🎵',
        aac: '🎵',
        ogg: '🎵',
        // 代码类
        js: '💻',
        ts: '💻',
        html: '💻',
        css: '💻',
        json: '💻',
        xml: '💻',
        // 其他常见格式
        epub: '📚',
        mobi: '📚'
    }
    return iconMap[extension || ''] || '📄'
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-uploader {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-upload-area {
    width: 100%;
}

.upload-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 15px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 40px;
}

.upload-button:hover {
    border-color: #409eff;
    background: #f0f9ff;
}

.upload-button.uploading {
    border-color: #409eff;
    background: #f0f9ff;
    cursor: not-allowed;
}

.upload-icon {
    font-size: 16px;
    color: #409eff;
}

.upload-icon.rotating {
    animation: rotate 1s linear infinite;
}

.upload-text {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

.upload-button.uploading .upload-text {
    color: #409eff;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.file-preview {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.file-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    word-break: break-all;
}

.file-size {
    font-size: 12px;
    color: #909399;
}

.upload-tip {
    font-size: 11px;
    color: #909399;
    line-height: 1.4;
}
</style>