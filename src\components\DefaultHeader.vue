<script setup lang="ts">
import { computed } from 'vue'

// 定义组件属性
interface Props {
    title?: string
    backgroundColor?: string
    textColor?: string
    height?: string
    fontSize?: string
    fontWeight?: string
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
    title: '页面标题',
    backgroundColor: '#c00',
    textColor: '#ffffff',
    height: '60px',
    fontSize: '18px',
    fontWeight: '600'
})

// 计算样式
const headerStyle = computed(() => ({
    backgroundColor: props.backgroundColor,
    color: props.textColor,
    height: props.height,
    fontSize: props.fontSize,
    fontWeight: props.fontWeight
}))
</script>

<template>
    <header class="default-header" :style="headerStyle">
        <div class="header-content">
            <h1 class="header-title">{{ title }}</h1>
        </div>
    </header>
</template>

<style scoped>
.default-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.header-content {
    width: 100%;
    max-width: 1200px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-title {
    margin: 0;
    padding: 0;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}
</style>