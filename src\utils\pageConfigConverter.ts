import type { PageConfig, ComponentConfig, LayoutConfig, ThemeConfig, PageMetadata } from '../types'

// 旧格式的页面配置类型（兼容性）
interface LegacyPageConfig {
  id: string
  name: string
  description?: string
  baseId?: string
  components: any[]
  settings: any
  header: any
  footer: any
  metadata: any
  version: string
}

/**
 * 页面配置转换工具
 */
export class PageConfigConverter {
  /**
   * 将旧格式转换为新格式
   */
  static toNewFormat(legacyConfig: LegacyPageConfig): PageConfig {
    return {
      id: legacyConfig.id,
      name: legacyConfig.name,
      baseId: legacyConfig.baseId || legacyConfig.id,
      version: legacyConfig.version,
      schemaVersion: '1.0.0',
      components: this.convertComponents(legacyConfig.components),
      layout: this.convertLayout(legacyConfig.settings),
      theme: this.convertTheme(legacyConfig.header, legacyConfig.footer),
      metadata: this.convertMetadata(legacyConfig.metadata),
    }
  }

  /**
   * 转换组件配置
   */
  private static convertComponents(legacyComponents: any[]): ComponentConfig[] {
    return legacyComponents.map((comp, index) => ({
      id: comp.id,
      type: comp.type,
      order: index + 1,
      properties: comp.properties || {},
    }))
  }

  /**
   * 转换布局配置
   */
  private static convertLayout(legacySettings: any): LayoutConfig {
    return {
      width: legacySettings.width || '100%',
      height: legacySettings.height || 'auto',
      backgroundColor: legacySettings.backgroundColor || '#ffffff',
      seo: {
        title: legacySettings.title || '',
        description: legacySettings.description || '',
        keywords: legacySettings.keywords || '',
        viewport: legacySettings.viewport || 'width=device-width, initial-scale=1.0',
      },
      globalStyles: {
        fontFamily: "'PingFang SC', 'Microsoft YaHei', sans-serif",
        fontSize: '16px',
        lineHeight: '1.6',
        spacing: {
          xs: '4px',
          sm: '8px',
          md: '16px',
          lg: '24px',
          xl: '32px',
        },
        colors: {
          primary: '#c00',
          secondary: '#409eff',
          success: '#67c23a',
          warning: '#e6a23c',
          error: '#f56c6c',
          text: '#333333',
          background: '#ffffff',
        },
      },
    }
  }

  /**
   * 转换主题配置
   */
  private static convertTheme(legacyHeader: any, legacyFooter: any): ThemeConfig {
    return {
      header: {
        title: legacyHeader.title || '',
        backgroundColor: legacyHeader.backgroundColor || '#c00',
        textColor: legacyHeader.textColor || '#ffffff',
        height: legacyHeader.height || '60px',
        fontSize: legacyHeader.fontSize || '18px',
        fontWeight: legacyHeader.fontWeight || '600',
      },
      footer: {
        contact: {
          name: legacyFooter.contactName || '',
          phone: legacyFooter.contactPhone || '',
          address: legacyFooter.address || '',
        },
        copyright: legacyFooter.copyrightInfo || '',
        license: legacyFooter.licenseInfo || '',
        backgroundColor: legacyFooter.backgroundColor || '#c00',
        textColor: legacyFooter.textColor || '#FFFFFF',
      },
    }
  }

  /**
   * 转换元数据
   */
  private static convertMetadata(legacyMetadata: any): PageMetadata {
    return {
      createdAt: legacyMetadata.createdAt || new Date().toISOString(),
      updatedAt: legacyMetadata.updatedAt || new Date().toISOString(),
      status: legacyMetadata.status || 'draft',
      version: legacyMetadata.version || '1.0.0',
      category: legacyMetadata.category || 'practice-base',
      tags: legacyMetadata.tags || [],
      createdBy: legacyMetadata.createdBy,
      updatedBy: legacyMetadata.updatedBy,
    }
  }

  /**
   * 验证配置格式
   */
  static validateConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.id) errors.push('缺少必需字段: id')
    if (!config.name) errors.push('缺少必需字段: name')
    if (!config.version) errors.push('缺少必需字段: version')

    if (!Array.isArray(config.components)) {
      errors.push('components 必须是数组')
    }

    if (!config.layout) errors.push('缺少 layout 配置')
    if (!config.theme) errors.push('缺少 theme 配置')
    if (!config.metadata) errors.push('缺少 metadata 配置')

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * 清理配置数据（移除冗余字段）
   */
  static cleanConfig(config: PageConfig): PageConfig {
    const cleaned = { ...config }

    // 清理组件属性中的空值
    cleaned.components = cleaned.components.map((comp) => ({
      ...comp,
      properties: this.cleanObject(comp.properties),
    }))

    return cleaned
  }

  /**
   * 清理对象中的空值
   */
  private static cleanObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) return obj

    const cleaned: any = {}
    for (const [key, value] of Object.entries(obj)) {
      if (value !== null && value !== undefined && value !== '') {
        cleaned[key] = typeof value === 'object' ? this.cleanObject(value) : value
      }
    }
    return cleaned
  }
}
