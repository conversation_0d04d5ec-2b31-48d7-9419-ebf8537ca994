<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft, Save, Preview, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const baseId = ref(route.params.baseId as string)

const editorContent = ref('')
const previewMode = ref(false)
const loading = ref(false)

const goBack = () => {
  router.push('/dashboard')
}

const handleSave = () => {
  loading.value = true
  setTimeout(() => {
    ElMessage.success('保存成功')
    loading.value = false
  }, 1000)
}

const togglePreview = () => {
  previewMode.value = !previewMode.value
}

const handleRefresh = () => {
  ElMessage.info('刷新编辑器')
}

onMounted(() => {
  // 初始化编辑器内容
  editorContent.value = `
    <div class="base-detail-page">
      <h1>基地详情页面</h1>
      <p>这是一个可视化编辑的基地详情页面</p>
      <div class="content-section">
        <h2>基地介绍</h2>
        <p>在这里可以编辑基地的详细介绍内容...</p>
      </div>
    </div>
  `
})
</script>

<template>
  <div class="editor-container">
    <!-- Header -->
    <div class="editor-header">
      <div class="header-left">
        <el-button @click="goBack" circle>
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <h1>页面配置编辑器</h1>
        <span class="base-id">基地ID: {{ baseId }}</span>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="togglePreview" type="info">
          <el-icon><Preview /></el-icon>
          {{ previewMode ? '编辑模式' : '预览模式' }}
        </el-button>
        <el-button @click="handleSave" type="primary" :loading="loading">
          <el-icon><Save /></el-icon>
          保存
        </el-button>
      </div>
    </div>

    <!-- Editor Content -->
    <div class="editor-content">
      <div v-if="!previewMode" class="editor-panel">
        <div class="editor-toolbar">
          <span>HTML编辑器</span>
        </div>
        <el-input
          v-model="editorContent"
          type="textarea"
          :rows="25"
          placeholder="请输入HTML内容"
          class="code-editor"
        />
      </div>
      
      <div v-else class="preview-panel">
        <div class="preview-toolbar">
          <span>预览效果</span>
        </div>
        <div class="preview-content" v-html="editorContent"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.editor-container {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.base-id {
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.editor-content {
  flex: 1;
  padding: 24px;
}

.editor-panel,
.preview-panel {
  height: calc(100vh - 120px);
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.editor-toolbar,
.preview-toolbar {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
  color: #303133;
}

.code-editor {
  border: none;
  border-radius: 0;
}

.code-editor :deep(.el-textarea__inner) {
  border: none;
  border-radius: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
}

.preview-content {
  padding: 24px;
  height: calc(100% - 49px);
  overflow-y: auto;
}

.preview-content :deep(.base-detail-page) {
  max-width: 800px;
  margin: 0 auto;
}

.preview-content :deep(h1) {
  color: #303133;
  font-size: 28px;
  margin-bottom: 16px;
}

.preview-content :deep(h2) {
  color: #606266;
  font-size: 20px;
  margin: 24px 0 12px 0;
}

.preview-content :deep(p) {
  color: #909399;
  line-height: 1.6;
  margin-bottom: 12px;
}

.preview-content :deep(.content-section) {
  margin-top: 32px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .header-left,
  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .editor-content {
    padding: 12px;
  }

  .editor-panel,
  .preview-panel {
    height: calc(100vh - 140px);
  }
}
</style>
