<!-- src/components/editor/widgets/CardWidget.vue -->
<template>
  <div class="card-widget-container">
    <div class="card-widget-scroll" ref="scrollContainer">
      <a v-for="(card, index) in props.cards" :key="index" :href="card.link || '#'" target="_blank" class="card-item">
        <div class="card-image-wrapper">
          <img v-if="card.imageSrc" :src="card.imageSrc" alt="卡片封面" class="card-image" />
          <div v-else class="image-placeholder">上传图片</div>
        </div>
        <div class="card-content">
          <h3 class="card-title">{{ card.title }}</h3>
          <p class="card-description">{{ card.description }}</p>
        </div>
      </a>
    </div>

    <!-- 滑动指示器 -->
    <div class="scroll-indicators" v-if="needsScroll">
      <div class="scroll-hint">
        <Icon icon="material-symbols:swipe-left" style="width: 20px; height: 20px;" />
        <span>左右滑动查看更多</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Icon } from '@iconify/vue'

// 定义组件属性
interface CardItem {
  imageSrc?: string
  title: string
  description: string
  link?: string
}

interface Props {
  cards?: CardItem[]
}

// 滚动容器引用
const scrollContainer = ref<HTMLElement>()

// 计算是否需要滑动模式
const needsScroll = computed(() => {
  return props.cards && props.cards.length > 3
})

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  cards: () => [
    {
      imageSrc: '',
      title: '这是一个示例标题',
      description: '这是一段示例描述，用于展示卡片的外观和感觉。',
      link: '#'
    },
    {
      imageSrc: '',
      title: '这是另一个示例标题',
      description: '更多描述内容，可以展示多行文本截断的效果。',
      link: '#'
    },
    {
      imageSrc: '',
      title: '第三个示例标题',
      description: '这是第三个卡片的描述内容，展示网格布局的效果。',
      link: '#'
    }
  ]
})
</script>

<style scoped>
/* 卡片容器 */
.card-widget-container {
  width: 100%;
  position: relative;
}

/* 横向滚动容器 */
.card-widget-scroll {
  display: flex;
  flex-wrap: nowrap;
  gap: 24px;
  padding: 16px 0 24px 0;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 隐藏滚动条 */
.card-widget-scroll::-webkit-scrollbar {
  display: none;
}

/* 子项平分剩余空间 */
.card-widget-scroll>* {
  flex: 1 1 280px;
  min-width: 280px;
  max-width: 400px;
}

.card-item {
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 4px 16px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.04);
  min-height: 320px;
}

.card-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 16px 32px rgba(0, 0, 0, 0.08);
  border-color: rgba(64, 158, 255, 0.2);
}

.card-image-wrapper {
  width: 100%;
  padding-top: 60%;
  position: relative;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.card-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  font-weight: 500;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.card-content {
  padding: 20px;
  height: calc(100% - 60%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px;
  line-height: 1.5;
  color: #1a1a1a;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  letter-spacing: 0.2px;
}

.card-description {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

/* 滑动指示器 */
.scroll-indicators {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.scroll-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #999;
  font-size: 13px;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.scroll-hint:hover {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

/* 小屏幕优化 */
@media (max-width: 480px) {
  .card-widget-scroll {
    gap: 16px;
    padding: 12px 0 20px 0;
  }

  .card-widget-scroll>* {
    min-width: 260px;
    max-width: 300px;
  }

  .scroll-hint {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* 添加滑动动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.card-item {
  animation: slideIn 0.3s ease-out forwards;
}

.card-item:nth-child(1) {
  animation-delay: 0s;
}

.card-item:nth-child(2) {
  animation-delay: 0.1s;
}

.card-item:nth-child(3) {
  animation-delay: 0.2s;
}

.card-item:nth-child(4) {
  animation-delay: 0.3s;
}

.card-item:nth-child(5) {
  animation-delay: 0.4s;
}
</style>