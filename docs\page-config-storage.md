# 页面配置持久化存储系统

## 概述

本系统实现了低代码编辑器页面配置的完整持久化存储功能，支持将页面配置保存到数据库，并可以导出为JSON文件。

## 功能特性

### ✅ 核心功能

- **完整配置保存**：保存页面设置、组件结构、Header/Footer配置
- **服务器存储**：通过API将配置保存到数据库
- **本地备份**：自动保存到localStorage作为备份
- **JSON导出**：导出完整的页面配置为JSON文件
- **配置导入**：从JSON文件导入页面配置
- **版本管理**：支持配置版本控制和状态管理

### ✅ 容错机制

- **双重保存**：服务器 + 本地存储
- **优雅降级**：服务器不可用时自动使用本地存储
- **错误处理**：完善的错误提示和恢复机制

## JSON配置格式

### 完整配置结构

```json
{
  "id": "页面唯一标识",
  "name": "页面名称",
  "description": "页面描述",
  "baseId": "基地ID",
  "version": "配置版本",
  "components": [
    {
      "id": "组件唯一标识",
      "name": "组件名称",
      "icon": "组件图标",
      "type": "组件类型",
      "x": 0,
      "y": 0,
      "width": 1200,
      "height": 400,
      "properties": {
        // 组件具体属性
      }
    }
  ],
  "settings": {
    "width": "100%",
    "height": "auto",
    "backgroundColor": "#ffffff",
    "title": "页面标题",
    "description": "页面描述",
    "keywords": "关键词",
    "viewport": "视口设置"
  },
  "header": {
    "title": "页眉标题",
    "backgroundColor": "#c00",
    "textColor": "#ffffff",
    "height": "60px",
    "fontSize": "18px",
    "fontWeight": "600"
  },
  "footer": {
    "contactName": "联系人",
    "contactPhone": "联系电话",
    "address": "地址",
    "copyrightInfo": "版权信息",
    "licenseInfo": "许可证信息",
    "backgroundColor": "#c00",
    "textColor": "#FFFFFF"
  },
  "metadata": {
    "createdAt": "创建时间",
    "updatedAt": "更新时间",
    "createdBy": "创建者",
    "updatedBy": "更新者",
    "version": "版本号",
    "status": "状态",
    "tags": ["标签"],
    "category": "分类"
  }
}
```

## API接口设计

### 保存页面配置

```typescript
POST /api/pages/save
Content-Type: application/json

{
  "pageConfig": {
    // 完整的页面配置对象
  },
  "baseId": "基地ID",
  "userId": "用户ID"
}

Response:
{
  "success": true,
  "message": "保存成功",
  "data": {
    "pageId": "页面ID",
    "savedAt": "保存时间"
  }
}
```

### 加载页面配置

```typescript
GET /api/pages/load?pageId=xxx&baseId=xxx

Response:
{
  "success": true,
  "message": "加载成功",
  "data": {
    // 完整的页面配置对象
  }
}
```

### 获取页面列表

```typescript
GET /api/pages/list?baseId=xxx&status=published&page=1&pageSize=10

Response:
{
  "success": true,
  "message": "获取成功",
  "data": {
    "pages": [
      // 页面配置列表
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 导出页面配置

```typescript
GET /api/pages/export/{pageId}

Response: application/json (文件下载)
```

### 导入页面配置

```typescript
POST /api/pages/import
Content-Type: multipart/form-data

{
  "file": "JSON文件",
  "baseId": "基地ID"
}

Response:
{
  "success": true,
  "message": "导入成功",
  "data": {
    "pageId": "新页面ID"
  }
}
```

## 使用方式

### 1. 保存页面配置

```typescript
// 在编辑器中点击保存按钮
const handleSave = async () => {
  try {
    // 构建完整配置
    const pageConfig: PageConfig = {
      id: props.baseId,
      name: currentBase.value?.name,
      components: canvasComponents.value,
      settings: pageSettings.value,
      header: pageHeader.value,
      footer: pageFooter.value,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '1.0.0',
        status: 'draft',
      },
      version: '1.0.0',
    }

    // 保存到服务器
    const response = await pageService.savePage({
      pageConfig,
      baseId: props.baseId,
      userId: 'current-user',
    })

    if (response.success) {
      ElMessage.success('保存成功')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}
```

### 2. 导出JSON文件

```typescript
// 导出当前页面配置
const handleExport = async () => {
  const pageConfig = buildPageConfig()
  localPageService.exportToFile(pageConfig)
  ElMessage.success('导出成功')
}
```

### 3. 导入JSON文件

```typescript
// 导入页面配置
const handleImport = async () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = async (event) => {
    const file = event.target.files?.[0]
    if (file) {
      try {
        const config = await parseJsonFile(file)
        loadPageConfig(config)
        ElMessage.success('导入成功')
      } catch (error) {
        ElMessage.error('导入失败')
      }
    }
  }
  input.click()
}
```

## 数据库设计建议

### 页面配置表 (page_configs)

```sql
CREATE TABLE page_configs (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  base_id VARCHAR(50) NOT NULL,
  version VARCHAR(20) NOT NULL,
  config_data JSON NOT NULL,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  created_by VARCHAR(50),
  updated_by VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_base_id (base_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);
```

### 配置字段说明

- `id`: 页面唯一标识
- `name`: 页面名称
- `description`: 页面描述
- `base_id`: 关联的基地ID
- `version`: 配置版本号
- `config_data`: 完整的JSON配置数据
- `status`: 页面状态（草稿/已发布/已归档）
- `created_by`: 创建者ID
- `updated_by`: 更新者ID
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 部署配置

### 环境变量

```bash
# API基础URL
VITE_API_BASE_URL=http://localhost:3000/api

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=lowcode_editor
DB_USER=root
DB_PASSWORD=password

# 文件存储配置
UPLOAD_PATH=/uploads
MAX_FILE_SIZE=10MB
```

### 后端API实现

建议使用Node.js + Express或Spring Boot实现后端API：

```javascript
// Node.js + Express 示例
app.post('/api/pages/save', async (req, res) => {
  try {
    const { pageConfig, baseId, userId } = req.body

    // 验证数据
    if (!pageConfig || !baseId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数',
      })
    }

    // 保存到数据库
    const result = await db.query(
      'INSERT INTO page_configs (id, name, base_id, version, config_data, created_by) VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE config_data = ?, updated_by = ?, updated_at = NOW()',
      [
        pageConfig.id,
        pageConfig.name,
        baseId,
        pageConfig.version,
        JSON.stringify(pageConfig),
        userId,
        JSON.stringify(pageConfig),
        userId,
      ],
    )

    res.json({
      success: true,
      message: '保存成功',
      data: {
        pageId: pageConfig.id,
        savedAt: new Date().toISOString(),
      },
    })
  } catch (error) {
    console.error('保存失败:', error)
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: error.message,
    })
  }
})
```

## 最佳实践

### 1. 数据验证

- 验证JSON格式的正确性
- 检查必要字段是否存在
- 验证组件类型的有效性

### 2. 性能优化

- 使用数据库索引优化查询
- 实现配置缓存机制
- 大文件上传使用分片上传

### 3. 安全性

- 验证用户权限
- 防止SQL注入
- 文件上传安全检查

### 4. 错误处理

- 完善的错误日志记录
- 用户友好的错误提示
- 自动重试机制

## 示例文件

- `src/examples/page-config-example.json` - 完整的配置示例
- `src/services/pageService.ts` - API服务实现
- `src/types/index.ts` - 类型定义

## 总结

这个持久化存储系统提供了完整的页面配置管理功能，支持：

1. **完整的配置保存**：包含所有页面元素和设置
2. **多种存储方式**：服务器存储 + 本地备份
3. **导入导出功能**：JSON文件格式
4. **版本管理**：支持配置版本控制
5. **容错机制**：优雅的错误处理

通过这个系统，用户可以安全地保存和管理他们的页面配置，实现真正的低代码开发体验。
