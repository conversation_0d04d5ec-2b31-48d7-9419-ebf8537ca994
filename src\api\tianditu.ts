import api from './config'

// 天地图搜索参数接口
export interface TiandituSearchParams {
  keyWord: string
  mapBound: string
  level: string
  queryType: string
  start: string
  count: string
  specify?: string
  dataTypes?: string
  show?: string
}

// 天地图搜索响应接口
export interface TiandituSearchResponse {
  resultType: number
  count: number
  keyword: string
  pois?: PoiItem[]
  statistics?: any[]
  area?: any[]
  lineData?: any[]
  status: {
    infocode: number
    cndesc: string
  }
}

// POI点信息接口
export interface PoiItem {
  name: string
  phone?: string
  address?: string
  lonlat: string
  poiType: number
  eaddress?: string
  ename?: string
  hotPointID: string
  province?: string
  provinceCode?: string
  city?: string
  cityCode?: string
  county?: string
  countyCode?: string
  source: string
  typeCode?: string
  typeName?: string
  stationData?: any[]
}

// 天地图搜索API
export const searchTianditu = (params: TiandituSearchParams): Promise<TiandituSearchResponse> => {
  const postStr = JSON.stringify(params)
  const apiKey = import.meta.env.VITE_TIANDITU_API_KEY || ''

  if (!apiKey) {
    console.warn('天地图API密钥未配置，请在.env文件中设置VITE_TIANDITU_API_KEY')
    return Promise.reject(new Error('天地图API密钥未配置'))
  }

  const url = `http://api.tianditu.gov.cn/v2/search?postStr=${encodeURIComponent(postStr)}&type=query&tk=${apiKey}`

  return fetch(url)
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response.json()
    })
    .catch((error) => {
      console.error('天地图搜索请求失败:', error)
      throw error
    })
}

// 默认搜索参数
export const getDefaultSearchParams = (keyword: string): TiandituSearchParams => ({
  keyWord: keyword,
  mapBound: '115.0,28.0,117.0,30.0', // 江西省大致范围
  level: '12',
  queryType: '1', // 普通搜索
  start: '0',
  count: '20',
  show: '2', // 返回详细POI信息
})

// 搜索建议词
export const searchSuggestions = (keyword: string): Promise<TiandituSearchResponse> => {
  const params = {
    ...getDefaultSearchParams(keyword),
    queryType: '4', // 建议词搜索
    count: '10',
  }
  return searchTianditu(params)
}

// 地名搜索
export const searchPlaceNames = (keyword: string): Promise<TiandituSearchResponse> => {
  const params = {
    ...getDefaultSearchParams(keyword),
    queryType: '7', // 地名搜索
    count: '15',
  }
  return searchTianditu(params)
}

// 地理编码接口参数
export interface GeocoderParams {
  keyWord?: string
  lon?: number
  lat?: number
  ver?: number
}

// 地理编码响应接口
export interface GeocoderResponse {
  status: string
  msg: string
  location?: {
    score: number
    level: string
    lon: string
    lat: string
    keyWord: string
  }
  result?: {
    location: {
      lon: number
      lat: number
    }
    formatted_address: string
    addressComponent: {
      address: string
      town: string
      nation: string
      city: string
      county_code: string
      county: string
      city_code: string
      province_code: string
      town_code: string
      province: string
      road: string
      road_distance: number
      address_distance: number
      poi_distance: number
    }
  }
  searchVersion?: string
}

// 地理编码API（地址转坐标）
export const geocodeAddress = (address: string): Promise<GeocoderResponse> => {
  const apiKey = import.meta.env.VITE_TIANDITU_API_KEY || ''

  if (!apiKey) {
    console.warn('天地图API密钥未配置，请在.env文件中设置VITE_TIANDITU_API_KEY')
    return Promise.reject(new Error('天地图API密钥未配置'))
  }

  const params = {
    keyWord: address,
  }

  const url = `http://api.tianditu.gov.cn/geocoder?ds=${encodeURIComponent(JSON.stringify(params))}&tk=${apiKey}`

  return fetch(url)
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response.json()
    })
    .catch((error) => {
      console.error('地理编码请求失败:', error)
      throw error
    })
}

// 逆地理编码API（坐标转地址）
export const reverseGeocode = (lon: number, lat: number): Promise<GeocoderResponse> => {
  const apiKey = import.meta.env.VITE_TIANDITU_API_KEY || ''

  if (!apiKey) {
    console.warn('天地图API密钥未配置，请在.env文件中设置VITE_TIANDITU_API_KEY')
    return Promise.reject(new Error('天地图API密钥未配置'))
  }

  const params = {
    lon: lon,
    lat: lat,
    ver: 1,
  }

  const url = `http://api.tianditu.gov.cn/geocoder?postStr=${encodeURIComponent(JSON.stringify(params))}&type=geocode&tk=${apiKey}`

  return fetch(url)
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return response.json()
    })
    .catch((error) => {
      console.error('逆地理编码请求失败:', error)
      throw error
    })
}
