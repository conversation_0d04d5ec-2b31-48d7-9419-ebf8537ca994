<!-- src/components/editor/widgets/ImageTextWidget.vue -->
<template>
  <div
    class="image-text-container"
    :class="{
      'image-on-right': props.layoutStyle === 'right-image',
      [`size-${props.size}`]: true,
    }"
    :style="{
      backgroundColor: backgroundColor,
      color: textColor,
    }"
  >
    <div class="image-column">
      <img v-if="props.imageSrc && props.imageSrc !== ''" :src="props.imageSrc" alt="模块配图" />
      <div v-else class="image-placeholder">
        <div class="placeholder-content">
          <Icon
            icon="material-symbols:add-photo-alternate"
            style="width: 48px; height: 48px; color: #c0c4cc"
          />
          <span class="placeholder-text">点击上传图片</span>
          <span class="placeholder-hint">支持 JPG、PNG、GIF 格式</span>
        </div>
      </div>
    </div>
    <div class="text-column" :style="{ color: textColor }">
      {{ props.description }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

// 定义组件属性
interface Props {
  imageSrc?: string
  description?: string
  layoutStyle?: 'left-image' | 'right-image'
  size?: 'small' | 'normal' | 'large'
  backgroundColor?: string
  textColor?: string
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  imageSrc: '',
  description: '这里是详细的描述文字。\n支持换行和多段落输入。',
  layoutStyle: 'left-image',
  size: 'normal',
  backgroundColor: '#ffffff',
  textColor: '#495057',
})
</script>

<style scoped>
/* 这里是"美丽预设"的核心 */
.image-text-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 40px;
  width: 100%;
  min-height: 200px;
  /* 设置最小高度 */
  border-radius: 8px;
  padding: 24px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

/* 小尺寸 */
.image-text-container.size-small {
  gap: 24px;
  padding: 16px;
  min-height: 150px;
}

/* 正常尺寸 */
.image-text-container.size-normal {
  gap: 40px;
  padding: 24px;
  min-height: 200px;
}

/* 大尺寸 */
.image-text-container.size-large {
  gap: 48px;
  padding: 32px;
  min-height: 280px;
}

.image-column {
  flex: 0 0 300px;
  /* 固定宽度，不伸缩 */
  min-width: 0;
  max-width: 300px;
  /* 限制最大宽度 */
  height: 200px;
  /* 固定高度 */
  overflow: hidden;
  /* 超出部分隐藏 */
  border-radius: 8px;
  background: #f8f9fa;
  /* 图片背景色 */
  position: relative;
  transition: all 0.3s ease;
}

/* 小尺寸图片列 */
.size-small .image-column {
  flex: 0 0 200px;
  max-width: 200px;
  height: 150px;
}

/* 正常尺寸图片列 */
.size-normal .image-column {
  flex: 0 0 300px;
  max-width: 300px;
  height: 200px;
}

/* 大尺寸图片列 */
.size-large .image-column {
  flex: 0 0 400px;
  max-width: 400px;
  height: 280px;
}

/* 当切换布局时，改变图片栏的顺序 */
.image-on-right .image-column {
  order: 2;
}

.image-column img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* 保持比例，裁剪超出部分 */
  display: block;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.image-column img:hover {
  transform: scale(1.02);
}

.text-column {
  flex: 1;
  min-width: 0;
  /* 固定的文本样式 */
  font-size: 16px;
  line-height: 1.8;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  /* 与图片高度保持一致 */
  overflow-y: auto;
  padding-right: 8px;
  transition: all 0.3s ease;
}

/* 小尺寸文本列 */
.size-small .text-column {
  font-size: 14px;
  line-height: 1.6;
  max-height: 150px;
}

/* 正常尺寸文本列 */
.size-normal .text-column {
  font-size: 16px;
  line-height: 1.8;
  max-height: 200px;
}

/* 大尺寸文本列 */
.size-large .text-column {
  font-size: 18px;
  line-height: 1.9;
  max-height: 280px;
}

/* 自定义滚动条样式 */
.text-column::-webkit-scrollbar {
  width: 4px;
}

.text-column::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 2px;
}

.text-column::-webkit-scrollbar-thumb {
  background: #c1c7cd;
  border-radius: 2px;
}

.text-column::-webkit-scrollbar-thumb:hover {
  background: #a8b0b8;
}

/* 图片占位符样式 */
.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.image-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  animation: shimmer 2s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.image-placeholder:hover {
  border-color: #409eff;
  background: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
  z-index: 1;
  position: relative;
}

.placeholder-text {
  font-size: 16px;
  color: #606266;
  font-weight: 500;
  margin: 0;
}

.placeholder-hint {
  font-size: 12px;
  color: #909399;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-text-container {
    flex-direction: column;
    gap: 20px;
    padding: 16px;
    min-height: auto;
    /* 移动端不限制最小高度 */
  }

  .image-on-right .image-column {
    order: 1;
  }

  .image-column {
    flex: 0 0 auto;
    width: 100%;
    max-width: 100%;
    height: 150px;
    /* 移动端图片高度稍小 */
  }

  .text-column {
    max-height: none;
    overflow-y: visible;
  }

  .placeholder-text {
    font-size: 14px;
  }

  .placeholder-hint {
    font-size: 11px;
  }

  /* 移动端尺寸调整 */
  .size-small .image-column {
    height: 120px;
  }

  .size-normal .image-column {
    height: 150px;
  }

  .size-large .image-column {
    height: 200px;
  }

  .size-small .text-column {
    font-size: 13px;
  }

  .size-normal .text-column {
    font-size: 14px;
  }

  .size-large .text-column {
    font-size: 16px;
  }
}
</style>
