<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

// 定义组件属性
interface Props {
  text?: string
  buttonType?: 'button' | 'link'
  href?: string
  target?: '_blank' | '_self'
  buttonStyle?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'plain' | 'link'
  buttonSize?: 'small' | 'medium' | 'large'
  buttonShape?: 'default' | 'round' | 'circle'
  hoverEffect?: 'scale' | 'glow' | 'slide' | 'none'
  disabled?: boolean
  loading?: boolean
  width?: number
  height?: number
  backgroundColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: '按钮',
  buttonType: 'button',
  href: '',
  target: '_self',
  buttonStyle: 'primary',
  buttonSize: 'medium',
  buttonShape: 'default',
  hoverEffect: 'scale',
  disabled: false,
  loading: false,
  width: undefined, // 全宽
  height: undefined, // 高度自适应
  backgroundColor: 'white',
})

// 按钮样式配置
const buttonStyles = {
  default: {
    backgroundColor: '#ffffff',
    color: '#606266',
    borderColor: '#dcdfe6',
  },
  primary: {
    backgroundColor: '#409eff',
    color: '#ffffff',
    borderColor: '#409eff',
  },
  success: {
    backgroundColor: '#67c23a',
    color: '#ffffff',
    borderColor: '#67c23a',
  },
  warning: {
    backgroundColor: '#e6a23c',
    color: '#ffffff',
    borderColor: '#e6a23c',
  },
  danger: {
    backgroundColor: '#f56c6c',
    color: '#ffffff',
    borderColor: '#f56c6c',
  },
  plain: {
    backgroundColor: 'transparent',
    color: '#409eff',
    borderColor: '#409eff',
  },
  link: {
    backgroundColor: 'transparent',
    color: '#409eff',
    borderColor: 'transparent',
    textDecoration: 'underline',
  },
}

// 按钮尺寸配置
const buttonSizes = {
  small: {
    fontSize: '12px',
    padding: '8px 16px',
  },
  medium: {
    fontSize: '14px',
    padding: '12px 20px',
  },
  large: {
    fontSize: '16px',
    padding: '16px 24px',
  },
}

// 按钮形状配置
const buttonShapes = {
  default: {
    borderRadius: '4px',
  },
  round: {
    borderRadius: '20px',
  },
  circle: {
    borderRadius: '50%',
    padding: '0',
    aspectRatio: '1',
  },
}

// 计算样式
const buttonStyle = computed(() => {
  // 当buttonType为link时，强制使用link样式
  const styleKey = props.buttonType === 'link' ? 'link' : props.buttonStyle
  const style = buttonStyles[styleKey] || buttonStyles.default
  const size = buttonSizes[props.buttonSize] || buttonSizes.medium
  const shape = buttonShapes[props.buttonShape] || buttonShapes.default

  // 基础样式
  const baseStyle = {
    ...style,
    ...size,
    ...shape,
    outline: 'none',
    textAlign: 'center',
    verticalAlign: 'middle',
    whiteSpace: 'nowrap',
    fontFamily: 'inherit',
    lineHeight: '1',
    cursor: props.disabled ? 'not-allowed' : 'pointer',
    transition: 'all 0.3s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    boxSizing: 'border-box',
    width: '100%',
    height: '100%',
    minWidth: 'auto',
    minHeight: 'auto',
  }

  // 根据buttonType调整样式
  if (props.buttonType === 'link') {
    return {
      ...baseStyle,
      border: 'none',
      textDecoration: 'underline',
      backgroundColor: 'transparent',
      padding: '4px 8px', // 文字链接使用较小的padding
    }
  } else {
    return {
      ...baseStyle,
      border: '1px solid',
      textDecoration: 'none',
    }
  }
})

// 计算CSS类
const buttonClasses = computed(() => ({
  'is-disabled': props.disabled,
  'is-loading': props.loading,
  'is-link': props.buttonType === 'link' || props.buttonStyle === 'link',
  [`hover-${props.hoverEffect}`]: props.hoverEffect !== 'none',
}))
</script>

<template>
  <!-- 链接按钮 -->
  <a v-if="buttonType === 'link' && href && !disabled" :href="href" :target="target" class="button-widget"
    :class="buttonClasses" :style="buttonStyle">
    <span v-if="loading" class="loading-icon">
      <Icon icon="material-symbols:sync" style="width: 16px; height: 16px" />
    </span>
    <span class="button-text">{{ text }}</span>
  </a>

  <!-- 普通按钮 -->
  <button v-else class="button-widget" :class="buttonClasses" :style="buttonStyle" :disabled="disabled">
    <span v-if="loading" class="loading-icon">
      <Icon icon="material-symbols:sync" style="width: 16px; height: 16px" />
    </span>
    <span class="button-text">{{ text }}</span>
  </button>
</template>

<style scoped>
.button-widget {
  position: relative;
  overflow: hidden;
}

/* 基础悬停效果 */
.button-widget:hover:not(.is-disabled) {
  opacity: 0.8;
}

.button-widget:active:not(.is-disabled) {
  transform: scale(0.98);
}

/* 自定义悬停效果 */
.button-widget.hover-scale:hover:not(.is-disabled) {
  transform: scale(1.05);
}

.button-widget.hover-glow:hover:not(.is-disabled) {
  box-shadow: 0 0 20px rgba(64, 158, 255, 0.4);
}

.button-widget.hover-slide:hover:not(.is-disabled)::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.button-widget.hover-slide:hover:not(.is-disabled)::before {
  left: 100%;
}

/* 文字链接特殊样式 */
.button-widget.is-link {
  text-decoration: underline !important;
}

.button-widget.is-link:hover:not(.is-disabled) {
  color: #66b1ff;
  text-decoration: underline;
  opacity: 1;
}

/* 状态样式 */
.button-widget.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-widget.is-loading {
  pointer-events: none;
}

.button-widget.is-link {
  text-decoration: none;
}

/* 图标和文本 */
.loading-icon {
  animation: spin 1s linear infinite;
  display: inline-flex;
  align-items: center;
}

.button-text {
  display: inline-flex;
  align-items: center;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-widget {
    font-size: 14px !important;
    padding: 10px 16px !important;
  }
}

/* 打印样式 */
@media print {
  .button-widget {
    border: 1px solid #000 !important;
    background: transparent !important;
    color: #000 !important;
  }
}
</style>
