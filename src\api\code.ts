import api from './config'

// 低代码信息接口类型定义
export interface CodeInfo {
  id?: number
  baseInfoId: number
  codeJson: string
}

// 添加/更新低代码信息
export interface AddCodeInfoParams {
  id?: number | null // 新增时不传或传null，更新时传现有id
  baseInfoId: number
  codeJson: string
}

export interface AddCodeInfoResponse {
  code: string
  msg: string
  data?: {
    id: number // 返回的记录ID
  }
}

export const addCodeInfo = (params: AddCodeInfoParams): Promise<AddCodeInfoResponse> => {
  return api.post('/code/addCodeInfo', params)
}

// 获取基地低代码信息
export interface GetCodeInfoParams {
  id: number
}

export interface GetCodeInfoResponse {
  code: string
  data: CodeInfo
  msg: string
}

export const getCodeInfo = (params: GetCodeInfoParams): Promise<GetCodeInfoResponse> => {
  return api.get(`/code/getCodeInfo?id=${params.id}`)
}
