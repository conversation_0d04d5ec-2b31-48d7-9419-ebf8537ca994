import { uploadImage } from '@/api/user'

const handleFileUpload = async (file: File) => {
if (!file) return false

// 检查文件类型
const isImage = file.type.startsWith('image/')
if (!isImage) {
ElMessage.error('只能上传图片文件!')
return false
}

// 检查文件大小 (5MB)
const isLt5M = file.size / 1024 / 1024 < 5 if (!isLt5M) { ElMessage.error('图片大小不能超过 5MB!') return false }
    uploading.value=true try { const response=await uploadImage({ file }) if (response.code==='200' ) { const
    url=response.data.url uploadedFiles.value.push({ name: file.name, url: url, size: file.size, type: file.type })
    emit('upload-success', url) ElMessage.success('上传成功') } else { ElMessage.error(response.msg || '上传失败' ) } } catch
    (error) { console.error('上传错误:', error) ElMessage.error('上传失败，请检查网络连接') } finally { uploading.value=false } return
    false // 阻止默认上传行为 }