import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'
import AdminLogin from '../views/AdminLogin.vue'
import AdminRegister from '../views/AdminRegister.vue'
import AdminDashboard from '../views/baseAdmin/index.vue'
import LowCodeEditor from '../views/LowCodeEditor.vue'

const routes = [
  {
    path: '/',
    component: AdminLogin,
  },
  {
    path: '/login',
    component: AdminLogin,
  },
  {
    path: '/register',
    component: AdminRegister,
  },
  {
    path: '/dashboard',
    component: AdminDashboard,
  },
  {
    path: '/lowcode-editor/:baseId?',
    component: LowCodeEditor,
    props: true,
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('admin_token')

  // 如果访问的是登录页或注册页，直接放行
  if (to.path === '/' || to.path === '/login' || to.path === '/register') {
    // 如果已经登录，重定向到仪表板
    if (token) {
      next('/dashboard')
    } else {
      next()
    }
    return
  }

  // 对于其他页面，检查是否已登录
  if (!token) {
    // 未登录，重定向到登录页
    next('/')
    return
  }

  // 已登录，检查用户信息是否已加载
  const userStore = useUserStore()
  if (!userStore.userInfo) {
    try {
      // 尝试获取用户信息
      const success = await userStore.fetchUserInfo()
      if (!success) {
        // 获取用户信息失败，可能是token过期
        userStore.logout()
        next('/')
        return
      }
    } catch (error) {
      // 获取用户信息出错，清除token并重定向到登录页
      userStore.logout()
      next('/')
      return
    }
  }

  // 所有检查通过，允许访问
  next()
})

export default router
