import { createRouter, createWebHistory } from 'vue-router'
import AdminLogin from '../views/AdminLogin.vue'
import AdminRegister from '../views/AdminRegister.vue'
import AdminDashboard from '../views/baseAdmin/index.vue'
import LowCodeEditor from '../views/LowCodeEditor.vue'
import ApiDemo from '../views/ApiDemo.vue'
import BaseManagement from '../views/BaseManagement.vue'
import CategoryManagement from '../views/CategoryManagement.vue'

const routes = [
  {
    path: '/',
    redirect: '/login',
  },
  {
    path: '/login',
    component: AdminLogin,
  },
  {
    path: '/register',
    component: AdminRegister,
  },
  {
    path: '/dashboard',
    component: AdminDashboard,
  },
  {
    path: '/lowcode-editor/:baseId?',
    component: LowCodeEditor,
    props: true,
  },
  {
    path: '/api-demo',
    component: ApiDemo,
  },
  {
    path: '/base-management',
    component: BaseManagement,
  },
  {
    path: '/category-management',
    component: CategoryManagement,
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

export default router
