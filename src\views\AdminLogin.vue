<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { User, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { login } from '@/api/user'
import { useUserStore } from '../stores/user'

const router = useRouter()
const userStore = useUserStore()
const loginForm = ref({
  phone: '',
  password: '',
})
const loading = ref(false)

const handleLogin = async () => {
  if (!loginForm.value.phone || !loginForm.value.password) {
    ElMessage.error('请输入手机号和密码')
    return
  }

  loading.value = true

  try {
    const response = await login({
      phone: loginForm.value.phone,
      password: loginForm.value.password,
    })

    if (response.code === '200') {
      ElMessage.success('登录成功')
      userStore.setToken(response.data.token)
      await userStore.fetchUserInfo()
      router.push('/dashboard')
    } else {
      ElMessage.error(response.msg || '登录失败')
    }
  } catch (error) {
    console.error('登录错误:', error)
    ElMessage.error('登录失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const goToRegister = () => {
  router.push('/register')
}


</script>

<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="red-star star-1"></div>
      <div class="red-star star-2"></div>
      <div class="red-star star-3"></div>
      <div class="red-star star-4"></div>
      <div class="red-star star-5"></div>
      <div class="floating-element element-1"></div>
      <div class="floating-element element-2"></div>
      <div class="floating-element element-3"></div>
    </div>

    <!-- 主背景 -->
    <div class="login-background">
      <div class="background-overlay"></div>
      <div class="background-pattern"></div>
    </div>

    <div class="login-content">
      <!-- 系统Logo和标题 -->
      <div class="system-header">
        <div class="logo-container"></div>
        <h1 class="system-title">江西红色教育实践基地</h1>
        <!-- <p class="system-subtitle">管理系统</p> -->
      </div>

      <div class="login-card">
        <div class="login-header">
          <div class="header-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                fill="#E74C3C" />
            </svg>
          </div>
          <h2>管理员登录</h2>
        </div>

        <el-form :model="loginForm" class="login-form" @submit.prevent="handleLogin">
          <el-form-item>
            <el-input v-model="loginForm.phone" placeholder="请输入手机号" size="large" :prefix-icon="User"
              class="custom-input" />
          </el-form-item>

          <el-form-item>
            <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" size="large" :prefix-icon="Lock"
              show-password @keyup.enter="handleLogin" class="custom-input" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="large" :loading="loading" @click="handleLogin" class="login-button">
              <span v-if="!loading">登录系统</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <div class="footer-divider">
            <span class="divider-text">管理系统</span>
          </div>
          <div class="footer-links">
            <p>
              还没有账号？
              <el-button type="text" @click="goToRegister" class="link-button">立即注册</el-button>
            </p>

          </div>
        </div>
      </div>

      <!-- 底部装饰 -->
      <div class="bottom-decoration">
        <div class="red-line"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.red-star {
  position: absolute;
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  clip-path: polygon(50% 0%,
      61% 35%,
      98% 35%,
      68% 57%,
      79% 91%,
      50% 70%,
      21% 91%,
      32% 57%,
      2% 35%,
      39% 35%);
  animation: float 6s ease-in-out infinite;
}

.star-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.star-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.star-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.star-4 {
  bottom: 20%;
  right: 10%;
  animation-delay: 3s;
}

.star-5 {
  top: 50%;
  left: 5%;
  animation-delay: 4s;
}

.floating-element {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(231, 76, 60, 0.3);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
}

.element-1 {
  top: 15%;
  right: 25%;
  animation-delay: 0.5s;
}

.element-2 {
  bottom: 25%;
  left: 30%;
  animation-delay: 1.5s;
}

.element-3 {
  top: 70%;
  right: 20%;
  animation-delay: 2.5s;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 主背景 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
  z-index: 0;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(192, 57, 43, 0.1) 100%);
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(231, 76, 60, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(192, 57, 43, 0.03) 0%, transparent 50%);
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 450px;
  padding: 20px;
}

/* 系统标题 */
.system-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.logo-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(231, 76, 60, 0.3);
}

.logo-icon svg {
  width: 32px;
  height: 32px;
}

.system-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.system-subtitle {
  font-size: 18px;
  color: #e74c3c;
  font-weight: 600;
  margin: 0;
}

/* 登录卡片 */
.login-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.1);
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.header-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.header-icon svg {
  width: 32px;
  height: 32px;
}

.login-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.login-header p {
  color: #e74c3c;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

/* 表单样式 */
.login-form {
  margin-bottom: 24px;
}

.login-form .el-form-item {
  margin-bottom: 20px;
}

.custom-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e8e8e8;
  transition: all 0.3s ease;
  background: #fafafa;
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: #e74c3c;
  background: white;
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: #e74c3c;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1);
  background: white;
}

.custom-input :deep(.el-input__inner) {
  height: 48px;
  font-size: 16px;
}

.custom-input :deep(.el-input__prefix) {
  color: #e74c3c;
}

.login-button {
  width: 100%;
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(231, 76, 60, 0.3);
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(231, 76, 60, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

/* 底部样式 */
.login-footer {
  text-align: center;
  padding-top: 24px;
}

.footer-divider {
  position: relative;
  margin-bottom: 20px;
}

.footer-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e74c3c, transparent);
}

.divider-text {
  background: white;
  padding: 0 16px;
  color: #e74c3c;
  font-size: 12px;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.footer-links p {
  color: #7f8c8d;
  font-size: 14px;
  margin: 8px 0;
}

.link-button {
  color: #e74c3c !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.link-button:hover {
  color: #c0392b !important;
  text-decoration: underline;
}

/* 底部装饰 */
.bottom-decoration {
  text-align: center;
  margin-top: 30px;
}

.red-line {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #e74c3c, #c0392b);
  margin: 0 auto 16px;
  border-radius: 2px;
}

.decoration-text {
  color: #95a5a6;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 1px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    padding: 12px;
  }

  .login-card {
    padding: 32px 24px;
  }

  .system-title {
    font-size: 24px;
  }

  .system-subtitle {
    font-size: 16px;
  }

  .red-star {
    width: 16px;
    height: 16px;
  }

  .floating-element {
    width: 6px;
    height: 6px;
  }
}

@media (max-width: 480px) {
  .system-title {
    font-size: 20px;
  }

  .login-card {
    padding: 24px 20px;
  }

  .custom-input :deep(.el-input__inner) {
    height: 44px;
    font-size: 14px;
  }

  .login-button {
    height: 48px;
    font-size: 14px;
  }
}
</style>
