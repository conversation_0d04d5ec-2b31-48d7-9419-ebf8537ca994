// 控制器选项类型
export interface ControlOption {
  label?: string
  value: string | number | boolean
  icon?: string
  color?: string
}

// 控制器配置类型
export interface ControlConfig {
  type:
    | 'text'
    | 'textarea'
    | 'number'
    | 'color'
    | 'select'
    | 'upload'
    | 'switch'
    | 'slider'
    | 'radio-group'
    | 'radio-group-icon'
    | 'button-group'
    | 'image-upload'
    | 'video-upload'
    | 'range'
    | 'toggle'
    | 'list-editor'
    | 'file-uploader'
    | 'rich-text-editor'
    | 'segmented-control'
  placeholder?: string
  min?: number
  max?: number
  step?: number
  options?: ControlOption[]
  showInput?: boolean
  showStops?: boolean
  showTooltip?: boolean
  formatTooltip?: (val: number) => string
  colors?: string[]
  size?: 'small' | 'default' | 'large'
  clearable?: boolean
  multiple?: boolean
  accept?: string
  maxSize?: number
  showFileList?: boolean
  autoUpload?: boolean
  drag?: boolean
  tip?: string
  disabled?: boolean
  loading?: boolean
  itemConfig?: Array<{
    label: string
    key: string
    type: string
    placeholder?: string
    accept?: string
    tip?: string
  }>
  round?: boolean
  circle?: boolean
  plain?: boolean
  autofocus?: boolean
  nativeType?: 'button' | 'submit' | 'reset'
  icon?: string
  width?: string
  height?: string
  fontSize?: string
  fontWeight?: string
  color?: string
  backgroundColor?: string
  borderColor?: string
  borderRadius?: string
  border?: string
  padding?: string
  margin?: string
  display?: string
  position?: string
  top?: string
  left?: string
  right?: string
  bottom?: string
  zIndex?: string
  opacity?: string
  transform?: string
  transition?: string
  cursor?: string
  userSelect?: string
  overflow?: string
  boxShadow?: string

  [key: string]: unknown
}

// 属性配置项类型
export interface PropertyConfig {
  key: string
  label: string
  defaultValue?: unknown
  control: ControlConfig
}

// 属性分组类型
export interface PropertyGroup {
  name: string
  title: string
  properties: PropertyConfig[]
  collapsible?: boolean
  collapsed?: boolean
}

// 组件配置类型
export interface ComponentConfig {
  type: string
  name: string
  groups: PropertyGroup[]
}

// 组件配置映射
export const componentConfigs: Record<string, ComponentConfig> = {
  // 文本组件配置
  text: {
    type: 'text',
    name: '文本组件',
    groups: [
      {
        name: 'content',
        title: '内容设置',
        properties: [
          {
            key: 'text',
            label: '文本内容',
            defaultValue: '请输入文本内容',
            control: {
              type: 'textarea',
              placeholder: '请输入文本内容，支持HTML标签',
              rows: 6,
            },
          },
          {
            key: 'textStyle',
            label: '文本样式',
            defaultValue: 'body',
            control: {
              type: 'segmented-control',
              options: [
                { label: '正文', value: 'body' },
                { label: '标题', value: 'heading' },
                { label: '副标题', value: 'subtitle' },
                { label: '强调', value: 'emphasis' },
              ],
            },
          },
          {
            key: 'textAlign',
            label: '对齐方式',
            defaultValue: 'center',
            control: {
              type: 'radio-group-icon',
              options: [
                { value: 'left', icon: 'material-symbols:format-align-left' },
                { value: 'center', icon: 'material-symbols:format-align-center' },
                { value: 'right', icon: 'material-symbols:format-align-right' },
              ],
            },
          },
          {
            key: 'color',
            label: '文字颜色',
            defaultValue: '#333333',
            control: {
              type: 'color',
              showAlpha: false,
            },
          },
        ],
      },
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'backgroundColor',
            label: '背景颜色',
            defaultValue: 'white',
            control: {
              type: 'color',
              showAlpha: true,
            },
          },
        ],
      },
    ],
  },

  // 标题组件配置
  title: {
    type: 'title',
    name: '标题组件',
    groups: [
      {
        name: 'content',
        title: '内容设置',
        properties: [
          {
            key: 'text',
            label: '标题内容',
            defaultValue: '这是一个区块标题',
            control: {
              type: 'text',
              placeholder: '请输入标题内容',
            },
          },
          {
            key: 'fontSize',
            label: '字体大小(px)',
            defaultValue: 32,
            control: {
              type: 'slider',
              min: 16,
              max: 48,
              step: 1,
              showInput: true,
              showTooltip: true,
              formatTooltip: (val: number) => `${val}px`,
            },
          },
          {
            key: 'color',
            label: '文字颜色',
            defaultValue: '#333333',
            control: {
              type: 'color',
            },
          },
          {
            key: 'textAlign',
            label: '对齐方式',
            defaultValue: 'center',
            control: {
              type: 'radio-group-icon',
              options: [
                { value: 'left', icon: 'material-symbols:format-align-left' },
                { value: 'center', icon: 'material-symbols:format-align-center' },
                { value: 'right', icon: 'material-symbols:format-align-right' },
              ],
            },
          },
        ],
      },
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'backgroundColor',
            label: '背景颜色',
            defaultValue: 'white',
            control: {
              type: 'color',
              showAlpha: true,
            },
          },
        ],
      },
    ],
  },

  // Banner组件配置
  banner: {
    type: 'banner',
    name: 'Banner横幅',
    groups: [
      {
        name: 'content',
        title: '内容设置',
        properties: [
          {
            key: 'backgroundImage',
            label: '背景图片',
            defaultValue: '',
            control: {
              type: 'image-upload',
              placeholder: '',
              accept: 'image/*',
            },
          },
          {
            key: 'mainTitle',
            label: '主标题',
            defaultValue: 'Banner主标题',
            control: {
              type: 'text',
              placeholder: '请输入主标题',
            },
          },
          {
            key: 'subTitle',
            label: '副标题',
            defaultValue: 'Banner描述内容',
            control: {
              type: 'textarea',
              placeholder: '请输入副标题',
              rows: 3,
            },
          },
        ],
      },
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'height',
            label: '高度(px)',
            defaultValue: 400,
            control: {
              type: 'slider',
              min: 200,
              max: 800,
              step: 10,
              showInput: true,
              showTooltip: true,
              formatTooltip: (val: number) => `${val}px`,
            },
          },
          {
            key: 'mainTitleFontSize',
            label: '主标题字号(px)',
            defaultValue: 48,
            control: {
              type: 'slider',
              min: 24,
              max: 72,
              step: 1,
              showInput: true,
              showTooltip: true,
              formatTooltip: (val: number) => `${val}px`,
            },
          },
          {
            key: 'subTitleFontSize',
            label: '副标题字号(px)',
            defaultValue: 18,
            control: {
              type: 'slider',
              min: 14,
              max: 36,
              step: 1,
              showInput: true,
              showTooltip: true,
              formatTooltip: (val: number) => `${val}px`,
            },
          },
          {
            key: 'overlayColor',
            label: '遮罩颜色',
            defaultValue: 'rgba(0, 0, 0, 0.4)',
            control: {
              type: 'color',
              showAlpha: true,
            },
          },
          {
            key: 'textColor',
            label: '文字颜色',
            defaultValue: '#FFFFFF',
            control: {
              type: 'color',
            },
          },
          {
            key: 'textAlign',
            label: '文字对齐',
            defaultValue: 'center',
            control: {
              type: 'radio-group-icon',
              options: [
                { value: 'left', icon: 'material-symbols:format-align-left' },
                { value: 'center', icon: 'material-symbols:format-align-center' },
                { value: 'right', icon: 'material-symbols:format-align-right' },
              ],
            },
          },
        ],
      },
    ],
  },

  // 图片组件配置
  image: {
    type: 'image',
    name: '图片组件',
    groups: [
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'imageStyle',
            label: '图片样式',
            defaultValue: 'default',
            control: {
              type: 'segmented-control',
              options: [
                { label: '默认', value: 'default' },
                { label: '圆角', value: 'rounded' },
                { label: '圆形', value: 'circle' },
                { label: '阴影', value: 'shadow' },
              ],
            },
          },
          {
            key: 'objectFit',
            label: '填充方式',
            defaultValue: 'cover',
            control: {
              type: 'radio-group-icon',
              options: [
                { value: 'cover', icon: 'material-symbols:crop-square' },
                { value: 'contain', icon: 'material-symbols:fit-screen' },
                { value: 'fill', icon: 'material-symbols:aspect-ratio' },
              ],
            },
          },
        ],
      },
    ],
  },

  // 视频组件配置
  video: {
    type: 'video',
    name: '视频组件',
    groups: [
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'videoStyle',
            label: '视频样式',
            defaultValue: 'default',
            control: {
              type: 'segmented-control',
              options: [
                { label: '默认', value: 'default' },
                { label: '圆角', value: 'rounded' },
                { label: '阴影', value: 'shadow' },
              ],
            },
          },

          {
            key: 'autoplay',
            label: '自动播放',
            defaultValue: false,
            control: {
              type: 'switch',
            },
          },
          {
            key: 'controls',
            label: '显示控制条',
            defaultValue: true,
            control: {
              type: 'switch',
            },
          },
        ],
      },
    ],
  },

  // 按钮组件配置
  button: {
    type: 'button',
    name: '按钮组件',
    groups: [
      {
        name: 'content',
        title: '内容设置',
        properties: [
          {
            key: 'text',
            label: '按钮文字',
            defaultValue: '按钮',
            control: {
              type: 'text',
              placeholder: '请输入按钮文字',
            },
          },
          {
            key: 'buttonType',
            label: '按钮类型',
            defaultValue: 'button',
            control: {
              type: 'segmented-control',
              options: [
                { label: '按钮', value: 'button' },
                { label: '文字链接', value: 'link' },
              ],
            },
          },
          {
            key: 'href',
            label: '链接地址',
            defaultValue: '',
            control: {
              type: 'text',
              placeholder: '请输入链接地址（可选）',
            },
          },
          {
            key: 'target',
            label: '打开方式',
            defaultValue: '_self',
            control: {
              type: 'select',
              options: [
                { label: '当前窗口', value: '_self' },
                { label: '新窗口', value: '_blank' },
              ],
            },
          },
        ],
      },
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'buttonStyle',
            label: '按钮样式',
            defaultValue: 'primary',
            control: {
              type: 'segmented-control',
              options: [
                { label: '主要', value: 'primary' },
                { label: '默认', value: 'default' },
                { label: '成功', value: 'success' },
                { label: '警告', value: 'warning' },
                { label: '危险', value: 'danger' },
                { label: '文字链接', value: 'link' },
              ],
            },
          },
          {
            key: 'buttonShape',
            label: '按钮形状',
            defaultValue: 'default',
            control: {
              type: 'segmented-control',
              options: [
                { label: '默认', value: 'default' },
                { label: '圆角', value: 'round' },
                { label: '圆形', value: 'circle' },
              ],
            },
          },
          {
            key: 'hoverEffect',
            label: '悬停效果',
            defaultValue: 'scale',
            control: {
              type: 'segmented-control',
              options: [
                { label: '缩放', value: 'scale' },
                { label: '发光', value: 'glow' },
                { label: '滑动', value: 'slide' },
                { label: '无', value: 'none' },
              ],
            },
          },
        ],
      },
    ],
  },

  // 关键信息网格组件配置
  infoGrid: {
    type: 'infoGrid',
    name: '关键信息网格',
    groups: [
      {
        name: 'content',
        title: '信息项管理',
        properties: [
          {
            key: 'infoItems',
            label: '信息项列表',
            defaultValue: [
              { label: '基地地址', value: '请输入地址' },
              { label: '联系人', value: '请输入姓名' },
              { label: '联系电话', value: '请输入电话' },
              { label: '开放时间', value: '请输入时间' },
            ],
            control: {
              type: 'list-editor',
              itemConfig: [
                { label: '标签', key: 'label', type: 'text', placeholder: '请输入标签' },
                { label: '值', key: 'value', type: 'text', placeholder: '请输入值' },
              ],
            },
          },
        ],
      },
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'primaryColor',
            label: '主色调',
            defaultValue: '#cc0000',
            control: {
              type: 'color',
            },
          },
          {
            key: 'borderRadius',
            label: '圆角样式',
            defaultValue: 'rounded',
            control: {
              type: 'segmented-control',
              options: [
                { label: '圆角', value: 'rounded' },
                { label: '直角', value: 'square' },
              ],
            },
          },
        ],
      },
    ],
  },

  // 文件列表组件配置
  fileList: {
    type: 'fileList',
    name: '文件列表',
    groups: [
      {
        name: 'content',
        title: '文件项管理',
        properties: [
          {
            key: 'files',
            label: '文件项列表',
            defaultValue: [
              {
                name: 'example.doc',
                url: '#',
                description: '航天博物馆（大学组）——做新时代科技创新的生力军',
              },
              {
                name: 'example.doc',
                url: '#',
                description: '航天博物馆（高中组）——育航天精神 促科技创新',
              },
              {
                name: 'example.doc',
                url: '#',
                description: '航天博物馆（初中组）——航天助力 创新强国',
              },
              {
                name: 'example.doc',
                url: '#',
                description: '航天博物馆（马院）——传承航天精神 矢志科创报国',
              },
            ],
            control: {
              type: 'list-editor',
              itemConfig: [
                {
                  label: '上传文件',
                  key: 'fileUpload',
                  type: 'file-uploader',
                  placeholder: '请选择要上传的文件',
                },
                {
                  label: '文件描述',
                  key: 'description',
                  type: 'text',
                  placeholder: '请输入文件描述',
                },
              ],
            },
          },
        ],
      },
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'borderRadius',
            label: '圆角样式',
            defaultValue: 'rounded',
            control: {
              type: 'segmented-control',
              options: [
                { label: '圆角', value: 'rounded' },
                { label: '直角', value: 'square' },
              ],
            },
          },
        ],
      },
    ],
  },

  // 图文介绍组件配置
  imageText: {
    type: 'imageText',
    name: '图文介绍',
    groups: [
      {
        name: 'content',
        title: '内容设置',
        properties: [
          {
            key: 'imageSrc',
            label: '上传图片',
            defaultValue: '',
            control: {
              type: 'image-upload',
              placeholder: '',
              accept: 'image/*',
            },
          },
          {
            key: 'description',
            label: '详细描述',
            defaultValue: '这里是详细的描述文字。\n支持换行和多段落输入。',
            control: {
              type: 'textarea',
              placeholder: '请输入详细描述内容...',
              rows: 8,
            },
          },
        ],
      },
      {
        name: 'layout',
        title: '布局设置',
        properties: [
          {
            key: 'layoutStyle',
            label: '图片位置',
            defaultValue: 'left-image',
            control: {
              type: 'segmented-control',
              options: [
                { label: '左侧', value: 'left-image' },
                { label: '右侧', value: 'right-image' },
              ],
            },
          },
          {
            key: 'size',
            label: '组件大小',
            defaultValue: 'normal',
            control: {
              type: 'segmented-control',
              options: [
                { label: '小', value: 'small' },
                { label: '正常', value: 'normal' },
                { label: '大', value: 'large' },
              ],
            },
          },
        ],
      },
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'backgroundColor',
            label: '背景颜色',
            defaultValue: '#ffffff',
            control: {
              type: 'color',
              showAlpha: true,
            },
          },
          {
            key: 'textColor',
            label: '文字颜色',
            defaultValue: '#495057',
            control: {
              type: 'color',
            },
          },
        ],
      },
    ],
  },

  // 卡片网格组件配置
  card: {
    type: 'card',
    name: '信息卡片',
    groups: [
      {
        name: 'content',
        title: '卡片管理',
        properties: [
          {
            key: 'cards',
            label: '卡片列表',
            defaultValue: [
              {
                imageSrc: '',
                title: '这是一个示例标题',
                description: '这是一段示例描述，用于展示卡片的外观和感觉。',
                link: '#',
              },
              {
                imageSrc: '',
                title: '这是另一个示例标题',
                description: '更多描述内容，可以展示多行文本截断的效果。',
                link: '#',
              },
              {
                imageSrc: '',
                title: '第三个示例标题',
                description: '这是第三个卡片的描述内容，展示网格布局的效果。',
                link: '#',
              },
            ],
            control: {
              type: 'list-editor',
              itemConfig: [
                {
                  label: '上传图片',
                  key: 'imageSrc',
                  type: 'image-upload',
                  placeholder: '',
                  accept: 'image/*',
                },
                {
                  label: '卡片标题',
                  key: 'title',
                  type: 'text',
                  placeholder: '请输入卡片标题',
                },
                {
                  label: '卡片描述',
                  key: 'description',
                  type: 'textarea',
                  placeholder: '请输入卡片描述内容',
                },
                {
                  label: '链接地址',
                  key: 'link',
                  type: 'text',
                  placeholder: '请输入链接地址（可选）',
                },
              ],
            },
          },
        ],
      },
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'borderRadius',
            label: '圆角样式',
            defaultValue: 'rounded',
            control: {
              type: 'segmented-control',
              options: [
                { label: '圆角', value: 'rounded' },
                { label: '直角', value: 'square' },
              ],
            },
          },
        ],
      },
    ],
  },

  // 底部版权组件配置
  footer: {
    type: 'footer',
    name: '底部版权',
    groups: [
      {
        name: 'content',
        title: '内容设置',
        properties: [
          {
            key: 'contactName',
            label: '联系人姓名',
            defaultValue: '韦雅云',
            control: {
              type: 'text',
              placeholder: '请输入联系人姓名',
            },
          },
          {
            key: 'contactPhone',
            label: '联系电话',
            defaultValue: '010-68755926',
            control: {
              type: 'text',
              placeholder: '请输入联系电话',
            },
          },
          {
            key: 'address',
            label: '联系地址',
            defaultValue: '北京市丰台区万源路1号',
            control: {
              type: 'text',
              placeholder: '请输入联系地址',
            },
          },
          {
            key: 'copyrightInfo',
            label: '版权信息',
            defaultValue: '版权所有 © 2016-2025 北京高校思想政治理论课高精尖创新中心',
            control: {
              type: 'textarea',
              placeholder: '请输入版权信息',
            },
          },
          {
            key: 'licenseInfo',
            label: '许可证信息',
            defaultValue:
              '增值电信业务经营许可证:京B2-20190536 京ICP备10054422号-13 京公网安备110108002480号',
            control: {
              type: 'textarea',
              placeholder: '请输入许可证信息（可选）',
            },
          },
        ],
      },
      {
        name: 'style',
        title: '样式设置',
        properties: [
          {
            key: 'backgroundColor',
            label: '背景颜色',
            defaultValue: '#cc0000',
            control: {
              type: 'color',
            },
          },
          {
            key: 'textColor',
            label: '文字颜色',
            defaultValue: '#FFFFFF',
            control: {
              type: 'color',
            },
          },
        ],
      },
    ],
  },

  // 视频列表组件配置
  videoList: {
    type: 'videoList',
    name: '视频列表',
    groups: [
      {
        name: 'content',
        title: '视频管理',
        properties: [
          {
            key: 'title',
            label: '列表标题',
            defaultValue: '实践基地课程',
            control: {
              type: 'text',
              placeholder: '请输入列表标题',
            },
          },
          {
            key: 'showTitle',
            label: '显示标题',
            defaultValue: true,
            control: {
              type: 'switch',
            },
          },
          {
            key: 'videos',
            label: '视频列表',
            defaultValue: [
              {
                id: '1',
                title: '民之所盼 政之所向（马院）——小学生看到的"12345"',
                description:
                  '本课程通过小学生的视角，了解政府服务热线12345的重要作用，培养学生的公民意识和社会责任感。',
                thumbnail: '',
                videoUrl: '',
                duration: '25:30',
                category: '马院',
                date: '2024-01-15',
                downloadUrl: '#',
              },
              {
                id: '2',
                title: '民之所盼 政之所向（初中组）——高考静音不容易 高考静音也容易',
                description:
                  '通过初中生的角度探讨高考期间的静音管理，理解政府在民生服务中的作用和意义。',
                thumbnail: '',
                videoUrl: '',
                duration: '30:45',
                category: '初中组',
                date: '2024-01-20',
                downloadUrl: '#',
              },
            ],
            control: {
              type: 'list-editor',
              itemConfig: [
                {
                  label: '视频标题',
                  key: 'title',
                  type: 'text',
                  placeholder: '请输入视频标题',
                },
                {
                  label: '视频描述',
                  key: 'description',
                  type: 'textarea',
                  placeholder: '请输入视频描述',
                },
                {
                  label: '视频封面',
                  key: 'thumbnail',
                  type: 'image-upload',
                  placeholder: '',
                  accept: 'image/*',
                },
                {
                  label: '上传视频',
                  key: 'videoUrl',
                  type: 'video-upload',
                  placeholder: '',
                  accept: 'video/*',
                },
                {
                  label: '视频分类',
                  key: 'category',
                  type: 'text',
                  placeholder: '',
                },
                {
                  label: '参考教案',
                  key: 'downloadFile',
                  type: 'file-uploader',
                  placeholder: '请上传参考教案文件',
                  accept: '.pdf,.doc,.docx,.ppt,.pptx',
                },
              ],
            },
          },
        ],
      },
      {
        name: 'layout',
        title: '布局设置',
        properties: [
          {
            key: 'gridColumns',
            label: '每行视频数量',
            defaultValue: 2,
            control: {
              type: 'select',
              options: [
                { label: '1列', value: 1 },
                { label: '2列', value: 2 },
                { label: '3列', value: 3 },
                { label: '4列', value: 4 },
              ],
            },
          },
        ],
      },
    ],
  },
}

// 获取组件配置
export function getComponentConfig(type: string): ComponentConfig | null {
  return (componentConfigs as Record<string, ComponentConfig>)[type] || null
}

// 获取组件默认属性
export function getDefaultProperties(type: string): Record<string, unknown> {
  const config = getComponentConfig(type)
  if (!config) return {}

  const properties: Record<string, unknown> = {}
  config.groups.forEach((group) => {
    group.properties.forEach((property) => {
      if (property.defaultValue !== undefined) {
        properties[property.key] = property.defaultValue
      }
    })
  })
  return properties
}
