<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { addBaseInfo, deleteBaseInfo, listAllBaseInfo, type BaseInfo } from '@/api/base'

// 数据状态
const baseList = ref<BaseInfo[]>([])
const loading = ref(false)

// 获取基地列表
const fetchBaseList = async () => {
    loading.value = true
    try {
        const response = await listAllBaseInfo()
        if (response.code === '200') {
            baseList.value = response.data
            console.log('基地列表:', response.data)
        } else {
            ElMessage.error(response.msg || '获取基地列表失败')
        }
    } catch (error) {
        console.error('获取基地列表错误:', error)
        ElMessage.error('获取基地列表失败，请检查网络连接')
    } finally {
        loading.value = false
    }
}

// 测试新增基地
const testAddBase = async () => {
    const testData: BaseInfo = {
        baseName: '测试基地',
        baseDesc: '这是一个测试基地',
        address: '江西省南昌市红谷滩新区',
        province: '江西省',
        provinceCode: '360000',
        city: '南昌市',
        cityCode: '360100',
        district: '红谷滩新区',
        districtCode: '360106',
        lat: 28.698,
        lng: 115.857,
        phone: '0791-12345678',
        openTime: '09:00-17:00',
        image: '',
        categoryId: 1,
        score: 4.5
    }

    try {
        const response = await addBaseInfo(testData)
        if (response.code === '200') {
            ElMessage.success('新增基地成功')
            fetchBaseList()
        } else {
            ElMessage.error(response.msg || '新增基地失败')
        }
    } catch (error) {
        console.error('新增基地错误:', error)
        ElMessage.error('新增基地失败，请检查网络连接')
    }
}

// 测试删除基地
const testDeleteBase = async (id: number) => {
    try {
        const response = await deleteBaseInfo({ id })
        if (response.code === '200') {
            ElMessage.success('删除基地成功')
            fetchBaseList()
        } else {
            ElMessage.error(response.msg || '删除基地失败')
        }
    } catch (error) {
        console.error('删除基地错误:', error)
        ElMessage.error('删除基地失败，请检查网络连接')
    }
}

// 初始化
onMounted(() => {
    fetchBaseList()
})
</script>

<template>
    <div class="base-management">
        <div class="page-header">
            <h2>基地信息管理 - API测试</h2>
            <el-button type="primary" @click="testAddBase">测试新增基地</el-button>
        </div>

        <div class="api-info">
            <h3>API接口信息</h3>
            <ul>
                <li><strong>新增基地:</strong> POST /base/addBaseInfo</li>
                <li><strong>删除基地:</strong> GET /base/deleteBaseInfo?id={id}</li>
                <li><strong>获取列表:</strong> GET /base/listAll</li>
                <li><strong>更新基地:</strong> POST /base/updateBaseInfo</li>
            </ul>
        </div>

        <div class="base-list">
            <h3>基地列表 ({{ baseList.length }}条)</h3>
            <div v-if="loading" class="loading">加载中...</div>
            <div v-else-if="baseList.length === 0" class="empty">暂无基地信息</div>
            <div v-else class="base-items">
                <div v-for="base in baseList" :key="base.id" class="base-item">
                    <div class="base-info">
                        <h4>{{ base.baseName }}</h4>
                        <p><strong>地址:</strong> {{ base.address }}</p>
                        <p><strong>电话:</strong> {{ base.phone }}</p>
                        <p><strong>开放时间:</strong> {{ base.openTime }}</p>
                        <p><strong>评分:</strong> {{ base.score }}</p>
                        <p><strong>描述:</strong> {{ base.baseDesc }}</p>
                    </div>
                    <div class="base-actions">
                        <el-button size="small" type="danger" @click="testDeleteBase(base.id!)">
                            删除
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.base-management {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
    margin: 0;
    color: #303133;
}

.api-info {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.api-info h3 {
    margin-top: 0;
    color: #303133;
}

.api-info ul {
    margin: 0;
    padding-left: 20px;
}

.api-info li {
    margin-bottom: 8px;
    color: #606266;
}

.base-list h3 {
    color: #303133;
    margin-bottom: 20px;
}

.loading,
.empty {
    text-align: center;
    padding: 40px;
    color: #909399;
}

.base-items {
    display: grid;
    gap: 20px;
}

.base-item {
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.base-info h4 {
    margin: 0 0 15px 0;
    color: #303133;
}

.base-info p {
    margin: 8px 0;
    color: #606266;
}

.base-actions {
    flex-shrink: 0;
    margin-left: 20px;
}
</style>