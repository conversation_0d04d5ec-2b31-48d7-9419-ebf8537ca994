<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { Icon } from '@iconify/vue'
import type { CanvasComponent } from '../../types/editor'

// 定义组件属性
interface Props {
    component: CanvasComponent
    previewMode: boolean
}

const props = defineProps<Props>()

// 轮播图状态
const currentCarouselIndex = ref(0)
let carouselTimer: number | null = null

// 计算组件样式
const componentStyle = computed(() => {
    const style = props.component.properties || {}
    return {
        width: (style as any).width || '200px',
        height: (style as any).height || '100px',
        margin: (style as any).margin || '0px',
        padding: (style as any).padding || '16px',
        backgroundColor: (style as any).backgroundColor || '#ffffff',
        borderRadius: (style as any).borderRadius || '8px',
        border: (style as any).border || '1px solid #e4e7ed',
        display: (style as any).display || 'block',
        flexDirection: (style as any).flexDirection || 'row',
        justifyContent: (style as any).justifyContent || 'flex-start',
        alignItems: (style as any).alignItems || 'flex-start',
        gap: (style as any).gap || '8px'
    }
})

// 获取组件内容
const componentContent = computed(() => {
    return props.component.properties || {}
})

// 轮播图相关计算属性
const carouselImages = computed(() => {
    const images = (componentContent.value as any).images || ''
    return images.split('\n').filter((url: string) => url.trim())
})

const carouselStyle = computed(() => {
    return {
        width: '100%',
        height: '100%',
        position: 'relative' as const,
        overflow: 'hidden' as const
    }
})

// 卡片样式计算
const cardStyle = computed(() => {
    const style = props.component.properties || {}
    const shadow = (style as any).shadow || 'default'
    
    let boxShadow = 'none'
    switch (shadow) {
        case 'default':
            boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)'
            break
        case 'deep':
            boxShadow = '0 4px 16px rgba(0, 0, 0, 0.2)'
            break
        case 'light':
            boxShadow = '0 1px 4px rgba(0, 0, 0, 0.05)'
            break
        case 'none':
        default:
            boxShadow = 'none'
    }
    
    return {
        width: (style as any).width || '300px',
        height: (style as any).height || 'auto',
        backgroundColor: (style as any).backgroundColor || '#ffffff',
        borderRadius: (style as any).borderRadius || '8px',
        border: (style as any).border || '1px solid #e4e7ed',
        boxShadow,
        margin: (style as any).margin || '16px',
        padding: (style as any).padding || '16px'
    }
})

// Banner样式计算
const bannerStyle = computed(() => {
    const style = props.component.properties || {}
    return {
        width: '100%',
        height: (style as any).height || '400px',
        margin: (style as any).margin || '0px',
        position: 'relative' as const,
        overflow: 'hidden' as const
    }
})

const bannerBackgroundStyle = computed(() => {
    const content = props.component.properties || {}
    return {
        position: 'absolute' as const,
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundImage: `url(${(content as any).backgroundImage || ''})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
    }
})

const bannerOverlayStyle = computed(() => {
    const style = props.component.properties || {}
    const opacity = (style as any).overlayOpacity || 0.3
    return {
        position: 'absolute' as const,
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundColor: `rgba(0, 0, 0, ${opacity})`
    }
})

const bannerContentStyle = computed(() => {
    const style = props.component.properties || {}
    return {
        position: 'relative' as const,
        zIndex: 10,
        height: '100%',
        display: 'flex',
        flexDirection: 'column' as const,
        justifyContent: 'center',
        alignItems: (style as any).textAlign || 'center',
        padding: '40px',
        textAlign: (style as any).textAlign || 'center'
    }
})

// 图文介绍样式计算
const imageTextStyle = computed(() => {
    const style = props.component.properties || {}
    const layout = (style as any).layout || 'left'
    
    return {
        width: '100%',
        height: (style as any).height || '400px',
        margin: (style as any).margin || '0px',
        padding: (style as any).padding || '40px',
        backgroundColor: (style as any).backgroundColor || '#ffffff',
        display: 'flex',
        flexDirection: layout === 'right' ? 'row-reverse' as const : 'row' as const,
        alignItems: 'center',
        gap: '40px'
    }
})

const imageTextImageStyle = computed(() => {
    const style = props.component.properties || {}
    return {
        width: (style as any).imageWidth || '50%',
        height: '100%',
        flexShrink: 0
    }
})

const imageTextContentStyle = computed(() => {
    const style = props.component.properties || {}
    return {
        flex: 1,
        display: 'flex',
        flexDirection: 'column' as const,
        justifyContent: 'center',
        gap: '20px'
    }
})

// 关键信息网格样式计算
const infoGridStyle = computed(() => {
    const style = props.component.properties || {}
    return {
        width: '100%',
        height: '100%',
        margin: (style as any).margin || '0px',
        padding: (style as any).padding || '40px',
        backgroundColor: (style as any).backgroundColor || '#f5f7fa'
    }
})

const infoGridContentStyle = computed(() => {
    const style = props.component.properties || {}
    const columns = (style as any).columns || 2
    const gap = (style as any).gap || '20px'
    
    return {
        display: 'grid',
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap,
        marginTop: '24px'
    }
})

const infoGridItemStyle = computed(() => {
    const style = props.component.properties || {}
    return {
        backgroundColor: (style as any).cardBackground || '#ffffff',
        borderRadius: (style as any).borderRadius || '8px',
        padding: '20px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease'
    }
})

// 解析信息项列表
const infoGridItems = computed(() => {
    const content = props.component.properties || {}
    const itemsText = (content as any).infoItems || ''
    
    if (!itemsText.trim()) return []
    
    return itemsText.split('\n')
        .filter((line: string) => line.trim())
        .map((line: string) => {
            const [label, value] = line.split('|')
            return {
                label: label?.trim() || '',
                value: value?.trim() || ''
            }
        })
        .filter((item: { label: string; value: string }) => item.label && item.value)
})

// 文件列表样式计算
const fileListStyle = computed(() => {
    const style = props.component.properties || {}
    return {
        width: '100%',
        height: '100%',
        margin: (style as any).margin || '0px',
        padding: (style as any).padding || '40px',
        backgroundColor: (style as any).backgroundColor || '#ffffff'
    }
})

const fileListItemStyle = computed(() => {
    const style = props.component.properties || {}
    return {
        backgroundColor: (style as any).itemBackground || '#f8f9fa',
        borderRadius: (style as any).borderRadius || '8px',
        transition: 'background-color 0.3s ease'
    }
})

// 解析文件列表
const fileListItems = computed(() => {
    const content = props.component.properties || {}
    const filesText = (content as any).fileItems || ''
    
    if (!filesText.trim()) return []
    
    return filesText.split('\n')
        .filter((line: string) => line.trim())
        .map((line: string) => {
            const [name, type, size] = line.split('|')
            return {
                name: name?.trim() || '',
                type: type?.trim() || '',
                size: size?.trim() || ''
            }
        })
        .filter((file: { name: string; type: string; size: string }) => file.name && file.type)
})

// 获取文件图标
const getFileIcon = (fileType: string) => {
    const type = fileType.toLowerCase()
    if (type.includes('pdf')) return 'material-symbols:description'
    if (type.includes('doc') || type.includes('word')) return 'material-symbols:article'
    if (type.includes('xls') || type.includes('excel')) return 'material-symbols:table-chart'
    if (type.includes('ppt') || type.includes('powerpoint')) return 'material-symbols:slideshow'
    if (type.includes('mp4') || type.includes('avi') || type.includes('mov')) return 'material-symbols:video-file'
    if (type.includes('mp3') || type.includes('wav')) return 'material-symbols:audio-file'
    if (type.includes('jpg') || type.includes('png') || type.includes('gif')) return 'material-symbols:image'
    if (type.includes('zip') || type.includes('rar')) return 'material-symbols:folder-zip'
    return 'material-symbols:insert-drive-file'
}

// 轮播图控制方法
const setCarouselIndex = (index: number) => {
    currentCarouselIndex.value = index
}

const nextCarousel = () => {
    if (carouselImages.value.length > 0) {
        currentCarouselIndex.value = (currentCarouselIndex.value + 1) % carouselImages.value.length
    }
}

const prevCarousel = () => {
    if (carouselImages.value.length > 0) {
        currentCarouselIndex.value = currentCarouselIndex.value === 0
            ? carouselImages.value.length - 1
            : currentCarouselIndex.value - 1
    }
}

// 自动播放
const startAutoPlay = () => {
    if (!props.previewMode) return

    const autoplay = (componentContent.value as any).autoplay
    const interval = (componentContent.value as any).interval || 3

    if (autoplay && carouselImages.value.length > 1) {
        carouselTimer = window.setInterval(() => {
            nextCarousel()
        }, interval * 1000)
    }
}

const stopAutoPlay = () => {
    if (carouselTimer) {
        clearInterval(carouselTimer)
        carouselTimer = null
    }
}

// 生命周期
onMounted(() => {
    startAutoPlay()
})

onUnmounted(() => {
    stopAutoPlay()
})
</script>

<template>
    <div class="component-renderer" :style="componentStyle">
        <!-- 文本组件 -->
        <div v-if="component.type === 'text'" class="text-component">
            {{ (componentContent as any).text || '文本内容' }}
        </div>

        <!-- 标题组件 -->
        <div v-else-if="component.type === 'title'" class="title-component">
            <h3>{{ (componentContent as any).text || '标题' }}</h3>
        </div>

        <!-- 图片组件 -->
        <div v-else-if="component.type === 'image'" class="image-component">
            <img v-if="(componentContent as any).src" :src="(componentContent as any).src"
                :alt="(componentContent as any).alt || '图片'" />
            <div v-else class="image-placeholder">
                <Icon icon="material-symbols:image" style="width: 32px; height: 32px;" />
                <span>图片组件</span>
            </div>
        </div>

        <!-- 按钮组件 -->
        <div v-else-if="component.type === 'button'" class="button-component">
            <button class="custom-button">
                {{ (componentContent as any).text || '按钮' }}
            </button>
        </div>

        <!-- 轮播图组件 -->
        <div v-else-if="component.type === 'carousel'" class="carousel-component">
            <div class="carousel-container" :style="carouselStyle">
                <div class="carousel-wrapper">
                    <div v-for="(image, index) in carouselImages" :key="index" class="carousel-item"
                        :class="{ active: index === currentCarouselIndex }">
                        <img :src="image" :alt="`轮播图${index + 1}`" />
                    </div>
                </div>

                <!-- 指示器 -->
                <div v-if="(componentContent as any).showIndicators && carouselImages.length > 1"
                    class="carousel-indicators">
                    <span v-for="(image, index) in carouselImages" :key="index" class="indicator"
                        :class="{ active: index === currentCarouselIndex }" @click="setCarouselIndex(index)">
                    </span>
                </div>

                <!-- 箭头 -->
                <div v-if="(componentContent as any).showArrows && carouselImages.length > 1" class="carousel-arrows">
                    <button class="arrow prev" @click="prevCarousel">
                        <Icon icon="material-symbols:chevron-left" />
                    </button>
                    <button class="arrow next" @click="nextCarousel">
                        <Icon icon="material-symbols:chevron-right" />
                    </button>
                </div>
            </div>
        </div>

        <!-- 视频组件 -->
        <div v-else-if="component.type === 'video'" class="video-component">
            <div v-if="(componentContent as any).videoType === 'url' && (componentContent as any).videoUrl" class="video-url">
                <video 
                    :src="(componentContent as any).videoUrl"
                    :autoplay="(componentContent as any).autoplay"
                    :controls="(componentContent as any).controls"
                    :loop="(componentContent as any).loop"
                    :muted="(componentContent as any).muted"
                    style="width: 100%; height: 100%; object-fit: cover;"
                >
                    您的浏览器不支持视频播放。
                </video>
            </div>
            <div v-else-if="(componentContent as any).videoType === 'embed' && (componentContent as any).embedCode" 
                 class="video-embed" v-html="(componentContent as any).embedCode">
            </div>
            <div v-else class="video-placeholder">
                <Icon icon="material-symbols:video-library" style="width: 32px; height: 32px;" />
                <span>视频组件</span>
                <p>请配置视频地址或嵌入代码</p>
            </div>
        </div>

        <!-- 卡片组件 -->
        <div v-else-if="component.type === 'card'" class="card-component">
            <div class="card-container" :style="cardStyle">
                <!-- 卡片图片 -->
                <div v-if="(componentContent as any).showImage && (componentContent as any).imageUrl" class="card-image">
                    <img :src="(componentContent as any).imageUrl" :alt="(componentContent as any).title || '卡片图片'" />
                </div>
                
                <!-- 卡片内容 -->
                <div class="card-body">
                    <!-- 标题 -->
                    <div v-if="(componentContent as any).title" class="card-title">
                        <h3>{{ (componentContent as any).title }}</h3>
                        <p v-if="(componentContent as any).subtitle" class="card-subtitle">
                            {{ (componentContent as any).subtitle }}
                        </p>
                    </div>
                    
                    <!-- 内容 -->
                    <div v-if="(componentContent as any).content" class="card-text">
                        <p>{{ (componentContent as any).content }}</p>
                    </div>
                    
                    <!-- 按钮 -->
                    <div v-if="(componentContent as any).showButton && (componentContent as any).buttonText" class="card-button">
                        <button class="card-btn">
                            {{ (componentContent as any).buttonText }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Banner组件 -->
        <div v-else-if="component.type === 'banner'" class="banner-component">
            <div class="banner-container" :style="bannerStyle">
                <!-- 背景图片 -->
                <div class="banner-background" :style="bannerBackgroundStyle"></div>
                
                <!-- 遮罩层 -->
                <div class="banner-overlay" :style="bannerOverlayStyle"></div>
                
                <!-- 内容区域 -->
                <div class="banner-content" :style="bannerContentStyle">
                    <div v-if="(componentContent as any).title" class="banner-title">
                        <h1>{{ (componentContent as any).title }}</h1>
                    </div>
                    
                    <div v-if="(componentContent as any).subtitle" class="banner-subtitle">
                        <h2>{{ (componentContent as any).subtitle }}</h2>
                    </div>
                    
                    <div v-if="(componentContent as any).description" class="banner-description">
                        <p>{{ (componentContent as any).description }}</p>
                    </div>
                    
                    <div v-if="(componentContent as any).showButton && (componentContent as any).buttonText" class="banner-button">
                        <button class="banner-btn">
                            {{ (componentContent as any).buttonText }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图文介绍组件 -->
        <div v-else-if="component.type === 'imageText'" class="image-text-component">
            <div class="image-text-container" :style="imageTextStyle">
                <!-- 图片区域 -->
                <div class="image-text-image" :style="imageTextImageStyle">
                    <img v-if="(componentContent as any).imageUrl" 
                         :src="(componentContent as any).imageUrl" 
                         :alt="(componentContent as any).imageAlt || '图片'" />
                    <div v-else class="image-placeholder">
                        <Icon icon="material-symbols:image" style="width: 32px; height: 32px;" />
                        <span>请配置图片</span>
                    </div>
                </div>
                
                <!-- 文字内容区域 -->
                <div class="image-text-content" :style="imageTextContentStyle">
                    <div v-if="(componentContent as any).title" class="image-text-title">
                        <h2>{{ (componentContent as any).title }}</h2>
                    </div>
                    
                    <div v-if="(componentContent as any).subtitle" class="image-text-subtitle">
                        <h3>{{ (componentContent as any).subtitle }}</h3>
                    </div>
                    
                    <div v-if="(componentContent as any).content" class="image-text-body">
                        <p>{{ (componentContent as any).content }}</p>
                    </div>
                    
                    <div v-if="(componentContent as any).showButton && (componentContent as any).buttonText" class="image-text-button">
                        <button class="image-text-btn">
                            {{ (componentContent as any).buttonText }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关键信息网格组件 -->
        <div v-else-if="component.type === 'infoGrid'" class="info-grid-component">
            <div class="info-grid-container" :style="infoGridStyle">
                <!-- 标题 -->
                <div v-if="(componentContent as any).title" class="info-grid-title">
                    <h2>{{ (componentContent as any).title }}</h2>
                </div>
                
                <!-- 信息网格 -->
                <div class="info-grid-content" :style="infoGridContentStyle">
                    <div v-for="(item, index) in infoGridItems" :key="index" class="info-grid-item" :style="infoGridItemStyle">
                        <div class="info-label">{{ item.label }}</div>
                        <div class="info-value">{{ item.value }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件列表组件 -->
        <div v-else-if="component.type === 'fileList'" class="file-list-component">
            <div class="file-list-container" :style="fileListStyle">
                <!-- 标题区域 -->
                <div class="file-list-header">
                    <div v-if="(componentContent as any).title" class="file-list-title">
                        <h2>{{ (componentContent as any).title }}</h2>
                    </div>
                    <div v-if="(componentContent as any).subtitle" class="file-list-subtitle">
                        <p>{{ (componentContent as any).subtitle }}</p>
                    </div>
                </div>
                
                <!-- 文件列表 -->
                <div class="file-list-content">
                    <div v-for="(file, index) in fileListItems" :key="index" class="file-list-item" :style="fileListItemStyle">
                        <div class="file-icon">
                            <Icon :icon="getFileIcon(file.type)" style="width: 24px; height: 24px;" />
                        </div>
                        <div class="file-info">
                            <div class="file-name">{{ file.name }}</div>
                            <div class="file-meta">
                                <span class="file-type">{{ file.type }}</span>
                                <span class="file-size">{{ file.size }}</span>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="file-download-btn" title="下载文件">
                                <Icon icon="material-symbols:download" style="width: 16px; height: 16px;" />
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 上传按钮 -->
                <div v-if="(componentContent as any).showUpload" class="file-upload-section">
                    <button class="file-upload-btn">
                        <Icon icon="material-symbols:upload" style="width: 16px; height: 16px;" />
                        {{ (componentContent as any).uploadText || '上传文件' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 容器组件 -->
        <div v-else-if="component.type === 'container'" class="container-component">
            <div class="container-content">
                <Icon icon="material-symbols:view-module" style="width: 24px; height: 24px;" />
                <span>容器组件</span>
            </div>
        </div>

        <!-- 行组件 -->
        <div v-else-if="component.type === 'row'" class="row-component">
            <div class="row-content">
                <Icon icon="material-symbols:view-stream" style="width: 24px; height: 24px;" />
                <span>行组件</span>
            </div>
        </div>

        <!-- 列组件 -->
        <div v-else-if="component.type === 'column'" class="column-component">
            <div class="column-content">
                <Icon icon="material-symbols:view-column" style="width: 24px; height: 24px;" />
                <span>列组件</span>
            </div>
        </div>

        <!-- 网格组件 -->
        <div v-else-if="component.type === 'grid'" class="grid-component">
            <div class="grid-content">
                <Icon icon="material-symbols:grid-view" style="width: 24px; height: 24px;" />
                <span>网格组件</span>
            </div>
        </div>

        <!-- 默认组件 -->
        <div v-else class="default-component">
            <Icon :icon="component.icon" style="width: 24px; height: 24px;" />
            <span>{{ component.name }}</span>
        </div>
    </div>
</template>

<style scoped>
.component-renderer {
    position: relative;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

/* 文本组件 */
.text-component {
    font-size: 14px;
    line-height: 1.5;
    color: #333;
}

/* 标题组件 */
.title-component h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 图片组件 */
.image-component {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.image-component img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    gap: 8px;
}

/* 轮播图组件 */
.carousel-component {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.carousel-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.carousel-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
}

.carousel-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.carousel-item.active {
    opacity: 1;
}

.carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 10;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.indicator.active {
    background: #fff;
}

.carousel-arrows {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 10;
}

.arrow {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.3);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

 .arrow:hover {
     background: rgba(0, 0, 0, 0.5);
 }

 /* 视频组件 */
 .video-component {
     width: 100%;
     height: 100%;
     position: relative;
     overflow: hidden;
 }

 .video-url {
     width: 100%;
     height: 100%;
 }

 .video-embed {
     width: 100%;
     height: 100%;
 }

 .video-embed iframe {
     width: 100%;
     height: 100%;
     border: none;
 }

 .video-placeholder {
     display: flex;
     flex-direction: column;
     align-items: center;
     justify-content: center;
     color: #909399;
     gap: 8px;
     height: 100%;
 }

 .video-placeholder p {
     margin: 0;
     font-size: 12px;
     color: #c0c4cc;
 }

 /* 按钮组件 */
.button-component {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.custom-button {
    padding: 8px 16px;
    background: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.custom-button:hover {
    background: #337ecc;
}

 /* 卡片组件 */
 .card-component {
     display: flex;
     align-items: center;
     justify-content: center;
     width: 100%;
     height: 100%;
 }

 .card-container {
     width: 100%;
     max-width: 100%;
     overflow: hidden;
     transition: transform 0.3s ease, box-shadow 0.3s ease;
 }

 .card-container:hover {
     transform: translateY(-2px);
 }

 .card-image {
     width: 100%;
     height: 200px;
     overflow: hidden;
 }

 .card-image img {
     width: 100%;
     height: 100%;
     object-fit: cover;
     transition: transform 0.3s ease;
 }

 .card-container:hover .card-image img {
     transform: scale(1.05);
 }

 .card-body {
     padding: 16px;
 }

 .card-title h3 {
     margin: 0 0 8px 0;
     font-size: 18px;
     font-weight: 600;
     color: #333;
     line-height: 1.4;
 }

 .card-subtitle {
     margin: 0 0 12px 0;
     font-size: 14px;
     color: #666;
     line-height: 1.4;
 }

 .card-text {
     margin-bottom: 16px;
 }

 .card-text p {
     margin: 0;
     font-size: 14px;
     color: #666;
     line-height: 1.6;
 }

 .card-button {
     text-align: center;
 }

 .card-btn {
     padding: 8px 16px;
     background: #409eff;
     color: white;
     border: none;
     border-radius: 4px;
     cursor: pointer;
     font-size: 14px;
     transition: background-color 0.3s ease;
 }

  .card-btn:hover {
     background: #337ecc;
 }

 /* Banner组件 */
 .banner-component {
     width: 100%;
     height: 100%;
 }

 .banner-container {
     width: 100%;
     height: 100%;
     position: relative;
     overflow: hidden;
 }

 .banner-background {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background-size: cover;
     background-position: center;
     background-repeat: no-repeat;
 }

 .banner-overlay {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
 }

 .banner-content {
     position: relative;
     z-index: 10;
     height: 100%;
     display: flex;
     flex-direction: column;
     justify-content: center;
     align-items: center;
     padding: 40px;
     text-align: center;
 }

 .banner-title h1 {
     margin: 0 0 16px 0;
     font-size: 48px;
     font-weight: 700;
     line-height: 1.2;
     text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
 }

 .banner-subtitle h2 {
     margin: 0 0 20px 0;
     font-size: 24px;
     font-weight: 500;
     line-height: 1.3;
     text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
 }

 .banner-description {
     margin-bottom: 32px;
     max-width: 600px;
 }

 .banner-description p {
     margin: 0;
     font-size: 16px;
     line-height: 1.6;
     text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
 }

 .banner-button {
     margin-top: 16px;
 }

 .banner-btn {
     padding: 12px 32px;
     background: #409eff;
     color: white;
     border: none;
     border-radius: 6px;
     cursor: pointer;
     font-size: 16px;
     font-weight: 500;
     transition: all 0.3s ease;
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
 }

 .banner-btn:hover {
     background: #337ecc;
     transform: translateY(-2px);
     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
 }

 /* 图文介绍组件 */
 .image-text-component {
     width: 100%;
     height: 100%;
 }

 .image-text-container {
     width: 100%;
     height: 100%;
     display: flex;
     align-items: center;
     gap: 40px;
 }

 .image-text-image {
     width: 50%;
     height: 100%;
     flex-shrink: 0;
     overflow: hidden;
     border-radius: 8px;
 }

 .image-text-image img {
     width: 100%;
     height: 100%;
     object-fit: cover;
     transition: transform 0.3s ease;
 }

 .image-text-image:hover img {
     transform: scale(1.05);
 }

 .image-text-content {
     flex: 1;
     display: flex;
     flex-direction: column;
     justify-content: center;
     gap: 20px;
 }

 .image-text-title h2 {
     margin: 0;
     font-size: 32px;
     font-weight: 600;
     line-height: 1.2;
 }

 .image-text-subtitle h3 {
     margin: 0;
     font-size: 18px;
     font-weight: 500;
     line-height: 1.3;
     opacity: 0.8;
 }

 .image-text-body {
     margin-bottom: 20px;
 }

 .image-text-body p {
     margin: 0;
     font-size: 16px;
     line-height: 1.6;
     color: #666;
 }

 .image-text-button {
     margin-top: 16px;
 }

 .image-text-btn {
     padding: 10px 24px;
     background: #409eff;
     color: white;
     border: none;
     border-radius: 6px;
     cursor: pointer;
     font-size: 14px;
     font-weight: 500;
     transition: all 0.3s ease;
 }

 .image-text-btn:hover {
     background: #337ecc;
     transform: translateY(-1px);
 }

 /* 关键信息网格组件 */
 .info-grid-component {
     width: 100%;
     height: 100%;
 }

 .info-grid-container {
     width: 100%;
     height: 100%;
     padding: 40px;
 }

 .info-grid-title {
     text-align: center;
     margin-bottom: 32px;
 }

 .info-grid-title h2 {
     margin: 0;
     font-size: 28px;
     font-weight: 600;
     color: #333;
 }

 .info-grid-content {
     display: grid;
     gap: 20px;
     margin-top: 24px;
 }

 .info-grid-item {
     background: #ffffff;
     border-radius: 8px;
     padding: 20px;
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
     transition: transform 0.3s ease, box-shadow 0.3s ease;
     cursor: pointer;
 }

 .info-grid-item:hover {
     transform: translateY(-2px);
     box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
 }

 .info-label {
     font-size: 14px;
     color: #666;
     margin-bottom: 8px;
     font-weight: 500;
 }

 .info-value {
     font-size: 16px;
     color: #333;
     font-weight: 600;
     line-height: 1.4;
 }

 /* 文件列表组件 */
 .file-list-component {
     width: 100%;
     height: 100%;
 }

 .file-list-container {
     width: 100%;
     height: 100%;
     padding: 40px;
 }

 .file-list-header {
     margin-bottom: 32px;
     text-align: center;
 }

 .file-list-title h2 {
     margin: 0 0 8px 0;
     font-size: 28px;
     font-weight: 600;
     color: #333;
 }

 .file-list-subtitle p {
     margin: 0;
     font-size: 16px;
     color: #666;
     line-height: 1.5;
 }

 .file-list-content {
     display: flex;
     flex-direction: column;
     gap: 12px;
     margin-bottom: 24px;
 }

 .file-list-item {
     display: flex;
     align-items: center;
     padding: 16px;
     border-radius: 8px;
     transition: background-color 0.3s ease;
     cursor: pointer;
 }

 .file-list-item:hover {
     background-color: #e9ecef !important;
 }

 .file-icon {
     margin-right: 16px;
     color: #409eff;
     flex-shrink: 0;
 }

 .file-info {
     flex: 1;
     min-width: 0;
 }

 .file-name {
     font-size: 16px;
     font-weight: 500;
     color: #333;
     margin-bottom: 4px;
     white-space: nowrap;
     overflow: hidden;
     text-overflow: ellipsis;
 }

 .file-meta {
     display: flex;
     gap: 16px;
     font-size: 14px;
     color: #666;
 }

 .file-type {
     color: #409eff;
     font-weight: 500;
 }

 .file-size {
     color: #999;
 }

 .file-actions {
     margin-left: 16px;
     flex-shrink: 0;
 }

 .file-download-btn {
     padding: 8px;
     background: #409eff;
     color: white;
     border: none;
     border-radius: 4px;
     cursor: pointer;
     transition: background-color 0.3s ease;
 }

 .file-download-btn:hover {
     background: #337ecc;
 }

 .file-upload-section {
     text-align: center;
     padding-top: 24px;
     border-top: 1px solid #e4e7ed;
 }

 .file-upload-btn {
     padding: 12px 24px;
     background: #67c23a;
     color: white;
     border: none;
     border-radius: 6px;
     cursor: pointer;
     font-size: 14px;
     font-weight: 500;
     transition: background-color 0.3s ease;
     display: inline-flex;
     align-items: center;
     gap: 8px;
 }

 .file-upload-btn:hover {
     background: #5daf34;
 }

 /* 布局组件 */
.container-component,
.row-component,
.column-component,
.grid-component {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border: 2px dashed #e4e7ed;
    background: #fafbfc;
}

.container-content,
.row-content,
.column-content,
.grid-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #909399;
}

/* 默认组件 */
.default-component {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #909399;
}
</style>