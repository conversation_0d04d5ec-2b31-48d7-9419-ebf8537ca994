<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { BaseInfo } from '../api/base'
import { listAllBaseInfo } from '../api/base'
import type {
    CanvasComponent,
    ComponentCategory,
    EditorState,
    ComponentItem
} from '../types/editor'
import type { PageConfig, SavePageRequest } from '../types'
import { getDefaultProperties } from '../types/componentConfigs'
import { widgetMap } from '../components/widgets/index'
import { Icon } from '@iconify/vue'
import { pageService, localPageService } from '../services/pageService'

// 导入子组件
import EditorToolbar from '../components/editor/EditorToolbar.vue'
import ComponentLibrary from '../components/editor/ComponentLibrary.vue'
import DesignCanvas from '../components/editor/DesignCanvas.vue'
import PropertiesPanel from '../components/editor/PropertiesPanel.vue'


// 接收路由参数
const props = defineProps<{
    baseId?: string
}>()

// 当前编辑的基地信息
const currentBase = ref<BaseInfo | null>(null)

const router = useRouter()

// 编辑器状态
const editorState = reactive<EditorState>({
    selectedComponent: null,
    previewMode: false,
    deviceType: 'desktop',
    showGrid: true,
    zoom: 100
})

// 全屏预览状态
const isFullscreenPreview = ref(false)

// 面板状态 - 移除单独的页面设置面板

// 页面设置
const pageSettings = ref({
    width: '100%',
    height: 'auto',
    backgroundColor: '#ffffff',
    title: '井冈山革命博物馆',
    description: '',
    keywords: '',
    viewport: 'width=device-width, initial-scale=1.0'
})

// 页面Header设置
const pageHeader = ref({
    title: '井冈山革命博物馆',
    backgroundColor: '#c00',
    textColor: '#ffffff',
    height: '60px',
    fontSize: '18px',
    fontWeight: '600'
})

// 页面Footer设置
const pageFooter = ref({
    contactName: '韦雅云',
    contactPhone: '010-68755926',
    address: '北京市丰台区万源路1号',
    copyrightInfo: '版权所有 © 2016-2025 北京高校思想政治理论课高精尖创新中心',
    licenseInfo: '增值电信业务经营许可证:京B2-20190536 京ICP备10054422号-13 京公网安备110108002480号',
    backgroundColor: '#c00',
    textColor: '#FFFFFF',
})

// Header和Footer选中状态
const isHeaderSelected = ref(false)
const isFooterSelected = ref(false)

// 定义业务组件类型（只能添加一次的组件）
const businessComponentTypes = ['banner', 'imageText', 'infoGrid', 'fileList', 'card', 'videoList']

// 计算已存在的业务组件类型
const existingBusinessComponents = computed(() => {
    return canvasComponents.value
        .filter(component => businessComponentTypes.includes(component.type))
        .map(component => component.type)
})

// 精简后的组件库 - 聚焦业务，隐藏实现细节
const componentLibrary = ref<ComponentCategory[]>([
    {
        category: '业务组件',
        components: [
            { id: 'banner', name: 'Banner横幅', icon: 'material-symbols:view-module', description: '页面横幅展示，拖拽即可使用' },
            { id: 'imageText', name: '图文介绍', icon: 'material-symbols:article', description: '图片文字介绍布局，适合基地简介' },
            { id: 'infoGrid', name: '关键信息网格', icon: 'material-symbols:grid-view', description: '关键信息网格展示，如联系方式' },
            { id: 'fileList', name: '文件列表', icon: 'material-symbols:folder', description: '文件列表展示，如示范教案' },
            // { id: 'carousel', name: '轮播图', icon: 'material-symbols:view-carousel', description: '图片轮播展示' },
            { id: 'card', name: '信息卡片', icon: 'material-symbols:credit-card', description: '信息卡片展示' },
            { id: 'videoList', name: '视频列表', icon: 'material-symbols:video-library', description: '视频课程列表展示，支持上传视频' }
        ]
    },
    {
        category: '基础组件',
        components: [
            { id: 'title', name: '标题', icon: 'material-symbols:title', description: '标题组件' },
            { id: 'text', name: '文本', icon: 'material-symbols:text-fields', description: '文本内容' },
            { id: 'image', name: '图片', icon: 'material-symbols:image', description: '图片展示' },
            { id: 'video', name: '视频', icon: 'material-symbols:video-library', description: '视频播放器' },
            { id: 'button', name: '按钮', icon: 'material-symbols:smart-button', description: '交互按钮' }
        ]
    }
])

// 画布上的组件
const canvasComponents = ref<CanvasComponent[]>([])

// 组件属性面板数据
const componentProperties = ref<Record<string, unknown>>({})

// 构建页面配置的辅助函数
const buildPageConfig = (): PageConfig => {
    return {
        id: props.baseId || 'page_' + Date.now(),
        name: currentBase.value?.baseName || '未命名页面',
        baseId: props.baseId || '',
        version: '1.0.0',
        schemaVersion: '1.0.0',
        components: canvasComponents.value.map((comp, index) => ({
            id: comp.id,
            type: comp.type,
            order: index + 1,
            properties: comp.properties || {}
        })),
        layout: {
            width: pageSettings.value.width,
            height: pageSettings.value.height,
            backgroundColor: pageSettings.value.backgroundColor,
            seo: {
                title: pageSettings.value.title,
                description: pageSettings.value.description,
                keywords: pageSettings.value.keywords,
                viewport: pageSettings.value.viewport
            },
            globalStyles: {
                fontFamily: "'PingFang SC', 'Microsoft YaHei', sans-serif",
                fontSize: '16px',
                lineHeight: '1.6',
                spacing: {
                    xs: '4px',
                    sm: '8px',
                    md: '16px',
                    lg: '24px',
                    xl: '32px'
                },
                colors: {
                    primary: '#c00',
                    secondary: '#409eff',
                    success: '#67c23a',
                    warning: '#e6a23c',
                    error: '#f56c6c',
                    text: '#333333',
                    background: '#ffffff'
                }
            }
        },
        theme: {
            header: {
                title: pageHeader.value.title,
                backgroundColor: pageHeader.value.backgroundColor,
                textColor: pageHeader.value.textColor,
                height: pageHeader.value.height,
                fontSize: pageHeader.value.fontSize,
                fontWeight: pageHeader.value.fontWeight
            },
            footer: {
                contact: {
                    name: pageFooter.value.contactName,
                    phone: pageFooter.value.contactPhone,
                    address: pageFooter.value.address
                },
                copyright: pageFooter.value.copyrightInfo,
                license: pageFooter.value.licenseInfo,
                backgroundColor: pageFooter.value.backgroundColor,
                textColor: pageFooter.value.textColor
            }
        },
        metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            status: 'draft',
            version: '1.0.0',
            category: 'practice-base',
            tags: [],
            createdBy: 'current-user',
            updatedBy: 'current-user'
        }
    }
}

// 加载页面配置的辅助函数
const loadPageConfig = (config: PageConfig) => {
    // 加载组件配置
    canvasComponents.value = config.components.map(comp => ({
        id: comp.id,
        name: comp.type,
        icon: 'material-symbols:widgets',
        type: comp.type,
        x: 0,
        y: 0,
        width: 200,
        height: 100,
        properties: comp.properties || {}
    }))

    // 加载布局配置
    if (config.layout) {
        pageSettings.value = {
            width: config.layout.width,
            height: config.layout.height,
            backgroundColor: config.layout.backgroundColor,
            title: config.layout.seo.title,
            description: config.layout.seo.description,
            keywords: config.layout.seo.keywords,
            viewport: config.layout.seo.viewport
        }
    }

    // 加载主题配置
    if (config.theme) {
        pageHeader.value = {
            title: config.theme.header.title,
            backgroundColor: config.theme.header.backgroundColor,
            textColor: config.theme.header.textColor,
            height: config.theme.header.height,
            fontSize: config.theme.header.fontSize,
            fontWeight: config.theme.header.fontWeight
        }

        pageFooter.value = {
            contactName: config.theme.footer.contact.name,
            contactPhone: config.theme.footer.contact.phone,
            address: config.theme.footer.contact.address,
            copyrightInfo: config.theme.footer.copyright,
            licenseInfo: config.theme.footer.license,
            backgroundColor: config.theme.footer.backgroundColor,
            textColor: config.theme.footer.textColor
        }
    }
}

// 工具栏操作
const handleSave = async () => {
    try {
        // 构建完整的页面配置
        const pageConfig = buildPageConfig()

        // 先保存到本地存储作为备份
        localPageService.saveToLocal(pageConfig)

        // 尝试保存到服务器
        try {
            const request: SavePageRequest = {
                pageConfig,
                baseId: props.baseId || '',
                userId: 'current-user' // 这里应该从用户状态获取
            }

            const response = await pageService.savePage(request)

            if (response.success) {
                ElMessage({
                    type: 'success',
                    message: '页面配置已成功保存到服务器',
                    duration: 3000
                })
                console.log('页面配置已保存到服务器:', response.data)
            } else {
                throw new Error(response.error || '保存失败')
            }
        } catch (apiError) {
            console.warn('服务器保存失败，已保存到本地:', apiError)
            ElMessage({
                type: 'warning',
                message: '服务器保存失败，已保存到本地存储',
                duration: 3000
            })
        }

        console.log('页面配置已保存:', pageConfig)
    } catch (error) {
        console.error('保存页面配置失败:', error)
        ElMessage({
            type: 'error',
            message: '保存页面配置失败',
            duration: 3000
        })
    }
}

const handlePreview = () => {
    // 切换全屏预览模式
    isFullscreenPreview.value = !isFullscreenPreview.value

    // 如果进入全屏预览，清除所有选中状态
    if (isFullscreenPreview.value) {
        editorState.selectedComponent = null
        isHeaderSelected.value = false
        isFooterSelected.value = false

        // 请求全屏API
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('无法进入全屏模式:', err)
            })
        }
    } else {
        // 退出全屏
        if (document.fullscreenElement && document.exitFullscreen) {
            document.exitFullscreen().catch(err => {
                console.warn('无法退出全屏模式:', err)
            })
        }
    }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
    if (!document.fullscreenElement) {
        isFullscreenPreview.value = false
    }
}

// 退出全屏预览
const handleExitFullscreenPreview = () => {
    isFullscreenPreview.value = false
    if (document.fullscreenElement && document.exitFullscreen) {
        document.exitFullscreen().catch(err => {
            console.warn('无法退出全屏模式:', err)
        })
    }
}

const handleExport = async () => {
    try {
        // 构建完整的页面配置
        const pageConfig = buildPageConfig()

        // 导出为JSON文件
        localPageService.exportToFile(pageConfig)

        ElMessage({
            type: 'success',
            message: '页面配置已导出为JSON文件',
            duration: 3000
        })
    } catch (error) {
        console.error('导出页面配置失败:', error)
        ElMessage({
            type: 'error',
            message: '导出页面配置失败',
            duration: 3000
        })
    }
}

const handleImport = async () => {
    // 创建文件输入元素
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = async (event) => {
        const file = (event.target as HTMLInputElement).files?.[0]
        if (file) {
            try {
                const reader = new FileReader()
                reader.onload = async (e) => {
                    try {
                        const config = JSON.parse(e.target?.result as string) as PageConfig

                        // 验证配置格式
                        if (!config.components || !config.layout || !config.theme) {
                            throw new Error('无效的页面配置文件格式')
                        }

                        // 加载配置到编辑器
                        canvasComponents.value = config.components.map(comp => ({
                            id: comp.id,
                            name: comp.type,
                            icon: 'material-symbols:widgets',
                            type: comp.type,
                            x: 0,
                            y: 0,
                            width: 200,
                            height: 100,
                            properties: comp.properties || {}
                        }))

                        // 加载布局配置
                        if (config.layout) {
                            pageSettings.value = {
                                width: config.layout.width,
                                height: config.layout.height,
                                backgroundColor: config.layout.backgroundColor,
                                title: config.layout.seo.title,
                                description: config.layout.seo.description,
                                keywords: config.layout.seo.keywords,
                                viewport: config.layout.seo.viewport
                            }
                        }

                        // 加载主题配置
                        if (config.theme) {
                            pageHeader.value = {
                                title: config.theme.header.title,
                                backgroundColor: config.theme.header.backgroundColor,
                                textColor: config.theme.header.textColor,
                                height: config.theme.header.height,
                                fontSize: config.theme.header.fontSize,
                                fontWeight: config.theme.header.fontWeight
                            }

                            pageFooter.value = {
                                contactName: config.theme.footer.contact.name,
                                contactPhone: config.theme.footer.contact.phone,
                                address: config.theme.footer.contact.address,
                                copyrightInfo: config.theme.footer.copyright,
                                licenseInfo: config.theme.footer.license,
                                backgroundColor: config.theme.footer.backgroundColor,
                                textColor: config.theme.footer.textColor
                            }
                        }

                        // 保存到本地存储
                        localPageService.saveToLocal(config)

                        ElMessage({
                            type: 'success',
                            message: '页面配置已成功导入',
                            duration: 3000
                        })

                        console.log('页面配置已导入:', config)
                    } catch (error) {
                        console.error('导入配置失败:', error)
                        ElMessage({
                            type: 'error',
                            message: '导入配置失败：' + (error as Error).message,
                            duration: 3000
                        })
                    }
                }
                reader.readAsText(file)
            } catch (error) {
                console.error('读取文件失败:', error)
                ElMessage({
                    type: 'error',
                    message: '读取文件失败',
                    duration: 3000
                })
            }
        }
    }
    input.click()
}

const handleDeviceChange = (device: 'desktop' | 'tablet' | 'mobile') => {
    editorState.deviceType = device
}

const handleZoomChange = (zoom: number) => {
    editorState.zoom = zoom
}

const handleGoBack = () => {
    router.back()
}

// 组件操作
const handleComponentDrag = (component: ComponentItem) => {
    console.log('开始拖拽组件:', component)
}

const handleComponentDrop = (component: ComponentItem, event: DragEvent) => {
    console.log('组件放置到画布:', component)

    // 检查是否为业务组件且已存在
    if (businessComponentTypes.includes(component.id) && existingBusinessComponents.value.includes(component.id)) {
        // 显示提示信息
        ElMessage({
            message: `业务组件"${component.name}"只能添加一次，画布上已存在该组件`,
            type: 'warning',
            duration: 3000
        })
        return
    }

    // 计算放置位置
    const canvas = event.currentTarget as HTMLElement
    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // 获取组件的默认属性
    const defaultProperties = getDefaultProperties(component.id)

    // 创建新的画布组件
    const newComponent: CanvasComponent = {
        id: `component_${Date.now()}`,
        name: component.name,
        icon: component.icon,
        type: component.id,
        x: Math.round(x),
        y: Math.round(y),
        width: 200,
        height: 100,
        properties: defaultProperties
    }

    // 添加到画布
    canvasComponents.value.push(newComponent)

    // 选中新添加的组件
    editorState.selectedComponent = newComponent
}

const handleComponentSelect = (component: CanvasComponent | null) => {
    console.log('handleComponentSelect 被调用:', component)

    // 清除Header和Footer选中状态
    isHeaderSelected.value = false
    isFooterSelected.value = false

    editorState.selectedComponent = component
    // 更新属性面板数据
    if (component && component.properties) {
        componentProperties.value = component.properties
    } else {
        componentProperties.value = {}
    }

    console.log('当前状态:', {
        selectedComponent: editorState.selectedComponent,
        isHeaderSelected: isHeaderSelected.value,
        isFooterSelected: isFooterSelected.value
    })
}

const handleComponentDelete = (component: CanvasComponent) => {
    const index = canvasComponents.value.findIndex(c => c.id === component.id)
    if (index > -1) {
        canvasComponents.value.splice(index, 1)
        if (editorState.selectedComponent?.id === component.id) {
            editorState.selectedComponent = null
        }
    }
}

const handleComponentReorder = (fromIndex: number, toIndex: number) => {
    console.log('收到重新排序事件:', fromIndex, '->', toIndex)

    // Vue.Draggable 已经自动处理了数组重新排序
    // 这里只需要记录日志，数组已经被 Vue.Draggable 更新了
    console.log('组件重新排序完成:', fromIndex, '->', toIndex)
    console.log('当前组件数组:', canvasComponents.value.map(c => c.name))
}



const handleToggleGrid = () => {
    editorState.showGrid = !editorState.showGrid
}

const handleClearCanvas = () => {
    canvasComponents.value = []
    editorState.selectedComponent = null
}

const handlePropertyChange = (properties: Record<string, unknown>) => {
    if (editorState.selectedComponent) {
        editorState.selectedComponent.properties = properties
        // 更新属性面板数据
        componentProperties.value = properties
    }
}

const handlePageSettingsChange = (settings: { title: string; description: string; keywords: string; viewport: string } | undefined) => {
    if (settings) {
        pageSettings.value = { ...pageSettings.value, ...settings }
        console.log('页面设置已更新:', settings)
    }
}

const handleHeaderSettingsChange = (settings: {
    title?: string
    backgroundColor?: string
    textColor?: string
    height?: string
    fontSize?: string
    fontWeight?: string
} | undefined) => {
    if (settings) {
        pageHeader.value = { ...pageHeader.value, ...settings }
        console.log('Header设置已更新:', settings)
    }
}

const handleFooterSettingsChange = (settings: {
    contactName?: string
    contactPhone?: string
    address?: string
    copyrightInfo?: string
    licenseInfo?: string
    backgroundColor?: string
    textColor?: string
} | undefined) => {
    if (settings) {
        pageFooter.value = { ...pageFooter.value, ...settings }
        console.log('Footer设置已更新:', settings)
    }
}

const handleHeaderSelect = () => {
    console.log('handleHeaderSelect 被调用')
    isHeaderSelected.value = true
    isFooterSelected.value = false
    editorState.selectedComponent = null
    console.log('Header被选中，当前状态:', {
        selectedComponent: editorState.selectedComponent,
        isHeaderSelected: isHeaderSelected.value,
        isFooterSelected: isFooterSelected.value
    })
}

const handleFooterSelect = () => {
    isHeaderSelected.value = false
    isFooterSelected.value = true
    editorState.selectedComponent = null
    console.log('Footer被选中')
}

const handleTogglePageSettings = () => {
    // 清除选中状态，显示页面设置
    editorState.selectedComponent = null
    isHeaderSelected.value = false
    isFooterSelected.value = false
    console.log('切换到页面设置')
}

// 初始化函数
const initializeEditor = async () => {
    if (props.baseId) {
        try {
            // 从API获取基地列表
            const response = await listAllBaseInfo()
            if (response.code === '200') {
                currentBase.value = response.data.find(base => base.id?.toString() === props.baseId) || null
                if (currentBase.value) {
                    console.log('正在为基地配置页面:', currentBase.value.baseName)

                    // 同步基地名称到页面标题和Header标题
                    const baseName = currentBase.value.baseName
                    pageSettings.value.title = baseName
                    pageHeader.value.title = baseName

                    // 尝试从服务器加载配置
                    try {
                        const response = await pageService.loadPage({
                            pageId: props.baseId,
                            baseId: props.baseId
                        })

                        if (response.success && response.data) {
                            const config = response.data
                            loadPageConfig(config)
                            console.log('已从服务器加载页面配置:', config)
                            return
                        }
                    } catch (error) {
                        console.warn('从服务器加载配置失败，尝试本地加载:', error)
                    }

                    // 尝试从本地存储加载配置
                    const localConfig = localPageService.loadFromLocal(props.baseId)
                    if (localConfig) {
                        loadPageConfig(localConfig)
                        console.log('已从本地存储加载页面配置:', localConfig)
                    } else {
                        // 如果没有保存的配置，添加一些演示组件
                        // addDemoComponents()
                    }
                }
            }
        } catch (error) {
            console.error('获取基地信息失败:', error)
            ElMessage.error('获取基地信息失败，请检查网络连接')
        }
    }
}

// // 添加演示组件
// const addDemoComponents = () => {
//     const demoComponents: CanvasComponent[] = [
//         {
//             id: 'demo_text_1',
//             name: '标题文本',
//             icon: 'material-symbols:text-fields',
//             type: 'text',
//             x: 100,
//             y: 100,
//             width: 200,
//             height: 100,
//             properties: {
//                 text: '这是一个标题文本示例',
//                 textStyle: 'heading',
//                 textAlign: 'center',
//                 color: '#333333',
//                 maxWidth: '800px',
//                 spacing: 'normal'
//             }
//         },
//         {
//             id: 'demo_text_2',
//             name: '正文文本',
//             icon: 'material-symbols:text-fields',
//             type: 'text',
//             x: 100,
//             y: 200,
//             width: 200,
//             height: 100,
//             properties: {
//                 text: '这是一段正文文本，展示了升级后的文本组件功能。支持多种文本样式、对齐方式和间距设置。',
//                 textStyle: 'body',
//                 textAlign: 'left',
//                 color: '#666666',
//                 maxWidth: '600px',
//                 spacing: 'normal'
//             }
//         },
//         {
//             id: 'demo_image_1',
//             name: '图片组件',
//             icon: 'material-symbols:image',
//             type: 'image',
//             x: 400,
//             y: 100,
//             width: 300,
//             height: 200,
//             properties: {
//                 src: '',
//                 alt: '演示图片',
//                 imageStyle: 'rounded',
//                 imageSize: 'medium',
//                 objectFit: 'cover',
//                 showUpload: true,
//                 uploadText: '点击上传图片'
//             }
//         },
//         {
//             id: 'demo_button_1',
//             name: '主要按钮',
//             icon: 'material-symbols:smart-button',
//             type: 'button',
//             x: 100,
//             y: 500,
//             width: 150,
//             height: 40,
//             properties: {
//                 text: '主要按钮',
//                 href: '',
//                 target: '_self',
//                 buttonStyle: 'primary',
//                 buttonSize: 'medium',
//                 buttonShape: 'default',
//                 hoverEffect: 'scale',
//                 disabled: false,
//                 loading: false
//             }
//         }
//     ]

//     canvasComponents.value = demoComponents
//     console.log('已添加演示组件')
// }

// 组件挂载时初始化
onMounted(async () => {
    await initializeEditor()

    // 添加全屏状态监听
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('keydown', (event) => {
        // ESC键退出全屏预览
        if (event.key === 'Escape' && isFullscreenPreview.value) {
            handleExitFullscreenPreview()
        }
    })
})

// 组件卸载时清理监听器
onUnmounted(() => {
    document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<template>
    <div class="lowcode-editor" :class="{ 'fullscreen-preview': isFullscreenPreview }">
        <!-- 全屏预览模式 -->
        <div v-if="isFullscreenPreview" class="fullscreen-preview-container">
            <!-- 全屏预览工具栏 -->
            <div class="fullscreen-toolbar">
                <div class="toolbar-left">
                    <h3 class="preview-title">{{ pageHeader.title }}</h3>
                    <span class="preview-badge">预览模式</span>
                </div>
                <div class="toolbar-right">
                    <el-button @click="handleExitFullscreenPreview" type="primary" size="small">
                        <Icon icon="material-symbols:delete" style="width: 16px; height: 16px;" />
                        退出预览 (ESC)
                    </el-button>
                </div>
            </div>

            <!-- 全屏预览内容 -->
            <div class="fullscreen-preview-content">
                <div class="preview-page" :style="{
                    backgroundColor: pageSettings.backgroundColor,
                    maxWidth: editorState.deviceType === 'mobile' ? '375px' :
                        editorState.deviceType === 'tablet' ? '768px' : '100%'
                }">
                    <!-- 页眉 -->
                    <div class="preview-header" v-if="pageHeader">
                        <component :is="widgetMap.defaultHeader" v-bind="pageHeader" />
                    </div>

                    <!-- 页面内容 -->
                    <div class="preview-components">
                        <div v-if="canvasComponents.length === 0" class="preview-empty">
                            <p>页面暂无内容</p>
                        </div>
                        <div v-else class="components-list">
                            <div v-for="component in canvasComponents" :key="component.id" class="preview-component">
                                <component :is="widgetMap[component.type] || 'div'" v-bind="component.properties || {}"
                                    v-if="widgetMap[component.type]" />
                            </div>
                        </div>
                    </div>

                    <!-- 页脚 -->
                    <div class="preview-footer">
                        <component :is="widgetMap.footer" v-bind="pageFooter" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑模式 -->
        <template v-else>
            <!-- 顶部工具栏 -->
            <EditorToolbar :current-base="currentBase" :device-type="editorState.deviceType" :zoom="editorState.zoom"
                :preview-mode="isFullscreenPreview" @go-back="handleGoBack" @device-change="handleDeviceChange"
                @zoom-change="handleZoomChange" @preview-toggle="handlePreview" @save="handleSave"
                @export="handleExport" @import="handleImport" @toggle-page-settings="handleTogglePageSettings" />

            <!-- 编辑器主体 -->
            <div class="editor-body">
                <!-- 左侧组件库 -->
                <ComponentLibrary :component-library="componentLibrary" :preview-mode="isFullscreenPreview"
                    :existing-business-components="existingBusinessComponents" @component-drag="handleComponentDrag" />

                <!-- 中间画布区域 -->
                <DesignCanvas :canvas-components="canvasComponents" :selected-component="editorState.selectedComponent"
                    :preview-mode="isFullscreenPreview" :show-grid="editorState.showGrid"
                    :device-type="editorState.deviceType" :zoom="editorState.zoom" :page-settings="pageSettings"
                    :page-header="pageHeader" :page-footer="pageFooter" :is-header-selected="isHeaderSelected"
                    :is-footer-selected="isFooterSelected" @component-drop="handleComponentDrop"
                    @component-select="handleComponentSelect" @component-delete="handleComponentDelete"
                    @component-reorder="handleComponentReorder" @toggle-grid="handleToggleGrid"
                    @clear-canvas="handleClearCanvas" @header-select="handleHeaderSelect"
                    @footer-select="handleFooterSelect" />

                <!-- 右侧面板区域 -->
                <div class="right-panels">
                    <!-- 属性面板 -->
                    <PropertiesPanel :selected-component="editorState.selectedComponent"
                        :component-properties="componentProperties" :page-settings="pageSettings"
                        :is-header-selected="isHeaderSelected" :header-settings="pageHeader"
                        :is-footer-selected="isFooterSelected" :footer-settings="pageFooter"
                        @property-change="handlePropertyChange" @page-settings-change="handlePageSettingsChange"
                        @header-settings-change="handleHeaderSettingsChange"
                        @footer-settings-change="handleFooterSettingsChange" />
                </div>
            </div>
        </template>
    </div>
</template>

<style scoped>
.lowcode-editor {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;
    overflow: hidden;
}

.editor-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.right-panels {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 全屏预览样式 */
.lowcode-editor.fullscreen-preview {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000;
    z-index: 9999;
}

.fullscreen-preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;
}

.fullscreen-toolbar {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.preview-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.preview-badge {
    background: #409eff;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.fullscreen-preview-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #f5f7fa;
    display: flex;
    justify-content: center;
}

.preview-page {
    width: 100%;
    /* background: white; */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    max-width: 1200px;
}

.preview-header {
    flex-shrink: 0;
}

.preview-components {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.preview-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #909399;
    font-size: 16px;
}

.components-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.preview-component {
    width: 100%;
}

.preview-footer {
    flex-shrink: 0;
    /* 页脚自然跟随内容，不固定在底部 */
    margin-top: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .fullscreen-toolbar {
        padding: 8px 16px;
    }

    .preview-title {
        font-size: 16px;
    }

    .fullscreen-preview-content {
        padding: 10px;
    }

    .preview-page {
        border-radius: 4px;
    }
}

/* 全屏预览滚动条样式 */
.fullscreen-preview-content {
    /* 确保在所有浏览器中显示滚动条 */
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.3) rgba(0, 0, 0, 0.1);
}

.fullscreen-preview-content::-webkit-scrollbar {
    width: 8px;
}

.fullscreen-preview-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.fullscreen-preview-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.fullscreen-preview-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.4);
}

.fullscreen-preview-content::-webkit-scrollbar-thumb:active {
    background: rgba(0, 0, 0, 0.6);
}
</style>