---
description: '前端开发总览与核心规则 (Vue 3, Tailwind, Element Plus, Iconify)'
globs:
  # 这个规则将应用于所有前端相关的文件
  - '../src/**/*.{ts,js,vue}'
  - '../package.json'
  - '../vite.config.ts'
  - '../tailwind.config.js'
  - '../postcss.config.js'
  - '../index.html'
alwaysApply: true
---

# 前端开发总览与核心规则

## 1. 项目目标与技术栈

### 项目目标

构建一个高质量、稳定且现代化的Web应用，名为“江西省红色教育实践基地地图平台”。应用需体现政府项目的庄重、严谨与大学教育的文化底蕴，同时兼具现代化的设计美感。

### 前端技术栈

- **核心框架**: Vue 3 (严格使用组合式API, `<script setup>`)
- **UI布局与样式**: Tailwind CSS (作为主要布局和原子化CSS工具)
- **UI组件库**: Element Plus (作为基础组件库，其样式将与Tailwind协同工作)
- **图标库**: Iconify (通过 `@iconify/vue` 按需加载)
- **状态管理**: Pinia
- **HTTP请求**: Axios
- **地图服务**: 国家地理信息天地图JavaScript API 4.0

---

## 2. 项目主题与视觉规范 (Style Guide)

这是项目的核心视觉DNA，所有开发都应遵循此规范。

### 色彩体系 (Color Palette)

- **主色调 (中国红)**: `primary-red: #CE2E2D` - 用于核心操作按钮、导航高亮、重要提示等。代表庄重、热情与红色主题。
- **辅助色 (琉璃金)**: `accent-gold: #D4A66A` - 用于点缀、徽标、特殊荣誉或高亮边框。增添文化感与精致感。
- **辅助色 (青墨蓝)**: `accent-blue: #2E4C6D` - 用于次要信息、非关键按钮、页脚背景等。体现政府项目的沉稳与信赖感。
- **文本色**:
  - `text-primary: #1F2937` (近黑色) - 用于正文、标题。
  - `text-secondary: #6B7280` (中灰色) - 用于次要描述、提示文字。
- **背景与中性色**:
  - `bg-page: #F9FAFB` (极浅灰色) - 页面主背景。
  - `bg-container: #FFFFFF` (白色) - 卡片、容器背景。
  - `border-color: #E5E7EB` (浅灰色) - 边框、分割线。

### 字体规范 (Typography)

- **标题字体**: `font-serif` - 使用思源宋体 (`Source Han Serif CN`)。赋予页面传统、典雅的文化气息。
- **正文字体**: `font-sans` - 使用思源黑体 (`Source Han Sans CN`)。保证内容在屏幕上的高可读性。
- **配置**: 在 `tailwind.config.js` 中配置 `fontFamily` 以便全局调用。

### 布局与间距

- **一致性**: 严格使用 Tailwind 的间距工具 (`p-4`, `m-8`, `gap-2`)。**禁止**使用任意的硬编码值（如 `margin: 13px`）。
- **圆角**: 使用中等程度的圆角 (`rounded-lg`) 营造现代感，避免过度圆润或完全直角。

---

## 3. UI与工具使用规范

### Tailwind CSS 使用规范

- **Utility-First**: 优先直接在 `<template>` 中使用原子化 class 来构建布局和样式。
- **组件抽象**: 当一组 class 被频繁复用时（如自定义按钮），在 `<style scoped>` 中使用 `@apply` 将其抽象成一个 class。
  ```css
  .btn-primary {
    @apply bg-primary-red text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105;
  }
  ```
- **配置驱动**: 所有项目特有的颜色、字体、间距等，**必须**在 `tailwind.config.js` 的 `theme.extend` 中进行定义，而不是在代码中硬编码。

### Iconify 图标库使用规范

- **核心原则**: **按需加载，性能至上**。绝不全局引入整个图标库。
- **实现方式**: 必须使用 `@iconify/vue` 组件。
  ```vue
  <script setup>
  import { Icon } from '@iconify/vue'
  </script>
  <template>
    <Icon icon="mdi:school" />
  </template>
  ```
- **推荐图标集**:
  - **`mdi` (Material Design Icons)**: 首选图标集。风格专业、全面，非常适合政府和教育类项目。
  - **`fluent` (Microsoft Fluent UI System Icons)**: 风格现代、简洁，可作为补充。
  - **`heroicons` (by Tailwind Labs)**: 与Tailwind风格完美契合，适合极简场景。
- **主题相关图标示例**:
  - **通用**: `mdi:map-marker` (地标), `mdi:magnify` (搜索), `mdi:arrow-left` (返回)
  - **教育**: `mdi:school` (学校), `mdi:book-open-variant` (书籍/学习), `mdi:account-group` (师生)
  - **政府/庄重**: `mdi:gavel` (权威/法律), `mdi:flag` (旗帜/国家), `mdi:shield-check` (安全/认证)
  - **文化/历史**: `mdi:temple-buddhist` (地标建筑), `mdi:timeline-text` (历史)

---

## 4. 其他核心规范 (延续)

### Vue组件开发规范

- **结构**: 遵循 `<template>`, `<script setup>`, `<style scoped>` 的顺序。
- **路由**: 所有页面级组件**必须**进行懒加载。
- 不要自主添加开发文档，或者创建测试文件
- 不是必须就不要使用内联样式
- 遵循组件化，模块化开发，不要集中在一个页面开发；
- 父子组件，兄弟组件交互

### API交互 (Axios)

- 所有API调用必须集中在 `../src/api/` 目录下的服务模块中。

### 天地图集成

- **加载与初始化**: **必须**通过返回Promise的工具函数异步加载，并在 `onMounted` 钩子中初始化。
- **AK管理**: **必须**使用环境变量 `VITE_BAIDU_MAP_AK`。
