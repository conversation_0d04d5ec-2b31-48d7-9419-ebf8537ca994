<!-- src/components/editor/widgets/InfoGridWidget.vue -->
<template>
  <div
    class="key-info-container"
    :class="{ rounded: borderRadius === 'rounded', square: borderRadius === 'square' }"
  >
    <div class="key-info-grid">
      <div
        v-for="(item, index) in props.infoItems"
        :key="index"
        class="info-item"
        :class="{ rounded: borderRadius === 'rounded', square: borderRadius === 'square' }"
        :style="{ '--primary-color': primaryColor, '--primary-color-light': primaryColorLight }"
      >
        <span
          class="info-label"
          :class="{ rounded: borderRadius === 'rounded', square: borderRadius === 'square' }"
          >{{ item.label }}</span
        >
        <span
          class="info-value"
          :class="{ rounded: borderRadius === 'rounded', square: borderRadius === 'square' }"
          >{{ item.value }}</span
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义组件属性
interface Props {
  infoItems?: Array<{ label: string; value: string }>
  primaryColor?: string
  borderRadius?: 'rounded' | 'square'
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  infoItems: () => [
    { label: '基地地址', value: '请输入地址' },
    { label: '联系人', value: '请输入姓名' },
    { label: '联系电话', value: '请输入电话' },
    { label: '开放时间', value: '请输入时间' },
  ],
  primaryColor: '#cc0000',
  borderRadius: 'rounded',
})

// 计算浅色版本的主色调
const primaryColorLight = computed(() => {
  const color = props.primaryColor
  // 简单的颜色亮化处理
  if (color.startsWith('#')) {
    const hex = color.slice(1)
    const r = parseInt(hex.slice(0, 2), 16)
    const g = parseInt(hex.slice(2, 4), 16)
    const b = parseInt(hex.slice(4, 6), 16)

    // 增加亮度
    const newR = Math.min(255, Math.floor(r + (255 - r) * 0.3))
    const newG = Math.min(255, Math.floor(g + (255 - g) * 0.3))
    const newB = Math.min(255, Math.floor(b + (255 - b) * 0.3))

    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`
  }
  return '#cc0000'
})
</script>

<style scoped>
.key-info-container {
  padding: 24px;
  box-sizing: border-box;
  width: 100%;
}

/* 圆角样式 */
.key-info-container.rounded {
  border-radius: 12px;
}

/* 直角样式 */
.key-info-container.square {
  border-radius: 0;
}

.key-info-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 16px;
  width: 100%;
}

.info-item {
  flex: 1 1 45%;
  min-width: 280px;
  max-width: 48%;
  display: flex;
  overflow: hidden;
  font-size: 14px;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 圆角样式 */
.info-item.rounded {
  border-radius: 8px;
}

/* 直角样式 */
.info-item.square {
  border-radius: 0;
}

.info-item:hover {
  border-color: var(--primary-color, #cc0000);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.info-label {
  background: linear-gradient(
    135deg,
    var(--primary-color, #cc0000) 0%,
    var(--primary-color-light, #cc0000) 100%
  );
  color: white;
  padding: 16px 20px;
  font-weight: 600;
  white-space: nowrap;
  min-width: 120px;
  display: flex;
  align-items: center;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 圆角样式的标签 */
.info-label.rounded {
  border-radius: 8px 0 0 8px;
}

/* 直角样式的标签 */
.info-label.square {
  border-radius: 0;
}

.info-label::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 60%;
  background: rgba(255, 255, 255, 0.2);
}

.info-value {
  background: #e9ecef;
  color: #333;
  padding: 16px 20px;
  flex: 1;
  display: flex;
  align-items: center;
  line-height: 1.5;
  word-break: break-word;
  font-size: 14px;
  position: relative;
  border-left: 1px solid rgba(0, 0, 0, 0.06);
}

/* 圆角样式的值 */
.info-value.rounded {
  border-radius: 0 8px 8px 0;
}

/* 直角样式的值 */
.info-value.square {
  border-radius: 0;
}

/* 平板端适配 */
@media (max-width: 768px) {
  .key-info-container {
    padding: 16px;
  }

  .key-info-grid {
    gap: 12px;
  }

  .info-item {
    flex: 1 1 100%;
    min-width: 0;
    max-width: 100%;
  }

  .info-label {
    min-width: 80px;
    padding: 12px 16px;
    font-size: 13px;
  }

  .info-value {
    padding: 12px 16px;
    font-size: 13px;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .key-info-container {
    padding: 12px;
  }

  .key-info-grid {
    gap: 8px;
  }

  .info-item {
    flex-direction: column;
  }

  .info-item.rounded {
    border-radius: 6px;
  }

  .info-item.square {
    border-radius: 0;
  }

  .info-label {
    min-width: 0;
    width: 100%;
    padding: 10px 12px;
    font-size: 12px;
    justify-content: center;
  }

  .info-label.rounded {
    border-radius: 6px 6px 0 0;
  }

  .info-label.square {
    border-radius: 0;
  }

  .info-value {
    padding: 10px 12px;
    font-size: 12px;
    min-height: 40px;
  }

  .info-value.rounded {
    border-radius: 0 0 6px 6px;
  }

  .info-value.square {
    border-radius: 0;
  }
}

/* 超小屏幕适配 */
@media (max-width: 320px) {
  .key-info-container {
    padding: 8px;
  }

  .key-info-grid {
    gap: 6px;
  }

  .info-label {
    padding: 8px 10px;
    font-size: 11px;
  }

  .info-value {
    padding: 8px 10px;
    font-size: 11px;
    min-height: 36px;
  }
}
</style>
