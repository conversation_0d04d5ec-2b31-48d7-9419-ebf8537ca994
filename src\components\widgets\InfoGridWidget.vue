<!-- src/components/editor/widgets/InfoGridWidget.vue -->
<template>
  <div class="key-info-container">
    <div class="key-info-grid">
      <div v-for="(item, index) in props.infoItems" :key="index" class="info-item">
        <span class="info-label">{{ item.label }}</span>
        <span class="info-value">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义组件属性
interface Props {
  infoItems?: Array<{ label: string; value: string }>
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  infoItems: () => [
    { label: '基地地址', value: '请输入地址' },
    { label: '联系人', value: '请输入姓名' },
    { label: '联系电话', value: '请输入电话' },
    { label: '开放时间', value: '请输入时间' },
  ]
})
</script>

<style scoped>
.key-info-container {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.key-info-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 16px;
  width: 100%;
}

.info-item {
  flex: 1 1 45%;
  min-width: 280px;
  max-width: 48%;
  display: flex;
  border: 1px solid #E0E0E0;
  border-radius: 12px;
  overflow: hidden;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06), 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: #ffffff;
}

.info-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: rgba(153, 0, 0, 0.3);
}

.info-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
  border-radius: 12px;
}

.info-label {
  background: linear-gradient(135deg, #990000 0%, #cc0000 100%);
  color: white;
  padding: 16px 20px;
  font-weight: 600;
  white-space: nowrap;
  min-width: 120px;
  display: flex;
  align-items: center;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.info-label::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 60%;
  background: rgba(255, 255, 255, 0.2);
}

.info-value {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  color: #333;
  padding: 16px 20px;
  flex: 1;
  display: flex;
  align-items: center;
  line-height: 1.5;
  word-break: break-word;
  font-size: 14px;
  position: relative;
  border-left: 1px solid rgba(0, 0, 0, 0.06);
}

/* 平板端适配 */
@media (max-width: 768px) {
  .key-info-container {
    padding: 16px;
  }

  .key-info-grid {
    gap: 12px;
  }

  .info-item {
    flex: 1 1 100%;
    min-width: 0;
    max-width: 100%;
  }

  .info-label {
    min-width: 80px;
    padding: 10px 12px;
    font-size: 12px;
  }

  .info-value {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .key-info-container {
    padding: 12px;
    border-radius: 6px;
  }

  .key-info-grid {
    gap: 8px;
  }

  .info-item {
    flex-direction: column;
    border-radius: 6px;
  }

  .info-label {
    min-width: 0;
    width: 100%;
    padding: 8px 12px;
    font-size: 12px;
    justify-content: center;
    border-radius: 6px 6px 0 0;
  }

  .info-value {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 0 0 6px 6px;
    min-height: 40px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 320px) {
  .key-info-container {
    padding: 8px;
  }

  .key-info-grid {
    gap: 6px;
  }

  .info-label {
    padding: 6px 8px;
    font-size: 11px;
  }

  .info-value {
    padding: 6px 8px;
    font-size: 11px;
    min-height: 36px;
  }
}
</style>