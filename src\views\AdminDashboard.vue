<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Edit, Delete, Search, ArrowLeft, User, Setting, UploadFilled, View, Close } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { mockBases } from '../data/mockData'
import type { PracticeBase } from '../types'
import { useUserStore } from '../stores/user'

const router = useRouter()
const userStore = useUserStore()
const tableData = ref<PracticeBase[]>([])
const searchQuery = ref('')
const loading = ref(false)
const dialogVisible = ref(false)
const editingBase = ref<PracticeBase | null>(null)
const baseForm = ref({
  name: '',
  city: '',
  category: '',
  address: '',
  description: '',
  phone: '',
  openHours: '',
  imageUrl: '',
  features: [] as string[],
  rating: 5.0
})

// 上传相关
const uploadRef = ref()
const uploadLoading = ref(false)
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

const cities = ['南昌市', '井冈山市', '瑞金市', '九江市', '萍乡市', '新余市', '鹰潭市', '赣州市', '宜春市', '上饶市', '吉安市', '抚州市']
const categories = ['革命历史', '爱国主义', '红色文化', '党史教育', '英烈纪念']

const checkAuth = () => {
  const token = localStorage.getItem('admin_token')
  if (!token) {
    ElMessage.error('请先登录')
    router.push('/login')
    return false
  }
  return true
}

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logout()
    router.push('/login')
  })
}

const goBack = () => {
  // 跳转到主项目首页
  window.location.href = '/'
}

const loadData = () => {
  loading.value = true
  setTimeout(() => {
    tableData.value = [...mockBases]
    loading.value = false
  }, 500)
}

const handleAdd = () => {
  editingBase.value = null
  baseForm.value = {
    name: '',
    city: '',
    category: '',
    address: '',
    description: '',
    phone: '',
    openHours: '',
    imageUrl: '',
    features: [],
    rating: 5.0
  }
  dialogVisible.value = true
}

const handleEdit = (base: PracticeBase) => {
  editingBase.value = base
  baseForm.value = {
    name: base.name,
    city: base.city,
    category: base.category,
    address: base.address,
    description: base.description,
    phone: base.phone || '',
    openHours: base.openHours || '',
    imageUrl: base.imageUrl,
    features: base.features || [],
    rating: base.rating
  }
  dialogVisible.value = true
}

const handleDelete = (base: PracticeBase) => {
  ElMessageBox.confirm(`确定要删除"${base.name}"吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === base.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  })
}

const handleSave = () => {
  if (!baseForm.value.name || !baseForm.value.city || !baseForm.value.address) {
    ElMessage.error('请填写必要信息')
    return
  }

  if (editingBase.value) {
    // Update existing
    const index = tableData.value.findIndex(item => item.id === editingBase.value!.id)
    if (index > -1) {
      tableData.value[index] = {
        ...baseForm.value,
        id: editingBase.value.id,
        coordinates: editingBase.value.coordinates
      }
    }
    ElMessage.success('更新成功')
  } else {
    // Add new
    const newBase: PracticeBase = {
      ...baseForm.value,
      id: Date.now().toString(),
      coordinates: { lat: 28.682, lng: 115.858 } // Default coordinates
    }
    tableData.value.push(newBase)
    ElMessage.success('添加成功')
  }

  dialogVisible.value = false
}

const handlePageConfig = (base: PracticeBase) => {
  ElMessage.info(`正在进入 "${base.name}" 的页面配置编辑器`)
  router.push(`/lowcode-editor/${base.id}`)
}

// 上传处理函数
const handleUploadSuccess = (response: { url?: string }) => {
  uploadLoading.value = false
  // 这里假设上传成功后会返回图片URL，您可以根据实际API调整
  baseForm.value.imageUrl = response.url || 'https://example.com/uploaded-image.jpg'
  ElMessage.success('图片上传成功')
}

const handleUploadError = () => {
  uploadLoading.value = false
  ElMessage.error('图片上传失败')
}

const handleUploadProgress = () => {
  uploadLoading.value = true
}

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 图片预览和删除处理
const handlePreviewImage = () => {
  previewImageUrl.value = baseForm.value.imageUrl
  imagePreviewVisible.value = true
}

const handleDeleteImage = () => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    baseForm.value.imageUrl = ''
    ElMessage.success('图片已删除')
  })
}

const filteredData = computed(() => {
  if (!searchQuery.value) return tableData.value
  return tableData.value.filter(base =>
    base.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    base.city.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

onMounted(async () => {
  if (checkAuth()) {
    await userStore.initUserInfo()
    loadData()
  }
})
</script>

<template>
  <div class="admin-container">
    <!-- Header -->
    <div class="admin-header">
      <div class="header-left">
        <!-- <el-button @click="goBack" circle>
          <el-icon>
            <ArrowLeft />
          </el-icon>
        </el-button> -->
        <h1>实践基地管理</h1>
      </div>
      <div class="header-right">
        <el-dropdown trigger="hover">
          <div class="user-info">
            <el-avatar :size="32" :icon="User" />
            <span class="username">{{ userStore.userName }}</span>
            <!-- <span class="username">{{ userStore.userName }}</span> -->
            <!-- <el-icon class="dropdown-icon">
              <ArrowLeft />
            </el-icon> -->
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item disabled>
                <div class="user-detail">
                  <div class="user-name">{{ userStore.userName }}</div>
                  <div class="user-phone">{{ userStore.userPhone }}</div>
                </div>
              </el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <!-- <el-icon>
                  <ArrowLeft />
                </el-icon> -->
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- Toolbar -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input v-model="searchQuery" placeholder="搜索基地名称或城市" :prefix-icon="Search" style="width: 300px" />
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon>
            <Plus />
          </el-icon>
          添加基地
        </el-button>
      </div>
    </div>

    <!-- Table -->
    <div class="table-container">
      <el-table :data="filteredData" :loading="loading" stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="基地名称" min-width="200" />
        <el-table-column prop="city" label="城市" width="100" />
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="address" label="地址" min-width="200" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="rating" label="评分" width="80">
          <template #default="{ row }">
            <el-rate v-model="row.rating" disabled text-color="#ff9900" score-template="{value}" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button @click="handleEdit(row)" type="primary" size="small">
              <el-icon>
                <Edit />
              </el-icon>
            </el-button>
            <el-button @click="handlePageConfig(row)" type="success" size="small" title="页面配置">
              <el-icon>
                <Setting />
              </el-icon>
            </el-button>
            <el-button @click="handleDelete(row)" type="danger" size="small">
              <el-icon>
                <Delete />
              </el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- Dialog -->
    <el-dialog v-model="dialogVisible" :title="editingBase ? '编辑基地' : '添加基地'" width="600px">
      <el-form :model="baseForm" label-width="100px">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="基地名称" required>
              <el-input v-model="baseForm.name" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市" required>
              <el-select v-model="baseForm.city" placeholder="请选择城市">
                <el-option v-for="city in cities" :key="city" :label="city" :value="city" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="分类">
              <el-select v-model="baseForm.category" placeholder="请选择分类">
                <el-option v-for="category in categories" :key="category" :label="category" :value="category" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话">
              <el-input v-model="baseForm.phone" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="地址" required>
          <el-input v-model="baseForm.address" />
        </el-form-item>

        <el-form-item label="开放时间">
          <el-input v-model="baseForm.openHours" placeholder="例如：周一至周日 9:00-17:00" />
        </el-form-item>

        <el-form-item label="基地图片">
          <div v-if="baseForm.imageUrl" class="image-display">
            <div class="image-container">
              <img :src="baseForm.imageUrl" alt="预览图" />
              <div class="image-overlay">
                <el-button type="primary" circle size="small" @click="handlePreviewImage" title="预览图片">
                  <el-icon>
                    <View />
                  </el-icon>
                </el-button>
                <el-button type="danger" circle size="small" @click="handleDeleteImage" title="删除图片">
                  <el-icon>
                    <Close />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>
          <el-upload v-else ref="uploadRef" class="upload-demo" drag
            action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" :before-upload="beforeUpload"
            :on-success="handleUploadSuccess" :on-error="handleUploadError" :on-progress="handleUploadProgress"
            :loading="uploadLoading" :show-file-list="false" :limit="1" accept="image/*">
            <el-icon class="el-icon--upload">
              <UploadFilled />
            </el-icon>
            <div class="el-upload__text">
              拖拽图片到此处或 <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 jpg/png 格式，文件大小不超过 2MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="详细描述">
          <el-input v-model="baseForm.description" type="textarea" :rows="4" placeholder="请输入基地的详细介绍" />
        </el-form-item>

        <el-form-item label="评分">
          <el-rate v-model="baseForm.rating" text-color="#ff9900" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">
            {{ editingBase ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="imagePreviewVisible" title="图片预览" width="600px" center>
      <div class="preview-container">
        <img :src="previewImageUrl" alt="预览图" style="max-width: 100%; max-height: 400px;" />
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.admin-container {
  min-height: 100vh;
  background: #f5f7fa;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  font-size: 12px;
  color: #909399;
  transition: transform 0.3s;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}

.user-detail {
  padding: 4px 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.user-phone {
  font-size: 12px;
  color: #909399;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #ebeef5;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 12px;
}

.table-container {
  padding: 24px;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}

.upload-demo {
  width: 100%;
}

.image-display {
  width: 100%;
  min-height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fafafa;
  transition: border-color 0.3s;
}

.image-display:hover {
  border-color: #409eff;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: filter 0.3s ease;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.image-container:hover img {
  filter: brightness(0.7);
}

.preview-container {
  text-align: center;
}

@media (max-width: 768px) {
  .admin-header {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }

  .table-container {
    padding: 12px;
  }
}
</style>
