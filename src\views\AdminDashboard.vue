<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  Plus,
  Edit,
  Delete,
  Search,
  User,
  Setting,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { PracticeBase } from '../types'
import { useUserStore } from '../stores/user'
import { listAllBaseInfo, deleteBaseInfo, addBaseInfo, updateBaseInfo } from '../api/base'
import type { BaseInfo, UpdateBaseInfoParams } from '../api/base'

const router = useRouter()
const userStore = useUserStore()
const tableData = ref<PracticeBase[]>([])
const searchQuery = ref('')
const loading = ref(false)
const dialogVisible = ref(false)
const editingBase = ref<PracticeBase | null>(null)
const baseForm = ref({
  name: '',
  city: '',
  category: '',
  address: '',
  description: '',
  phone: '',
  openHours: '',
  imageUrl: '',
  features: [] as string[],
  rating: 5.0,
})

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    userStore.logout()
    router.push('/')
  })
}

const loadData = async () => {
  loading.value = true
  try {
    const response = await listAllBaseInfo()
    if (response.code === '200') {
      // 将 BaseInfo 转换为 PracticeBase 格式
      tableData.value = response.data.data.map((base: BaseInfo) => ({
        id: base.id?.toString() || '',
        name: base.baseName,
        city: base.city,
        category: base.categoryName || '',
        address: base.address,
        description: base.baseDesc,
        phone: base.phone,
        openHours: base.openTime,
        imageUrl: base.image,
        features: [],
        rating: base.score || 5.0,
        coordinates: {
          lat: base.lat,
          lng: base.lng,
        },
      }))
    } else {
      ElMessage.error('获取基地列表失败')
    }
  } catch (error) {
    console.error('获取基地列表错误:', error)
    ElMessage.error('获取基地列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  editingBase.value = null
  baseForm.value = {
    name: '',
    city: '',
    category: '',
    address: '',
    description: '',
    phone: '',
    openHours: '',
    imageUrl: '',
    features: [],
    rating: 5.0,
  }
  dialogVisible.value = true
}

const handleEdit = (base: PracticeBase) => {
  editingBase.value = base
  baseForm.value = {
    name: base.name,
    city: base.city,
    category: base.category,
    address: base.address,
    description: base.description,
    phone: base.phone || '',
    openHours: base.openHours || '',
    imageUrl: base.imageUrl,
    features: base.features || [],
    rating: base.rating,
  }
  dialogVisible.value = true
}

const handleDelete = async (base: PracticeBase) => {
  ElMessageBox.confirm(`确定要删除"${base.name}"吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const response = await deleteBaseInfo({ id: parseInt(base.id) })
      if (response.code === '200') {
        ElMessage.success('删除成功')
        loadData() // 重新加载数据
      } else {
        ElMessage.error('删除失败')
      }
    } catch (error) {
      console.error('删除基地错误:', error)
      ElMessage.error('删除失败，请重试')
    }
  })
}

const handleSave = async () => {
  if (!baseForm.value.name || !baseForm.value.city || !baseForm.value.address) {
    ElMessage.error('请填写必要信息')
    return
  }

  try {
    if (editingBase.value) {
      // 更新现有基地
      const updateData: UpdateBaseInfoParams = {
        id: parseInt(editingBase.value.id),
        baseName: baseForm.value.name,
        city: baseForm.value.city,
        categoryName: baseForm.value.category,
        address: baseForm.value.address,
        baseDesc: baseForm.value.description,
        phone: baseForm.value.phone,
        openTime: baseForm.value.openHours,
        image: baseForm.value.imageUrl,
        score: baseForm.value.rating,
        province: '江西省',
        provinceCode: '360000',
        cityCode: '',
        district: '',
        districtCode: '',
        lat: 28.682,
        lng: 115.858,
        categoryId: 1,
        codeInfoId: 0,
      }

      const response = await updateBaseInfo(updateData)
      if (response.code === '200') {
        ElMessage.success('更新成功')
        dialogVisible.value = false
        loadData() // 重新加载数据
      } else {
        ElMessage.error('更新失败')
      }
    } else {
      // 添加新基地
      const addData: BaseInfo = {
        baseName: baseForm.value.name,
        city: baseForm.value.city,
        categoryName: baseForm.value.category,
        address: baseForm.value.address,
        baseDesc: baseForm.value.description,
        phone: baseForm.value.phone,
        openTime: baseForm.value.openHours,
        image: baseForm.value.imageUrl,
        score: baseForm.value.rating,
        province: '江西省',
        provinceCode: '360000',
        cityCode: '',
        district: '',
        districtCode: '',
        lat: 28.682,
        lng: 115.858,
        categoryId: 1,
        codeInfoId: 0,
      }

      const response = await addBaseInfo(addData)
      if (response.code === '200') {
        ElMessage.success('添加成功')
        dialogVisible.value = false
        loadData() // 重新加载数据
      } else {
        ElMessage.error('添加失败')
      }
    }
  } catch (error) {
    console.error('保存基地错误:', error)
    ElMessage.error('保存失败，请重试')
  }
}

const handlePageConfig = (base: PracticeBase) => {
  ElMessage.info(`正在进入 "${base.name}" 的页面配置编辑器`)
  router.push(`/lowcode-editor/${base.id}`)
}

const filteredData = computed(() => {
  if (!searchQuery.value) return tableData.value
  return tableData.value.filter(
    (base) =>
      base.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      base.city.toLowerCase().includes(searchQuery.value.toLowerCase()),
  )
})

onMounted(async () => {
  // 路由守卫已经处理了权限检查和用户信息初始化
  loadData()
})
</script>

<template>
  <div class="admin-container">
    <!-- Header -->
    <div class="admin-header">
      <div class="header-left">
        <!-- <el-button @click="goBack" circle>
          <el-icon>
            <ArrowLeft />
          </el-icon>
        </el-button> -->
        <h1>实践基地管理</h1>
      </div>
      <div class="header-right">
        <el-dropdown trigger="hover">
          <div class="user-info">
            <el-avatar :size="32" :icon="User" />
            <span class="username">{{ userStore.userName }}</span>
            <!-- <span class="username">{{ userStore.userName }}</span> -->
            <!-- <el-icon class="dropdown-icon">
              <ArrowLeft />
            </el-icon> -->
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item disabled>
                <div class="user-detail">
                  <div class="user-name">{{ userStore.userName }}</div>
                  <div class="user-phone">{{ userStore.userPhone }}</div>
                </div>
              </el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <!-- <el-icon>
                  <ArrowLeft />
                </el-icon> -->
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- Toolbar -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input v-model="searchQuery" placeholder="搜索基地名称或城市" :prefix-icon="Search" style="width: 300px" />
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon>
            <Plus />
          </el-icon>
          添加基地
        </el-button>
      </div>
    </div>

    <!-- Table -->
    <div class="table-container">
      <el-table :data="filteredData" :loading="loading" stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="基地名称" min-width="200" />
        <el-table-column prop="city" label="城市" width="100" />
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="address" label="地址" min-width="200" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="rating" label="评分" width="80">
          <template #default="{ row }">
            <el-rate v-model="row.rating" disabled text-color="#ff9900" score-template="{value}" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button @click="handleEdit(row)" type="primary" size="small">
              <el-icon>
                <Edit />
              </el-icon>
            </el-button>
            <el-button @click="handlePageConfig(row)" type="success" size="small" title="页面配置">
              <el-icon>
                <Setting />
              </el-icon>
            </el-button>
            <el-button @click="handleDelete(row)" type="danger" size="small">
              <el-icon>
                <Delete />
              </el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style scoped>
.admin-container {
  min-height: 100vh;
  background: #f5f7fa;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  font-size: 12px;
  color: #909399;
  transition: transform 0.3s;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}

.user-detail {
  padding: 4px 0;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.user-phone {
  font-size: 12px;
  color: #909399;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #ebeef5;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 12px;
}

.table-container {
  padding: 24px;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}

.upload-demo {
  width: 100%;
}

.image-display {
  width: 100%;
  min-height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fafafa;
  transition: border-color 0.3s;
}

.image-display:hover {
  border-color: #409eff;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: filter 0.3s ease;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.image-container:hover img {
  filter: brightness(0.7);
}

.preview-container {
  text-align: center;
}

@media (max-width: 768px) {
  .admin-header {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }

  .table-container {
    padding: 12px;
  }
}
</style>
