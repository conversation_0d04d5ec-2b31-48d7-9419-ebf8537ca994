{"name": "jxnu-admin", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "dev:test": "vite --mode test", "build": "vite build", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify/vue": "^5.0.0", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "element-plus": "^2.10.3", "pinia": "^3.0.3", "uuid": "^11.1.0", "vue": "^3.5.18", "vue-draggable-next": "^2.2.1", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@prettier/plugin-oxc": "^0.0.4", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-oxlint": "~1.8.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "oxlint": "~1.8.0", "prettier": "3.6.2", "terser": "^5.43.1", "typescript": "~5.8.0", "unplugin-auto-import": "^20.0.0", "unplugin-vue-components": "^29.0.0", "vite": "^6.0.0", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}