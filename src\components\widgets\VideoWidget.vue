<script setup lang="ts">
import { computed, ref } from 'vue'
import { Icon } from '@iconify/vue'
import { ElMessage } from 'element-plus'
import { uploadImage } from '@/api/user'

// 定义组件属性
interface Props {
  videoUrl?: string
  videoStyle?: 'default' | 'rounded' | 'shadow'
  videoSize?: 'small' | 'medium' | 'large' | 'full'
  videoAlign?: 'left' | 'center' | 'right'
  autoplay?: boolean
  controls?: boolean
  width?: number
  height?: number
  backgroundColor?: string
}

// 定义组件事件
interface Emits {
  (e: 'update:videoUrl', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  videoUrl: '',
  videoStyle: 'default',
  videoSize: 'medium',
  videoAlign: 'center',
  autoplay: false,
  controls: true,
  width: undefined, // 全宽
  height: undefined, // 高度自适应
  backgroundColor: '#f8f9fa',
})

const emit = defineEmits<Emits>()

// 状态管理
const videoLoaded = ref(false)
const videoError = ref(false)
const isUploading = ref(false)

// 尺寸配置（保留用于属性面板选择）
// const sizeConfig = {
//   small: { width: '300px', height: '200px' },
//   medium: { width: '500px', height: '300px' },
//   large: { width: '700px', height: '400px' },
//   full: { width: '100%', height: '400px' },
// }

// 样式配置
const styleConfig = {
  default: {
    borderRadius: '0px',
    boxShadow: 'none',
  },
  rounded: {
    borderRadius: '12px',
    boxShadow: 'none',
  },
  shadow: {
    borderRadius: '12px',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
  },
}



// 计算样式
const widgetStyle = computed(() => {
  const style = styleConfig[props.videoStyle] || styleConfig.default

  return {
    // 完全填充外层容器，不使用内部尺寸配置
    width: props.width ? `${props.width}px` : '100%',
    height: props.height ? `${props.height}px` : '100%',
    ...style,
    boxSizing: 'border-box' as const,
    position: 'relative' as const,
    overflow: 'hidden' as const,
    backgroundColor: props.backgroundColor || '#f8f9fa',
    // 根据对齐方式设置margin
    marginLeft: props.videoAlign === 'left' ? '0' : props.videoAlign === 'right' ? 'auto' : 'auto',
    marginRight: props.videoAlign === 'right' ? '0' : props.videoAlign === 'left' ? 'auto' : 'auto',
  } as const
})

// 视频事件处理
const handleVideoLoad = () => {
  videoLoaded.value = true
  videoError.value = false
}

const handleVideoError = () => {
  videoError.value = true
  videoLoaded.value = false
}

// 文件上传处理
const handleFileUpload = async (event: Event) => {
  const input = event.target as HTMLInputElement
  const file = input.files?.[0]

  if (file) {
    isUploading.value = true

    // 检查文件类型
    if (!file.type.startsWith('video/')) {
      ElMessage.error('请选择视频文件')
      isUploading.value = false
      return
    }

    // 检查文件大小 (100MB限制)
    if (file.size > 100 * 1024 * 1024) {
      ElMessage.error('视频文件大小不能超过100MB')
      isUploading.value = false
      return
    }

    try {
      // 调用上传接口
      const response = await uploadImage({ file })

      if (response.code === '200') {
        const videoUrl = response.data
        emit('update:videoUrl', videoUrl)
        ElMessage.success('视频上传成功')
      } else {
        ElMessage.error(response.msg || '上传失败')
      }
    } catch (error) {
      console.error('视频上传错误:', error)
      ElMessage.error('上传失败，请检查网络连接')
    } finally {
      isUploading.value = false
    }
  }
}

// 触发文件选择
const triggerFileSelect = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'video/*'
  input.onchange = handleFileUpload
  input.click()
}
</script>

<template>
  <div class="video-widget" :style="widgetStyle" @dblclick="triggerFileSelect">
    <!-- 有视频时显示视频 -->
    <div v-if="props.videoUrl && !videoError" class="video-container">
      <video :src="props.videoUrl" :autoplay="props.autoplay" :controls="props.controls" @loadeddata="handleVideoLoad"
        @error="handleVideoError" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit">
        您的浏览器不支持视频播放。
      </video>
      <div v-if="!videoLoaded" class="video-loading">
        <Icon icon="material-symbols:video-library" style="width: 32px; height: 32px; color: #c0c4cc" />
        <span>加载中...</span>
      </div>
      <!-- 双击提示遮罩 -->
      <div class="double-click-overlay">
        <div class="double-click-hint">
          <Icon icon="material-symbols:upload" style="width: 20px; height: 20px" />
          <span>双击更换视频</span>
        </div>
      </div>
    </div>

    <!-- 上传区域 -->
    <div v-else class="video-placeholder">
      <div v-if="isUploading" class="upload-loading">
        <Icon icon="material-symbols:upload" style="width: 32px; height: 32px; color: #409eff" />
        <span>上传中...</span>
      </div>
      <div v-else class="placeholder-content">
        <Icon icon="material-symbols:video-library" style="width: 48px; height: 48px; color: #c0c4cc" />
        <div class="placeholder-text">双击上传视频</div>
        <div class="placeholder-desc">支持 MP4、WebM、OGG 格式，最大 100MB</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.video-widget {
  line-height: 0;
  transition: all 0.3s ease;
}

.video-widget:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-container:hover .double-click-overlay {
  opacity: 1;
}

.video-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #c0c4cc;
  font-size: 14px;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(248, 249, 250, 0.5);
  border-radius: inherit;
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-placeholder:hover {
  background: rgba(240, 249, 255, 0.8);
}

.placeholder-content {
  text-align: center;
  color: #909399;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.placeholder-text {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.placeholder-desc {
  font-size: 12px;
  color: #909399;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-size: 14px;
}

/* 双击提示遮罩样式 */
.double-click-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  pointer-events: none;
}

.double-click-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  background: rgba(64, 158, 255, 0.9);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-container {
    min-height: 150px;
  }

  .video-placeholder {
    min-height: 150px;
  }

  .placeholder-text {
    font-size: 13px;
  }

  .placeholder-desc {
    font-size: 11px;
  }
}
</style>
