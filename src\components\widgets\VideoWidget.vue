<script setup lang="ts">
import { computed, ref } from 'vue'
import { Icon } from '@iconify/vue'

// 定义组件属性
interface Props {
    videoUrl?: string
    videoType?: 'url' | 'embed' | 'vr'
    embedCode?: string
    autoplay?: boolean
    controls?: boolean
    loop?: boolean
    muted?: boolean
    preload?: 'none' | 'metadata' | 'auto'
    poster?: string
    width?: string
    height?: string
    borderRadius?: string
    border?: string
    padding?: string
    margin?: string
    backgroundColor?: string
    display?: string
    position?: string
    top?: string
    left?: string
    right?: string
    bottom?: string
    zIndex?: string
    opacity?: string
    transform?: string
    transition?: string
    cursor?: string
    userSelect?: string
    overflow?: string
    boxShadow?: string
    showUpload?: boolean
    uploadText?: string
    [key: string]: any
}

const props = withDefaults(defineProps<Props>(), {
    videoUrl: '',
    videoType: 'url',
    embedCode: '',
    autoplay: false,
    controls: true,
    loop: false,
    muted: false,
    preload: 'metadata',
    poster: '',
    width: '100%',
    height: '300px',
    borderRadius: '8px',
    border: 'none',
    padding: '0',
    margin: '0',
    backgroundColor: '#f5f5f5',
    display: 'block',
    position: 'static',
    top: 'auto',
    left: 'auto',
    right: 'auto',
    bottom: 'auto',
    zIndex: 'auto',
    opacity: '1',
    transform: 'none',
    transition: 'none',
    cursor: 'default',
    userSelect: 'auto',
    overflow: 'hidden',
    boxShadow: 'none',
    showUpload: false,
    uploadText: '点击上传视频'
})

// 状态管理
const videoLoaded = ref(false)
const videoError = ref(false)
const isUploading = ref(false)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)

// 计算样式
const widgetStyle = computed(() => {
    const style: Record<string, string> = {
        width: props.width,
        height: props.height,
        borderRadius: props.borderRadius,
        border: props.border,
        padding: props.padding,
        margin: props.margin,
        backgroundColor: props.backgroundColor,
        display: props.display,
        position: props.position,
        top: props.top,
        left: props.left,
        right: props.right,
        bottom: props.bottom,
        zIndex: props.zIndex,
        opacity: props.opacity,
        transform: props.transform,
        transition: props.transition,
        cursor: props.cursor,
        userSelect: props.userSelect,
        overflow: props.overflow,
        boxShadow: props.boxShadow
    }

    return Object.fromEntries(
        Object.entries(style).filter(([_, value]) =>
            value !== 'auto' && value !== 'normal' && value !== 'none' && value !== 'transparent'
        )
    )
})

// 视频事件处理
const handleVideoLoad = () => {
    videoLoaded.value = true
    videoError.value = false
}

const handleVideoError = () => {
    videoError.value = true
    videoLoaded.value = false
}

const handleTimeUpdate = (event: Event) => {
    const video = event.target as HTMLVideoElement
    currentTime.value = video.currentTime
    duration.value = video.duration
}

const handlePlay = () => {
    isPlaying.value = true
}

const handlePause = () => {
    isPlaying.value = false
}

// 文件上传处理
const handleFileUpload = (event: Event) => {
    const input = event.target as HTMLInputElement
    const file = input.files?.[0]

    if (file) {
        isUploading.value = true

        // 检查文件类型
        if (!file.type.startsWith('video/')) {
            alert('请选择视频文件')
            isUploading.value = false
            return
        }

        // 检查文件大小 (100MB限制)
        if (file.size > 100 * 1024 * 1024) {
            alert('视频文件大小不能超过100MB')
            isUploading.value = false
            return
        }

        // 转换为Base64或URL
        const reader = new FileReader()
        reader.onload = (e) => {
            const result = e.target?.result as string
            console.log('视频上传成功:', result)
            isUploading.value = false
        }
        reader.onerror = () => {
            alert('视频上传失败')
            isUploading.value = false
        }
        reader.readAsDataURL(file)
    }
}

// 触发文件选择
const triggerFileSelect = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'video/*'
    input.onchange = handleFileUpload
    input.click()
}

// 格式化时间
const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}
</script>

<template>
    <div class="video-widget" :style="widgetStyle">
        <!-- VR视频 -->
        <div v-if="videoType === 'vr' && videoUrl" class="video-vr">
            <div class="vr-container">
                <iframe :src="videoUrl" frameborder="0" allowfullscreen
                    style="width: 100%; height: 100%; border-radius: inherit;"></iframe>
                <div class="vr-overlay">
                    <Icon icon="material-symbols:view-in-ar" style="width: 24px; height: 24px; color: white;" />
                    <span>VR视频</span>
                </div>
            </div>
        </div>

        <!-- URL视频 -->
        <div v-else-if="videoType === 'url' && videoUrl" class="video-url">
            <video :src="videoUrl" :poster="poster" :autoplay="autoplay" :controls="controls" :loop="loop"
                :muted="muted" :preload="preload" @loadeddata="handleVideoLoad" @error="handleVideoError"
                @timeupdate="handleTimeUpdate" @play="handlePlay" @pause="handlePause"
                style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">
                您的浏览器不支持视频播放。
            </video>

            <!-- 自定义播放控制 -->
            <div v-if="!controls && videoLoaded" class="custom-controls">
                <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: `${(currentTime / duration) * 100}%` }"></div>
                </div>
                <div class="control-buttons">
                    <button @click="(event) => {
                        const target = event.target as HTMLElement;
                        const video = target.closest('video') as HTMLVideoElement;
                        if (video) {
                            isPlaying ? video.pause() : video.play();
                        }
                    }">
                        <Icon :icon="isPlaying ? 'material-symbols:pause' : 'material-symbols:play-arrow'" />
                    </button>
                    <span class="time-display">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
                </div>
            </div>
        </div>

        <!-- 嵌入视频 -->
        <div v-else-if="videoType === 'embed' && embedCode" class="video-embed" v-html="embedCode">
        </div>

        <!-- 上传区域 -->
        <div v-else-if="showUpload" class="upload-area" @click="triggerFileSelect">
            <div v-if="isUploading" class="upload-loading">
                <Icon icon="material-symbols:upload" style="width: 32px; height: 32px; color: #409eff;" />
                <span>上传中...</span>
            </div>
            <div v-else class="upload-content">
                <Icon icon="material-symbols:video-library" style="width: 48px; height: 48px; color: #c0c4cc;" />
                <span class="upload-text">{{ uploadText }}</span>
                <span class="upload-hint">支持 MP4、WebM、OGG 格式，最大 100MB</span>
            </div>
        </div>

        <!-- 占位符 -->
        <div v-else class="video-placeholder">
            <div class="placeholder-content">
                <Icon icon="material-symbols:video-library" style="width: 48px; height: 48px; color: #c0c4cc;" />
                <div class="placeholder-text">视频组件</div>
                <div class="placeholder-desc">请配置视频地址或嵌入代码</div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.video-widget {
    box-sizing: border-box;
    position: relative;
}

.video-vr {
    width: 100%;
    height: 100%;
    position: relative;
}

.vr-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.vr-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
}

.video-url {
    position: relative;
    width: 100%;
    height: 100%;
}

.custom-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px 10px 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-url:hover .custom-controls {
    opacity: 1;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin-bottom: 10px;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: #409eff;
    border-radius: 2px;
    transition: width 0.1s ease;
}

.control-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.control-buttons button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.control-buttons button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.time-display {
    color: white;
    font-size: 12px;
    font-weight: 500;
}

.video-embed {
    width: 100%;
    height: 100%;
}

.video-embed iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: inherit;
}

.upload-area {
    width: 100%;
    height: 100%;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #e4e7ed;
    border-radius: 8px;
    background: #fafbfc;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #409eff;
    background: #f0f9ff;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
}

.upload-text {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

.upload-hint {
    font-size: 12px;
    color: #909399;
}

.upload-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #409eff;
    font-size: 14px;
}

.video-placeholder {
    width: 100%;
    height: 100%;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border: 2px dashed #ddd;
    border-radius: inherit;
}

.placeholder-content {
    text-align: center;
    color: #999;
}

.placeholder-text {
    font-size: 16px;
    font-weight: 500;
    margin: 16px 0 8px 0;
}

.placeholder-desc {
    font-size: 14px;
    color: #ccc;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-area {
        min-height: 150px;
    }

    .upload-text {
        font-size: 13px;
    }

    .upload-hint {
        font-size: 11px;
    }

    .custom-controls {
        padding: 15px 8px 8px;
    }
}
</style>