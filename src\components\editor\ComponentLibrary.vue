<script setup lang="ts">
import { Icon } from '@iconify/vue'

// 组件类型定义
interface ComponentItem {
  id: string
  name: string
  icon: string
  description: string
}

interface ComponentCategory {
  category: string
  components: ComponentItem[]
}

// 定义组件属性
interface Props {
  componentLibrary: ComponentCategory[]
  previewMode: boolean
  existingBusinessComponents?: string[]
}

// 定义组件事件
interface Emits {
  (e: 'componentDrag', component: ComponentItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 检查组件是否已存在
const isComponentDisabled = (component: ComponentItem, category: string) => {
  return (
    category === '业务组件' &&
    props.existingBusinessComponents &&
    props.existingBusinessComponents.includes(component.id)
  )
}

// 组件拖拽处理
const handleComponentDrag = (event: DragEvent, component: ComponentItem, category: string) => {
  if (props.previewMode) return

  // 如果是已存在的业务组件，阻止拖拽
  if (isComponentDisabled(component, category)) {
    event.preventDefault()
    return
  }

  // 设置拖拽数据
  event.dataTransfer!.setData('application/json', JSON.stringify(component))
  event.dataTransfer!.effectAllowed = 'copy'

  emit('componentDrag', component)
}
</script>

<template>
  <div class="component-library">
    <div class="library-header">
      <h4>
        <Icon icon="material-symbols:widgets" style="width: 18px; height: 18px" />
        组件库
      </h4>
    </div>

    <div class="library-content">
      <div v-for="category in componentLibrary" :key="category.category" class="component-category">
        <div class="category-title">
          {{ category.category }}
        </div>

        <div class="component-grid">
          <div v-for="component in category.components" :key="component.id" class="component-item" :class="{
            'business-component':
              category.category === '业务组件' &&
              !isComponentDisabled(component, category.category),
            'basic-component': category.category === '基础组件',
            'disabled-component': isComponentDisabled(component, category.category),
          }" :draggable="!previewMode && !isComponentDisabled(component, category.category)"
            @dragstart="handleComponentDrag($event, component, category.category)" :title="isComponentDisabled(component, category.category)
              ? `${component.description}（已添加，不可重复添加）`
              : component.description
              ">
            <div class="component-icon">
              <Icon :icon="component.icon" style="width: 24px; height: 24px" />
            </div>
            <div class="component-name">{{ component.name }}</div>
            <div v-if="
              category.category === '业务组件' &&
              !isComponentDisabled(component, category.category)
            " class="component-badge">
              推荐
            </div>
            <div v-if="isComponentDisabled(component, category.category)" class="component-badge disabled-badge">
              已添加
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.component-library {
  width: 280px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.library-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
}

.library-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.library-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.component-category {
  margin-bottom: 20px;
}

.category-title {
  font-size: 12px;
  color: #909399;
  font-weight: 600;
  margin-bottom: 8px;
  padding: 0 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: hidden;
}

.component-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.component-item:hover::before {
  left: 100%;
}

.component-item:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f3ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.component-item:active {
  cursor: grabbing;
  transform: translateY(0) scale(0.98);
}

.component-icon {
  margin-bottom: 6px;
  color: #606266;
  transition: color 0.3s ease;
}

.component-item:hover .component-icon {
  color: #409eff;
}

.component-name {
  font-size: 12px;
  color: #303133;
  text-align: center;
  font-weight: 500;
}

.component-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: #67c23a;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

/* 业务组件特殊样式 */
.business-component {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.business-component:hover {
  border-color: #67c23a;
  background: linear-gradient(135deg, #e6f7ff 0%, #d4f1ff 100%);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
}

.business-component .component-icon {
  color: #67c23a;
}

.business-component:hover .component-icon {
  color: #67c23a;
}

/* 基础组件样式 */
.basic-component {
  border-color: #e4e7ed;
  background: white;
}

.basic-component:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f3ff 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

/* 业务组件样式 */
.business-component {
  border: 2px solid #67c23a;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.business-component:hover {
  border-color: #67c23a;
  background: linear-gradient(135deg, #e6f7ff 0%, #d1f2ff 100%);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
}

.business-component .component-icon {
  color: #67c23a;
}

/* 基础组件样式 */
.basic-component {
  border: 1px solid #409eff;
}

.basic-component:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f3ff 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

/* 布局工具样式 */
.layout-component {
  border: 1px solid #e6a23c;
}

.layout-component:hover {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #f8f9fa 0%, #fff7e6 100%);
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.15);
}

/* 高级组件样式 */
.advanced-component {
  border: 1px solid #909399;
  opacity: 0.8;
}

.advanced-component:hover {
  border-color: #909399;
  background: linear-gradient(135deg, #f8f9fa 0%, #f5f5f5 100%);
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.15);
  opacity: 1;
}

/* 禁用组件样式 */
.disabled-component {
  border: 1px solid #e4e7ed !important;
  background: #f5f7fa !important;
  opacity: 0.6;
  cursor: not-allowed !important;
  pointer-events: none;
}

.disabled-component:hover {
  border-color: #e4e7ed !important;
  background: #f5f7fa !important;
  transform: none !important;
  box-shadow: none !important;
}

.disabled-component .component-icon {
  color: #c0c4cc !important;
}

.disabled-component .component-name {
  color: #c0c4cc !important;
}

.disabled-badge {
  background: #909399 !important;
  color: white !important;
}

/* 滚动条样式 */
.library-content::-webkit-scrollbar {
  width: 6px;
}

.library-content::-webkit-scrollbar-track {
  background: #f1f3f4;
}

.library-content::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.library-content::-webkit-scrollbar-thumb:hover {
  background: #909399;
}
</style>
