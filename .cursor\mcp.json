{"mcpServers": {"github.com/github/github-mcp-server": {"autoApprove": ["get_user", "create_or_update_file"], "disabled": false, "timeout": 60, "command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "transportType": "stdio"}, "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "docker", "args": ["run", "-i", "--rm", "--mount", "type=bind,src=c:/Users/<USER>/Desktop/webProjects/NotionNext,dst=/projects/NotionNext", "--mount", "type=bind,src=c:/Users/<USER>/Desktop,dst=/projects/Desktop", "mcp/filesystem", "/projects"], "transportType": "stdio"}, "time": {"autoApprove": ["convert_time"], "timeout": 60, "command": "docker", "args": ["run", "-i", "--rm", "mcp/time"], "transportType": "stdio"}, "browser-tools-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp@latest"], "autoApprove": ["getNetworkLogs", "getSelectedElement", "getConsoleLogs", "getConsoleErrors", "takeScreenshot", "runPerformanceAudit", "runAuditMode", "getNetworkErrors", "runNextJSAudit", "runDebuggerMode", "runAccessibilityAudit"]}, "sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-sequential-thinking"]}, "firecrawl-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-c9fd793747a048eca74a87d91edce0f5"}, "autoApprove": ["firecrawl_scrape", "firecrawl_extract", "firecrawl_search"]}, "notionApi": {"command": "cmd", "args": ["/c", "npx", "-y", "@notionhq/notion-mcp-server"], "env": {"OPENAPI_MCP_HEADERS": "{\"Authorization\": \"Bearer 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\", \"Notion-Version\": \"2022-06-28\" }"}, "autoApprove": ["API-post-database-query", "API-post-page", "API-patch-block-children", "API-patch-page"]}, "brave-search": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAdKj4wPsLV_MgowENQijQrvb-j_yk"}}}}